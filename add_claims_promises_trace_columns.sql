-- Add trace_json JSONB columns to claims and promises analysis tables
-- This script adds trace data support to claims and promises analysis tables

\echo 'Adding trace_json columns to claims and promises tables...'

-- Add trace_json column to ana_claims table
ALTER TABLE ana_claims
    ADD COLUMN IF NOT EXISTS trace_json JSONB;
\echo 'Added trace_json column to ana_claims'

-- Add trace_json column to ana_promises table  
ALTER TABLE ana_promises
    ADD COLUMN IF NOT EXISTS trace_json JSONB;
\echo 'Added trace_json column to ana_promises'

-- Create GIN indexes for efficient JSONB queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ana_claims_trace_json ON ana_claims USING GIN (trace_json);
\echo 'Created GIN index on ana_claims.trace_json'

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ana_promises_trace_json ON ana_promises USING GIN (trace_json);
\echo 'Created GIN index on ana_promises.trace_json'

-- Verify the changes
\echo 'Verifying trace_json columns were added...'
SELECT table_name,
       column_name,
       data_type,
       is_nullable
FROM information_schema.columns
WHERE table_schema = 'public'
  AND column_name = 'trace_json'
  AND table_name IN ('ana_claims', 'ana_promises')
ORDER BY table_name;

\echo 'Claims and promises trace column migration completed!'
