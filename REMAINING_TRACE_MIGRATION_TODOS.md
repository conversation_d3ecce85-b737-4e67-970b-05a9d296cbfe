# Remaining Trace System Migration TODOs

## Overview
The core effect->flag->merge->xfer pipeline has been successfully converted to the new trace system. However, several additional analysis modules still need to be migrated to use the new trace system with JSONB trace_data fields.

## Completed ✅
- ✅ Core effect analysis pipeline (effect_models.py, effect_flags_helpers.py)
- ✅ Effect flag creation and merging
- ✅ Flag utilities and main pipeline (flag_utils.py, flag_main.py)
- ✅ Customer UI trace modal and admin trace button
- ✅ New TraceCollector system fully operational
- ✅ Database schema with trace_json columns (ana_effects, ana_effect_flags, xfer_flags)

## Remaining Work 🔄

### 1. Selective Highlighting Analysis Modules
**Priority: Medium**

Files to update:
- `/backoffice/src/eko/analysis_v2/selective_highlighting/detection_pgvector.py`
- `/backoffice/src/eko/analysis_v2/selective_highlighting/merging.py`

**Required Changes:**
- Remove TraceabilityTracker parameters from function signatures
- Remove all `tracker.record_stat()` calls
- Add trace_json JSONB column to database tables:
  - `ana_cherry` table
  - `ana_flooding` table
- Update DAOs to support trace data storage/retrieval
- Integrate TraceCollector for comprehensive tracing

### 2. Claims and Promises Analysis Modules  
**Priority: Medium**

Files to update:
- `/backoffice/src/eko/analysis_v2/heart/trust_and_reliability/merging.py`

**Required Changes:**
- Remove TraceabilityTracker parameters from clustering/merging functions
- Remove all `tracker.record_stat()` calls  
- Add trace_json JSONB column to database tables:
  - Claims analysis tables
  - Promises analysis tables
- Update corresponding DAOs to support trace data
- Integrate TraceCollector for pipeline tracing

### 3. CLI Commands Update
**Priority: Medium**

**Required Changes:**
- Update CLI commands that still pass tracker parameters
- Remove any remaining tracker initialization in CLI modules
- Ensure all CLI commands use new trace system

### 4. Database Schema Updates
**Priority: Medium**

**SQL Changes Needed:**
```sql
-- Selective Highlighting Tables
ALTER TABLE ana_cherry
  ADD COLUMN trace_json JSONB;
ALTER TABLE ana_flooding ADD COLUMN trace_json JSONB;

-- Claims/Promises Tables (identify table names first)
-- ALTER TABLE [claims_table] ADD COLUMN trace_json JSONB;
-- ALTER TABLE [promises_table] ADD COLUMN trace_json JSONB;

-- Add GIN indexes for performance
CREATE INDEX idx_ana_cherry_trace ON ana_cherry USING GIN (trace_json);
CREATE INDEX idx_ana_flooding_trace ON ana_flooding USING GIN (trace_json);
```

### 5. DAO Updates
**Priority: Medium**

**Required Changes:**
- Update selective highlighting DAOs to store/retrieve trace data
- Update claims/promises DAOs to handle trace_json fields
- Ensure proper JSON serialization (json.dumps) for PostgreSQL JSONB

### 6. Integration Testing
**Priority: Low**

**Required Testing:**
- Test selective highlighting analysis with new trace system
- Test claims/promises analysis with trace data
- Verify trace data flows correctly through all pipelines
- Ensure customer UI can display trace data for all analysis types

### 7. Final Cleanup
**Priority: Low**

**After all modules converted:**
- Remove old tracking infrastructure files:
  - `pipeline_tracker.py`
  - `pipeline_tracker_extended.py` 
  - `dash/callbacks_traceability.py`
- Run the SQL script to drop old trk_* tables: `drop_old_tracking_tables.sql`
- Update documentation to reflect new trace system

## Implementation Strategy

### Phase 1: Selective Highlighting (2-3 days)
1. Add trace_json columns to selective highlighting tables
2. Update detection_pgvector.py and merging.py
3. Update selective highlighting DAOs
4. Test selective highlighting CLI commands

### Phase 2: Claims/Promises (2-3 days)  
1. Identify claims/promises database tables
2. Add trace_json columns to those tables
3. Update heart/trust_and_reliability/merging.py
4. Update corresponding DAOs
5. Test claims/promises CLI commands

### Phase 3: Final Integration (1-2 days)
1. Update remaining CLI commands
2. Integration testing across all modules
3. Remove old tracking infrastructure
4. Documentation updates

## Benefits After Full Migration

- **Unified Tracing**: All analysis modules use consistent trace system
- **Complete Observability**: Full pipeline visibility from statements to customer UI
- **Performance**: Reduced database complexity with fewer tracking tables
- **Maintainability**: Single trace system instead of dual tracking approach
- **Customer Value**: Comprehensive trace data available in customer UI for all analysis types

## Success Criteria

- ✅ Zero references to TraceabilityTracker/PipelineTracker in active code
- ✅ All analysis modules store trace data in trace_json JSONB columns
- ✅ Customer UI can display trace data for all analysis types
- ✅ All CLI commands work without tracker parameters
- ✅ Old trk_* tables successfully dropped
- ✅ All tests pass with new trace system
