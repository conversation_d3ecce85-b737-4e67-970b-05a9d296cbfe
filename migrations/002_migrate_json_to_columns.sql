-- Migration: Extract large text fields from JSON to columns
-- Purpose: Move existing data from JSON model field to new dedicated columns

-- Migrate xfer_flags data
UPDATE xfer_flags
SET flag_summary    = (model ->> 'flag_summary')::text,
    flag_analysis   = (model ->> 'flag_analysis')::text,
    flag_statements = CASE
                          WHEN jsonb_typeof(model -> 'flag_statements') = 'array'
                              THEN ARRAY(SELECT jsonb_array_elements(model -> 'flag_statements'))
                          ELSE ARRAY []::jsonb[]
        END
WHERE flag_summary IS NULL
  AND model IS NOT NULL;

-- Migrate xfer_claims data
UPDATE xfer_claims
SET summary        = (model ->> 'summary')::text,
    conclusion     = (model ->> 'conclusion')::text,
    statement_text = (model ->> 'statement_text')::text,
    context        = (model ->> 'context')::text
WHERE summary IS NULL
  AND model IS NOT NULL;

-- Migrate xfer_promises data  
UPDATE xfer_promises
SET summary        = (model ->> 'summary')::text,
    conclusion     = (model ->> 'conclusion')::text,
    statement_text = (model ->> 'statement_text')::text,
    evidence       = (model ->> 'evidence')::text
WHERE summary IS NULL
  AND model IS NOT NULL;

-- Migrate xfer_selective data
UPDATE xfer_selective
SET analysis    = (model ->> 'analysis')::text,
    explanation = (model ->> 'explanation')::text,
    reason      = (model ->> 'reason')::text
WHERE analysis IS NULL
  AND model IS NOT NULL;

-- Create minimal JSON models without the large text fields for better performance
-- This removes the large text fields from the JSON while preserving other data

-- Clean up xfer_flags JSON
UPDATE xfer_flags
SET model = model - 'flag_summary' - 'flag_analysis' - 'flag_statements'
WHERE flag_summary IS NOT NULL
  AND model ? 'flag_summary';

-- Clean up xfer_claims JSON
UPDATE xfer_claims
SET model = model - 'summary' - 'conclusion' - 'statement_text' - 'context'
WHERE summary IS NOT NULL
  AND model ? 'summary';

-- Clean up xfer_promises JSON
UPDATE xfer_promises
SET model = model - 'summary' - 'conclusion' - 'statement_text' - 'evidence'
WHERE summary IS NOT NULL
  AND model ? 'summary';

-- Clean up xfer_selective JSON
UPDATE xfer_selective
SET model = model - 'analysis' - 'explanation' - 'reason'
WHERE analysis IS NOT NULL
  AND model ? 'analysis';

-- Add constraints to ensure data integrity
ALTER TABLE xfer_flags
    ADD CONSTRAINT check_flag_summary_not_empty CHECK (flag_summary IS NULL OR length(flag_summary) > 0);

ALTER TABLE xfer_claims
    ADD CONSTRAINT check_claim_summary_not_empty CHECK (summary IS NULL OR length(summary) > 0);

ALTER TABLE xfer_promises
    ADD CONSTRAINT check_promise_summary_not_empty CHECK (summary IS NULL OR length(summary) > 0);

ALTER TABLE xfer_selective
    ADD CONSTRAINT check_selective_analysis_not_empty CHECK (analysis IS NULL OR length(analysis) > 0);

-- Migrate xfer_entities data from model JSON to dedicated columns
UPDATE xfer_entities
SET entity_description        = COALESCE(
        model -> 'base_entities' -> 0 ->> 'description',
        model ->> 'description',
        ''
                                ),
    entity_base_entities_json = CASE
                                    WHEN model -> 'base_entities' IS NOT NULL
                                        THEN model -> 'base_entities'::text
                                    ELSE NULL
        END
WHERE entity_description IS NULL
  AND model IS NOT NULL;

-- Clean up base_entities and description from model JSON if they were extracted
UPDATE xfer_entities
SET model = model - 'description' - 'base_entities'
WHERE entity_description IS NOT NULL
  AND (model ? 'description' OR model ? 'base_entities');

-- Migrate xfer_gw_single_doc data from analysis_json to dedicated columns
UPDATE xfer_gw_single_doc
SET analysis_summary         = analysis_json ->> 'summary',
    analysis_conclusion      = analysis_json ->> 'conclusion',
    analysis_recommendations = analysis_json ->> 'recommendations'
WHERE analysis_summary IS NULL
  AND analysis_json IS NOT NULL;

-- Clean up migrated fields from analysis_json
UPDATE xfer_gw_single_doc
SET analysis_json = analysis_json - 'summary' - 'conclusion' - 'recommendations'
WHERE analysis_summary IS NOT NULL
  AND (analysis_json ? 'summary' OR analysis_json ? 'conclusion' OR analysis_json ? 'recommendations');

-- Add constraints for new tables
ALTER TABLE xfer_entities
    ADD CONSTRAINT check_entity_description_not_empty CHECK (entity_description IS NULL OR length(entity_description) > 0);

ALTER TABLE xfer_gw_single_doc
    ADD CONSTRAINT check_analysis_summary_not_empty CHECK (analysis_summary IS NULL OR length(analysis_summary) > 0);

-- Report migration statistics
DO
$$
    DECLARE
        flags_migrated      INTEGER;
        claims_migrated     INTEGER;
        promises_migrated   INTEGER;
        selective_migrated  INTEGER;
        entities_migrated   INTEGER;
        single_doc_migrated INTEGER;
    BEGIN
        SELECT COUNT(*) INTO flags_migrated FROM xfer_flags WHERE flag_summary IS NOT NULL;
        SELECT COUNT(*) INTO claims_migrated FROM xfer_claims WHERE summary IS NOT NULL;
        SELECT COUNT(*) INTO promises_migrated FROM xfer_promises WHERE summary IS NOT NULL;
        SELECT COUNT(*) INTO selective_migrated FROM xfer_selective WHERE analysis IS NOT NULL;
        SELECT COUNT(*) INTO entities_migrated FROM xfer_entities WHERE entity_description IS NOT NULL;
        SELECT COUNT(*) INTO single_doc_migrated FROM xfer_gw_single_doc WHERE analysis_summary IS NOT NULL;

        RAISE NOTICE 'Migration completed:';
        RAISE NOTICE 'xfer_flags: % records migrated', flags_migrated;
        RAISE NOTICE 'xfer_claims: % records migrated', claims_migrated;
        RAISE NOTICE 'xfer_promises: % records migrated', promises_migrated;
        RAISE NOTICE 'xfer_selective: % records migrated', selective_migrated;
        RAISE NOTICE 'xfer_entities: % records migrated', entities_migrated;
        RAISE NOTICE 'xfer_gw_single_doc: % records migrated', single_doc_migrated;
    END
$$;
