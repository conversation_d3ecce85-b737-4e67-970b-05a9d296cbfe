-- Migration: Add text columns to xfer_ tables to extract large text fields from JSON
-- Purpose: Improve performance by moving frequently accessed large text fields to dedicated columns

-- xfer_flags table enhancements
ALTER TABLE xfer_flags
    ADD COLUMN IF NOT EXISTS flag_summary    TEXT,
    ADD COLUMN IF NOT EXISTS flag_analysis   TEXT,
    ADD COLUMN IF NOT EXISTS flag_statements JSONB[];

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_xfer_flags_flag_summary ON xfer_flags USING GIN (to_tsvector('english', flag_summary));
CREATE INDEX IF NOT EXISTS idx_xfer_flags_flag_analysis ON xfer_flags USING GIN (to_tsvector('english', flag_analysis));

-- xfer_claims table enhancements  
ALTER TABLE xfer_claims
    ADD COLUMN IF NOT EXISTS summary        TEXT,
    ADD COLUMN IF NOT EXISTS conclusion     TEXT,
    ADD COLUMN IF NOT EXISTS statement_text TEXT,
    ADD COLUMN IF NOT EXISTS context        TEXT;

-- Create indexes for claims
CREATE INDEX IF NOT EXISTS idx_xfer_claims_summary ON xfer_claims USING GIN (to_tsvector('english', summary));
CREATE INDEX IF NOT EXISTS idx_xfer_claims_statement_text ON xfer_claims USING GIN (to_tsvector('english', statement_text));

-- xfer_promises table enhancements
ALTER TABLE xfer_promises
    ADD COLUMN IF NOT EXISTS summary        TEXT,
    ADD COLUMN IF NOT EXISTS conclusion     TEXT,
    ADD COLUMN IF NOT EXISTS statement_text TEXT,
    ADD COLUMN IF NOT EXISTS evidence       TEXT;

-- Create indexes for promises
CREATE INDEX IF NOT EXISTS idx_xfer_promises_summary ON xfer_promises USING GIN (to_tsvector('english', summary));
CREATE INDEX IF NOT EXISTS idx_xfer_promises_statement_text ON xfer_promises USING GIN (to_tsvector('english', statement_text));

-- xfer_selective table enhancements
ALTER TABLE xfer_selective
    ADD COLUMN IF NOT EXISTS analysis    TEXT,
    ADD COLUMN IF NOT EXISTS explanation TEXT,
    ADD COLUMN IF NOT EXISTS reason      TEXT;

-- Create indexes for selective highlighting
CREATE INDEX IF NOT EXISTS idx_xfer_selective_analysis ON xfer_selective USING GIN (to_tsvector('english', analysis));

-- Add comments to document the migration
COMMENT ON COLUMN xfer_flags.flag_summary IS 'Extracted from model JSON for performance - flag summary text';
COMMENT ON COLUMN xfer_flags.flag_analysis IS 'Extracted from model JSON for performance - flag analysis text';
COMMENT ON COLUMN xfer_flags.flag_statements IS 'Extracted from model JSON for performance - array of statement objects as JSONB';

COMMENT ON COLUMN xfer_claims.summary IS 'Extracted from model JSON for performance - claim summary';
COMMENT ON COLUMN xfer_claims.conclusion IS 'Extracted from model JSON for performance - claim conclusion';
COMMENT ON COLUMN xfer_claims.statement_text IS 'Extracted from model JSON for performance - statement text';
COMMENT ON COLUMN xfer_claims.context IS 'Extracted from model JSON for performance - claim context';

COMMENT ON COLUMN xfer_promises.summary IS 'Extracted from model JSON for performance - promise summary';
COMMENT ON COLUMN xfer_promises.conclusion IS 'Extracted from model JSON for performance - promise conclusion';
COMMENT ON COLUMN xfer_promises.statement_text IS 'Extracted from model JSON for performance - statement text';
COMMENT ON COLUMN xfer_promises.evidence IS 'Extracted from model JSON for performance - promise evidence';

COMMENT ON COLUMN xfer_selective.analysis IS 'Extracted from model JSON for performance - selective highlighting analysis';
COMMENT ON COLUMN xfer_selective.explanation IS 'Extracted from model JSON for performance - selective highlighting explanation';
COMMENT ON COLUMN xfer_selective.reason IS 'Extracted from model JSON for performance - selective highlighting reason';

-- xfer_entities table enhancements for large model data
ALTER TABLE xfer_entities
    ADD COLUMN IF NOT EXISTS entity_description        TEXT,
    ADD COLUMN IF NOT EXISTS entity_base_entities_json TEXT;

-- Create indexes for entities
CREATE INDEX IF NOT EXISTS idx_xfer_entities_description ON xfer_entities USING GIN (to_tsvector('english', entity_description));

-- xfer_gw_single_doc table enhancements for large analysis data
ALTER TABLE xfer_gw_single_doc
    ADD COLUMN IF NOT EXISTS analysis_summary         TEXT,
    ADD COLUMN IF NOT EXISTS analysis_conclusion      TEXT,
    ADD COLUMN IF NOT EXISTS analysis_recommendations TEXT;

-- Create indexes for single doc analysis
CREATE INDEX IF NOT EXISTS idx_xfer_gw_single_doc_summary ON xfer_gw_single_doc USING GIN (to_tsvector('english', analysis_summary));
CREATE INDEX IF NOT EXISTS idx_xfer_gw_single_doc_conclusion ON xfer_gw_single_doc USING GIN (to_tsvector('english', analysis_conclusion));

COMMENT ON COLUMN xfer_entities.entity_description IS 'Extracted from model JSON for performance - entity description text';
COMMENT ON COLUMN xfer_entities.entity_base_entities_json IS 'Extracted from model JSON for performance - base_entities as JSON text for large data';

COMMENT ON COLUMN xfer_gw_single_doc.analysis_summary IS 'Extracted from analysis_json for performance - analysis summary text';
COMMENT ON COLUMN xfer_gw_single_doc.analysis_conclusion IS 'Extracted from analysis_json for performance - analysis conclusion text';
COMMENT ON COLUMN xfer_gw_single_doc.analysis_recommendations IS 'Extracted from analysis_json for performance - analysis recommendations text';
