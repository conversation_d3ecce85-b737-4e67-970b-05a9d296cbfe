# EkoIntelligence Platform

EkoIntelligence is a comprehensive ESG (Environmental, Social, Governance) analysis platform that processes corporate documents, extracts sustainability statements, and provides intelligent analysis using cutting-edge NLP and LLM technologies.

## 🌟 What EkoIntelligence Does

- **Document Processing**: Automatically processes ESG reports, sustainability documents, and corporate filings
- **Statement Extraction**: Uses advanced NLP to extract key sustainability claims and commitments
- **Entity Recognition**: Identifies companies, organizations, and entities mentioned in documents
- **Greenwashing Detection**: Analyzes statements for potential greenwashing and inconsistencies
- **Claims vs Evidence Analysis**: Compares corporate claims against historical evidence and future outcomes
- **Interactive Dashboards**: Provides analytics dashboards for ESG insights and trends
- **Collaborative Reporting**: Multi-user document editing and report generation

## 🏗️ Architecture Overview
x
This is a mono-repository containing:

### Backend (Python)
- **Analytics Engine** (`/backoffice/src/`) - Core Python application for document processing and analysis
- **Database Layer** - PostgreSQL databases for analytics and customer data
- **AI/ML Pipeline** - LLM integration, NLP processing, and predictive analytics

### Frontend (React/TypeScript)
- **Customer App** (`/apps/customer/`) - Main user interface for ESG analysis and reporting
- **CMS** (`/apps/customer/`) - Content management system for administrative tasks
- **Shared Components** (`/packages/`) - Reusable UI components and utilities

### Infrastructure
- **Database Schemas** (`/db/`) - SQL migrations, views, and database configuration
- **Development Tools** - Docker support, testing infrastructure, and CI/CD pipelines

## 🚀 Quick Setup

### Prerequisites

- **Ubuntu/Debian** (recommended) or macOS/Windows with WSL
- **Node.js 18+** and **pnpm**
- **Python 3.11**
- **PostgreSQL** (for local development)
- **Docker** (optional, for containerized development)

### Automated Setup (Recommended)

For Ubuntu/Debian systems, run the automated setup script:

```bash
git clone <repository-url>
cd mono-repo
./setup-dev-env.sh
```

This script will:
- Install Python 3.11 and all system dependencies (including OpenCV, Tesseract OCR)
- Set up uv package manager and Python virtual environment
- Install Node.js 18+ and pnpm
- Install all Python dependencies (numpy, spaCy, PyTorch, etc.)
- Install frontend dependencies and build shared packages
- Download required ML models (spaCy English model)
- Install Playwright browsers for testing

### Manual Setup

If you prefer manual setup or are on a different OS:

#### 1. System Dependencies

**Ubuntu/Debian:**
```bash
sudo apt-get update
sudo apt-get install -y python3.11 python3.11-dev python3-pip git-all build-essential \
    gcc g++ make libffi-dev libssl-dev libopenblas-dev liblapack-dev pkg-config \
    libleptonica-dev tesseract-ocr libtesseract-dev tesseract-ocr-eng \
    libxml2-dev libxslt-dev libjpeg-dev zlib1g-dev libpng-dev libatlas-base-dev \
    gfortran curl wget
```

**macOS:**
```bash
brew install python@3.11 tesseract postgresql node@18
```

#### 2. Python Environment

```bash
# Install uv package manager
curl -LsSf https://astral.sh/uv/install.sh | sh
export PATH="$HOME/.local/bin:$PATH"

# Set up Python environment
cd backoffice/src
uv venv
source .venv/bin/activate
uv sync --dev
uv run python -m spacy download en_core_web_sm
uv add "psycopg[binary]" "lxml[html_clean]"
uv run playwright install
```

#### 3. Frontend Dependencies

```bash
# Install pnpm (if not already installed)
npm install -g pnpm@9.0.0

# Install dependencies and build
pnpm install
pnpm build
```

## 📁 Project Structure

```
mono-repo/
├── backoffice/                 # Python Backend
│   ├── src/
│   │   ├── eko/               # Core Python modules
│   │   │   ├── db/            # Database access layer
│   │   │   ├── llm/           # LLM integration (OpenAI, Anthropic, etc.)
│   │   │   ├── nlp/           # NLP processing utilities
│   │   │   ├── entities/      # Entity recognition and management
│   │   │   ├── analysis_v2/   # ESG analysis pipeline
│   │   │   ├── scrape/        # Web scraping and document processing
│   │   │   └── models/        # Pydantic data models
│   │   ├── cli/               # Command-line interface
│   │   ├── tests/             # Python tests
│   │   └── pyproject.toml     # Python dependencies
│   └── Dockerfile
├── apps/
│   ├── customer/              # Customer Application (Next.js)
│   │   ├── app/               # Next.js app router
│   │   ├── components/        # React components
│   │   ├── tests/             # Playwright tests
│   │   └── supabase/          # Database client configuration
│   ├── cms/                   # Content Management System (Payload CMS)
│   │   ├── src/               # Payload CMS configuration
│   │   ├── collections/       # CMS content models
│   │   └── blocks/            # Reusable content blocks
│   └── docs/                  # Documentation site
├── packages/                  # Shared Libraries
│   └── ui/                    # Shared UI components
├── db/                        # Database Files
│   ├── migrations/            # SQL migration files
│   ├── functions/             # PostgreSQL functions
│   └── views/                 # Database views
├── environments/              # Environment Configuration
│   ├── local/                 # Local development setup
│   └── prod/                  # Production configuration
└── setup-dev-env.sh          # Automated setup script
```

## 🔧 Development Workflow

### Starting Development

1. **Activate Python Environment:**
   ```bash
   cd backoffice/src
   source .venv/bin/activate
   ```

2. **Start Frontend Development:**
   ```bash
   # Start all frontend apps
   pnpm dev
   
   # Or start specific apps
   pnpm --filter customer dev
   pnpm --filter cms dev
   ```

3. **Run Python Backend:**
   ```bash
   cd backoffice/src
   uv run python src/cli.py --help
   uv run python src/api.py  # Start FastAPI server
   ```

### Common Commands

#### Python Backend
```bash
# Run CLI commands
cd backoffice/src && uv run python src/cli.py [command]

# Run self-test
cd backoffice/src && uv run python src/cli.py claude-self-test

# Run Python tests
cd backoffice/src && uv run python -m pytest src/tests/on_commit/
cd backoffice/src && uv run python -m pytest src/tests/on_push/

# Type checking
cd backoffice/src && uvx ty check [filename]

# Start analytics dashboard
cd backoffice/src && uv run python src/eko/dash/app.py
```

#### Frontend
```bash
# Build all applications
pnpm build

# Run linting and type checking
pnpm lint
tsc --noEmit

# Run Playwright tests
cd apps/customer && npx playwright test --reporter=line

# Format code
pnpm format
```

#### Database Operations
```bash
# Run queries on analytics database
./bin/run_in_db.sh "SELECT * FROM kg_entities LIMIT 10;"

# Run queries on customer database  
./bin/run_in_customer_db.sh "SELECT * FROM cus_documents LIMIT 10;"

# Apply database migrations
./bin/run_in_db.sh < db/migrations/[migration_file].sql
```

## 🗄️ Database Setup

EkoIntelligence uses two PostgreSQL databases:

### Analytics Database
- **Purpose**: Core data processing, ML models, and analysis pipelines
- **Access**: `./bin/run_in_db.sh`
- **Key Schemas**: 
  - `public` - Main entities, statements, and analysis results
  - `dash` - Dashboard metrics and pipeline tracking
- **Key Tables**: `kg_entities`, `ana_statements`, `ana_effect_flags`

### Customer Database
- **Purpose**: Customer-facing data and user management
- **Access**: `./bin/run_in_customer_db.sh`
- **Key Schemas**:
  - `public` - User accounts and documents
  - `xfer_*` tables - Synced data from analytics database
- **Integration**: Supabase for real-time features and auth

## 🧪 Testing

### Python Tests
```bash
# Unit tests (run before commits)
cd backoffice/src && uv run python -m pytest src/tests/on_commit/

# Integration tests (run on CI)
cd backoffice/src && uv run python -m pytest src/tests/on_push/

# LLM evaluation tests
cd backoffice/src && uv run python src/llm_evals/run_evals.py
```

### Frontend Tests
```bash
# Type checking (required before commits)
tsc --noEmit

# Playwright end-to-end tests
cd apps/customer && npx playwright test

# Run specific test suites
cd apps/customer && npx playwright test --grep "report"
```

## 🔑 Key Features & Components

### Document Processing Pipeline
1. **Ingestion**: PDF processing, OCR, and text extraction
2. **NLP Analysis**: Statement extraction, entity recognition
3. **LLM Integration**: Claims analysis, greenwashing detection
4. **Clustering**: Similar statement grouping and deduplication
5. **Score Calculation**: Bayesian risk scoring with fail-fast error handling

### Score Calculation System
The platform includes a sophisticated Bayesian scoring system that calculates risk scores for entities based on effect flags:

- **BayesianScoreSync**: Advanced scoring using Bayesian inference with red/green flag analysis
- **ScoreSync**: Standard scoring algorithm for basic risk assessment
- **Fail-Fast Architecture**: Score calculation failures immediately halt analysis to prevent silent errors
- **Data Synchronization**: Scores are synced to `xfer_score` table for customer application display
- **CLI Integration**: Score calculation is triggered by `ana full` and `ana selective-highlighting` commands

### ESG Analysis Features
- **Claims vs Evidence**: Automated fact-checking against historical data
- **Promise Tracking**: Monitor corporate commitments over time
- **Entity Relationship Mapping**: Company hierarchies and relationships
- **Sector Analysis**: Industry-specific ESG benchmarking
- **Risk Score Calculation**: Bayesian scoring system with fail-fast error handling

### AI/ML Technologies
- **LLM Providers**: OpenAI GPT-4, Anthropic Claude, Google Gemini
- **NLP Models**: spaCy, custom BERT-based models
- **Vector Embeddings**: Sentence transformers for semantic similarity
- **Classification**: ESG topic classification and sentiment analysis

## 🚀 Deployment

### Environment Configuration
- **Local**: `environments/local/docker-compose.yml`
- **Production**: `environments/prod/docker-compose.yml`
- **Features**: Docker Compose with PostgreSQL, Redis, and application services

### Building Docker Images
```bash
# Build Python backend
cd backoffice && docker build -t eko-backoffice .

# Build frontend applications
docker build -f apps/customer/Dockerfile -t eko-customer .
```

## 🤝 Contributing

### Code Standards
- **Python**: Follow PEP 8, use type hints, prefer Pydantic models
- **TypeScript**: Strict typing, prefer functional components
- **Testing**: Write tests for new features, maintain >80% coverage
- **Documentation**: Update README and inline docs for significant changes

### Git Workflow
1. Create feature branch: `git checkout -b feature/description`
2. Make changes and test thoroughly
3. Run all tests: Python tests + `tsc --noEmit` + Playwright tests
4. Commit with descriptive messages including issue references
5. Create pull request with detailed description

### Development Guidelines
- **Fail Fast**: Use exceptions instead of silent failures (implemented in score calculation system)
- **Type Safety**: Leverage TypeScript and Python type hints
- **Performance**: Profile code and optimize hot paths
- **Security**: Never commit secrets, use environment variables

## 📚 Additional Resources

- **Linear Issues**: Track progress and report bugs at [Linear Workspace](https://linear.app/ekointelligence)
- **API Documentation**: FastAPI docs available at `/docs` when running backend
- **Database Schema**: See `/db/` directory for complete schema documentation
- **Component Library**: Shared UI components in `/packages/ui/`

## 🔧 Troubleshooting

### Common Issues

**Python Environment Issues:**
```bash
# Reset Python environment
cd backoffice/src && rm -rf .venv && uv venv && uv sync --dev
```

**Frontend Build Failures:**
```bash
# Clear node modules and reinstall
rm -rf node_modules pnpm-lock.yaml && pnpm install
```

**Database Connection Issues:**
- Ensure PostgreSQL is running on expected ports
- Check environment variables in `.env` files
- Verify database exists and user has proper permissions

**Missing Dependencies:**
```bash
# Update all dependencies
cd backoffice/src && uv sync --dev
pnpm install
```

### Performance Optimization
- Use database indexes for frequently queried columns
- Implement caching for expensive computations
- Profile LLM API usage to optimize costs
- Monitor memory usage in ML pipelines

---

## 🎯 Getting Started Checklist

- [ ] Run `./setup-dev-env.sh` (Ubuntu/Debian) or manual setup
- [ ] Verify Python environment: `cd backoffice/src && source .venv/bin/activate`
- [ ] Test Python installation: `uv run python src/cli.py claude-self-test`
- [ ] Verify frontend: `pnpm dev` 
- [ ] Run basic tests: `tsc --noEmit` and `cd backoffice/src && uv run python -m pytest src/tests/on_commit/`
- [ ] Set up database connections (see Database Setup section)
- [ ] Explore the codebase and start contributing!

Welcome to EkoIntelligence! 🌱 Start with the self-test command and explore the CLI to understand the platform's capabilities.
