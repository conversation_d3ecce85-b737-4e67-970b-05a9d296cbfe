#!/bin/bash
set -eux
cd  $(dirname $0)/../.. || exit
src_dir="$(pwd)"
#Create if not exists
if [ ! -d ~/claude_workdirs/fix-all-tests/repo ]; then
  mkdir -p ~/claude_workdirs/fix-all-tests/repo
  git clone "**************:ekointelligence/mono-repo.git" ~/claude_workdirs/fix-all-tests/repo
  cd ~/claude_workdirs/fix-all-tests/repo
  git checkout -b feature/fix-all-tests || true
  git branch --set-upstream-to=origin/main feature/fix-all-tests
  cd -
fi
#git worktree add -B feature/eko-$1 ~/claude_worktrees/$1 origin/main
cd $src_dir
cp .mcp.json ~/claude_workdirs/fix-all-tests/repo/.mcp.json
mkdir -p ~/claude_workdirs/fix-all-tests/repo/.claude
cp -f .claude/settings.json ~/claude_workdirs/fix-all-tests/repo/.claude/
cp -f .claude/settings.json ~/claude_workdirs/fix-all-tests/repo/.claude/settings.local.json
cp  apps/customer/.env.development.local ~/claude_workdirs/fix-all-tests/repo/apps/customer/.env.development.local
cd  ~/claude_workdirs/fix-all-tests/repo
cd ..

repo_dir=~/claude_workdirs/fix-all-tests/repo
RECHECK="Thanks for the help. Please check any changed tests against guidelines in apps/customer/tests/CLAUDE.md, and ensure each test
should follow a clear sequential path from start to finish, it should not branch, it should not have try/catch,
it should not fallback it should ignore failure states. A test either passes or fails completely,
it cannot partially pass. Please also make sure that tests haven't been deleted just because they are broken.
If they have please restore them."

PROMPT="
           Tests are failing after a refactor. The log file is in apps/customer/.last-test-run. The test result files are in apps/customer/test-results/.

           Please fix the failing tests.

           Do not add try/catch or if/else blocks to bypass the testing, do not delete failing tests.

           Please read apps/customer/tests/CLAUDE.md for details on writing tests.

           Look at a working test such as apps/customer/tests/editor-ai-features.spec.ts or apps/customer/tests/document-templates.spec.ts and see how it works.

           Run playwright tests as follows:  \`cd apps/customer && npx playwright test --reporter=line --max-failures=1\` .

           ---
           Each test should follow a clear sequential path from start to finish, it should not branch, it should not have try/catch, it should not fallback it should not ignore failure states. A test either passes or fails.
           ---

           If a test fails it's probably due to the refactor, i.e. the app has changed. The tests are
           most likely correct, it's most likely there is a bug in the code introduced during the
           refactor. If you are unsure, please fix the codebase.

           If you are fixing a test that relates to the editor then please look at /Users/<USER>/IdeaProjects/mono-repo/apps/customer/components/editor/README.md and any relevant sub documents. This document describes how the editor works.

           You're doing a great job, keep at it, plan this out and ultrathink carefully step by and work your way through this methodically.
           "

export PW_PORT=${1:-3030}
export CI=true
playwright_cmd="npx playwright test --output=test-results/ --max-failures=10"
run_claude="$src_dir/bin/claude-docker  fix-all-tests  --dangerously-skip-permissions --verbose  --output-format=stream-json --print "
# Loop while tests are failing

until (cd "$repo_dir"/apps/customer/  && $playwright_cmd  >  .last-test-run 2>&1 )
do
  cd "$repo_dir"/..
  count=0
   $run_claude --model=sonnet  "$PROMPT" | "$HOME/IdeaProjects/pretty-claude-json/pretty_claude.sh"
  until ( (cd "$repo_dir"/apps/customer/ && $playwright_cmd --last-failed ) >  .last-test-run  2>&1 ) || [ $count -eq 10 ]
  do
    cd "$repo_dir"/..
    $run_claude --model=sonnet "$PROMPT" | "$HOME/IdeaProjects/pretty-claude-json/pretty_claude.sh"
    ((count++))
  done
  $run_claude --model=opus "$RECHECK"| "$HOME/IdeaProjects/pretty-claude-json/pretty_claude.sh"
done
