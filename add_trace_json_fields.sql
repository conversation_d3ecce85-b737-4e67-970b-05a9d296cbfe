-- Add trace_json JSONB fields to selective highlighting and claims/promises tables
-- This script adds the new trace system fields to support comprehensive tracing

-- Set session to show progress
\echo 'Adding trace_json JSONB fields to selective highlighting and claims/promises tables...'

-- ========================================================================
-- SELECTIVE HIGHLIGHTING TABLES
-- ========================================================================

\echo 'Adding trace_json field to ana_cherry table...'
ALTER TABLE ana_cherry
    ADD COLUMN IF NOT EXISTS trace_json JSONB;

\echo 'Adding trace_json field to ana_flooding table...'
ALTER TABLE ana_flooding ADD COLUMN IF NOT EXISTS trace_json JSONB;

-- ========================================================================
-- CLAIMS AND PROMISES TABLES (TBD - need to identify table names)
-- ========================================================================

-- NOTE: Need to identify the actual table names for claims and promises analysis
-- These are placeholder queries that will need to be updated with actual table names

-- \echo 'Adding trace_json field to claims analysis table...'
-- ALTER TABLE [claims_table_name] ADD COLUMN IF NOT EXISTS trace_json JSONB;

-- \echo 'Adding trace_json field to promises analysis table...'
-- ALTER TABLE [promises_table_name] ADD COLUMN IF NOT EXISTS trace_json JSONB;

-- ========================================================================
-- CREATE GIN INDEXES FOR PERFORMANCE
-- ========================================================================

\echo 'Creating GIN indexes for trace_json fields...'

-- Selective highlighting indexes
CREATE INDEX IF NOT EXISTS idx_ana_cherry_trace ON ana_cherry USING GIN (trace_json);
CREATE INDEX IF NOT EXISTS idx_ana_flooding_trace ON ana_flooding USING GIN (trace_json);

-- Claims/promises indexes (TBD)
-- CREATE INDEX IF NOT EXISTS idx_[claims_table]_trace ON [claims_table] USING GIN (trace_json);
-- CREATE INDEX IF NOT EXISTS idx_[promises_table]_trace ON [promises_table] USING GIN (trace_json);

-- ========================================================================
-- VERIFICATION
-- ========================================================================

\echo 'Verifying trace_json fields have been added...'

-- Check selective highlighting tables
SELECT 
    table_name,
    column_name,
    data_type
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND column_name = 'trace_json'
  AND table_name IN ('ana_cherry', 'ana_flooding')
ORDER BY table_name;

-- List all GIN indexes on trace_json fields
\echo 'Checking GIN indexes on trace_json fields...'
SELECT 
    schemaname,
    tablename,
    indexname
FROM pg_indexes 
WHERE indexname LIKE '%trace%'
  AND tablename IN ('ana_cherry', 'ana_flooding')
ORDER BY tablename;

\echo 'trace_json field addition completed!'
\echo ''
\echo 'SUMMARY:'
\echo '- Added trace_json JSONB column to ana_cherry table'
\echo '- Added trace_json JSONB column to ana_flooding table'  
\echo '- Created GIN indexes for efficient JSONB querying'
\echo ''
\echo 'TODO:'
\echo '- Identify claims and promises table names'
\echo '- Update this script with actual table names'
\echo '- Add trace_json fields to those tables'
\echo '- Update corresponding DAOs to support trace data'
