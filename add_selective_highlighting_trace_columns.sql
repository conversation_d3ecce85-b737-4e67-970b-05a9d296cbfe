-- Add trace_json JSONB columns to selective highlighting tables
-- This script adds trace data support to cherry picking and flooding analysis tables

\echo 'Adding trace_json columns to selective highlighting tables...'

-- Add trace_json column to ana_cherry table
ALTER TABLE ana_cherry
    ADD COLUMN IF NOT EXISTS trace_json JSONB;
\echo 'Added trace_json column to ana_cherry'

-- Add trace_json column to ana_flooding table  
ALTER TABLE ana_flooding
    ADD COLUMN IF NOT EXISTS trace_json JSONB;
\echo 'Added trace_json column to ana_flooding'

-- Create GIN indexes for efficient JSONB queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ana_cherry_trace_json ON ana_cherry USING GIN (trace_json);
\echo 'Created GIN index on ana_cherry.trace_json'

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ana_flooding_trace_json ON ana_flooding USING GIN (trace_json);
\echo 'Created GIN index on ana_flooding.trace_json'

-- Verify the changes
\echo 'Verifying trace_json columns were added...'
SELECT table_name,
       column_name,
       data_type,
       is_nullable
FROM information_schema.columns
WHERE table_schema = 'public'
  AND column_name = 'trace_json'
  AND table_name IN ('ana_cherry', 'ana_flooding')
ORDER BY table_name;

\echo 'Selective highlighting trace column migration completed!'
