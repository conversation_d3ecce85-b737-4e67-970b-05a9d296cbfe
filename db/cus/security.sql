drop policy if exists  "Enable select for users based on run_by" on "public"."cus_ana_hist_gw_single_doc_runs";
drop policy if exists  "Enable select for users based on run_by" on "public"."cus_ana_hist_entity_runs";
drop policy if exists  "Enable select for users based on requester" on "public"."api_queue";
drop policy if exists  "Enable insert for users based on requester" on "public"."api_queue";
drop policy if exists  "Enable update for users based on requester" on "public"."api_queue";
drop policy if exists  "Enable select for auth users on own orgs" on "public"."acc_organisations";
drop policy if exists  "Enable select for users based on view_my_companies" on "public"."cus_ana_companies";
drop policy if exists  "Enable select for auth users" on "public"."acc_quota";
drop policy if exists "Enable select for authenticated users only" on "public"."xfer_entities";
drop policy if exists "Enable select for authenticated users only" on "public"."xfer_flags";
drop policy if exists "Enable select for authenticated users only" on "public".xfer_flags;
drop policy if exists "Enable select for authenticated users only" on "public".xfer_selective;
drop policy if exists "Enable select for authenticated users only" on "public"."xfer_gw_claims";
drop policy if exists "Enable select for authenticated users only" on "public"."xfer_gw_promises";
drop policy if exists "Enable select for authenticated users only" on "public"."xfer_gw_single_doc";
drop policy if exists "Enable select for authenticated users only" on "public"."xfer_gw_vague";
drop policy if exists "Enable select for authenticated users only" on "public"."xfer_issues";
drop policy if exists "Enable select for authenticated users only" on "public"."xfer_model_sections";
drop policy if exists "Enable select for authenticated users only" on "public"."xfer_runs";
drop policy if exists "Enable select for authenticated users only" on "public"."xfer_score";



--- The API queue is a special case where we want to allow users to see their own requests and add new requests
create policy "Enable select for users based on requester" on "public"."api_queue" as PERMISSIVE for SELECT to authenticated using ((select auth.uid()) = requester);
create policy "Enable insert for users based on requester" on "public"."api_queue" as PERMISSIVE for INSERT to authenticated with check ((select auth.uid()) = requester);
create policy "Enable update for users based on requester" on "public"."api_queue" as PERMISSIVE for UPDATE to authenticated using ((select auth.uid()) = requester);


--- The following policies are used to restrict access to the analysis history tables
create policy "Enable select for users based on run_by" on "public"."cus_ana_hist_gw_single_doc_runs" as PERMISSIVE for SELECT to authenticated using ((select auth.uid()) = run_by);
create policy "Enable select for users based on run_by" on "public"."cus_ana_hist_entity_runs" as PERMISSIVE for SELECT to authenticated using ((select auth.uid()) = run_by);

--- The following policies are used to restrict access to the org/account data
create policy "Enable select for auth users" on "public"."acc_quota" as PERMISSIVE for SELECT to authenticated using (true);
create policy "Enable select for auth users on own orgs" on "public"."acc_organisations" as PERMISSIVE for SELECT to authenticated using (id in (select profiles.organisation from profiles where profiles.id = auth.uid()));
create policy "Enable select for users based on view_my_companies" on "public"."cus_ana_companies" as PERMISSIVE for SELECT to authenticated using (true);


--- Read only tables populated by the backoffice
create policy "Enable select for authenticated users only" on "public"."xfer_entities" as PERMISSIVE for SELECT to authenticated using (true);
create policy "Enable select for authenticated users only" on "public"."xfer_flags" as PERMISSIVE for SELECT to authenticated using (true);
create policy "Enable select for authenticated users only" on "public".xfer_flags as PERMISSIVE for SELECT to authenticated using (true);
create policy "Enable select for authenticated users only" on "public".xfer_selective as PERMISSIVE for SELECT to authenticated using (true);
create policy "Enable select for authenticated users only" on "public"."xfer_gw_claims" as PERMISSIVE for SELECT to authenticated using (true);
create policy "Enable select for authenticated users only" on "public"."xfer_gw_promises" as PERMISSIVE for SELECT to authenticated using (true);
create policy "Enable select for authenticated users only" on "public"."xfer_gw_single_doc" as PERMISSIVE for SELECT to authenticated using (true);
create policy "Enable select for authenticated users only" on "public"."xfer_gw_vague" as PERMISSIVE for SELECT to authenticated using (true);
create policy "Enable select for authenticated users only" on "public"."xfer_issues" as PERMISSIVE for SELECT to authenticated using (true);
create policy "Enable select for authenticated users only" on "public"."xfer_model_sections" as PERMISSIVE for SELECT to authenticated using (true);
create policy "Enable select for authenticated users only" on "public"."xfer_runs" as PERMISSIVE for SELECT to authenticated using (true);
create policy "Enable select for authenticated users only" on "public"."xfer_score" as PERMISSIVE for SELECT to authenticated using (true);
