-- Add importance column to xfer_claims table in customer database
-- This migration adds the importance column to store the importance score of claims

-- Add importance column if it doesn't exist
ALTER TABLE xfer_claims
ADD COLUMN IF NOT EXISTS importance INTEGER DEFAULT 0;

-- Add comment to explain the purpose of this field
COMMENT ON COLUMN xfer_claims.importance IS 'Importance score of the claim (0-100), where higher scores indicate more newsworthy and impactful claims';

-- Create an index on importance for efficient filtering and sorting
CREATE INDEX IF NOT EXISTS idx_xfer_claims_importance ON xfer_claims (importance);
