-- <PERSON>ript to drop old tracking infrastructure tables
-- 
-- IMPORTANT: Run this ONLY after confirming that:
-- 1. New trace system is fully operational
-- 2. All conversion has been completed and tested
-- 3. No critical tracking data is needed from these tables
--
-- This script removes the old tracking system (trk_* tables in dash schema)
-- and replaces it with the new trace system (trace_json JSONB columns)

-- Set session to show progress
\echo 'Starting removal of old tracking infrastructure...'

-- ========================================================================
-- DROP OLD TRACKING TABLES (dash schema)
-- ========================================================================

\echo 'Dropping old tracking tables in dash schema...'

-- Drop tables in dependency order (foreign keys first)

-- Object relationships table (likely has foreign keys to other tables)
DROP TABLE IF EXISTS dash.trk_object_relationships CASCADE;
\echo 'Dropped: trk_object_relationships'

-- Effect assignments and flag creation tables
DROP TABLE IF EXISTS dash.trk_effect_assignments CASCADE;
\echo 'Dropped: trk_effect_assignments'

DROP TABLE IF EXISTS dash.trk_flag_creation CASCADE;
\echo 'Dropped: trk_flag_creation'

DROP TABLE IF EXISTS dash.trk_effect_flag_metrics CASCADE;
\echo 'Dropped: trk_effect_flag_metrics'

-- Statement and vectorization tracking
DROP TABLE IF EXISTS dash.trk_statement_decisions CASCADE;
\echo 'Dropped: trk_statement_decisions'

DROP TABLE IF EXISTS dash.trk_vectorization_metrics CASCADE;
\echo 'Dropped: trk_vectorization_metrics'

-- Clustering metrics
DROP TABLE IF EXISTS dash.trk_clustering_metrics CASCADE;
\echo 'Dropped: trk_clustering_metrics'

-- Pipeline tracking tables
DROP TABLE IF EXISTS dash.trk_pipeline_stats CASCADE;
\echo 'Dropped: trk_pipeline_stats'

DROP TABLE IF EXISTS dash.trk_pipeline_metrics CASCADE;
\echo 'Dropped: trk_pipeline_metrics'

DROP TABLE IF EXISTS dash.trk_pipeline_errors CASCADE;
\echo 'Dropped: trk_pipeline_errors'

-- Anomaly detection (was empty anyway)
DROP TABLE IF EXISTS dash.trk_anomalies CASCADE;
\echo 'Dropped: trk_anomalies'

-- ========================================================================
-- VERIFICATION
-- ========================================================================

\echo 'Verifying all tracking tables have been removed...'

-- Check that no trk_ tables remain
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN 'SUCCESS: All old tracking tables have been removed'
        ELSE 'WARNING: ' || COUNT(*) || ' tracking tables still exist'
    END as status
FROM information_schema.tables 
WHERE table_schema = 'dash' 
AND table_name LIKE 'trk_%';

-- Show remaining dash schema tables (should be other dashboard tables only)
\echo 'Remaining tables in dash schema:'
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'dash' 
ORDER BY table_name;

-- ========================================================================
-- VERIFY NEW TRACE SYSTEM IS ACTIVE
-- ========================================================================

\echo 'Verifying new trace system is in place...'

-- Check that trace_json columns exist in main tables
SELECT 
    table_name,
    column_name,
    data_type
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND column_name = 'trace_json'
AND table_name IN ('ana_effects', 'ana_effect_flags')
ORDER BY table_name;

-- Check customer database trace columns
\echo 'Checking customer database trace columns...'
-- Note: This will only work if run against customer database
-- SELECT table_name, column_name FROM information_schema.columns 
-- WHERE column_name = 'trace_json' AND table_name LIKE 'xfer_%';

\echo 'Old tracking infrastructure removal completed!'
\echo ''
\echo 'SUMMARY:'
\echo '- Old trk_* tables have been dropped from dash schema'
\echo '- New trace system uses trace_json JSONB columns in:'
\echo '  * ana_effects.trace_json'
\echo '  * ana_effect_flags.trace_json'
\echo '  * xfer_flags.trace_json (customer DB)'
\echo ''
\echo 'The new trace system provides:'
\echo '- Complete pipeline traceability'
\echo '- LLM call tracking with costs'
\echo '- Processing step details'
\echo '- Quality metrics and relationships'
\echo '- Hierarchical trace data inheritance'
\echo '- Customer UI trace modal access'
