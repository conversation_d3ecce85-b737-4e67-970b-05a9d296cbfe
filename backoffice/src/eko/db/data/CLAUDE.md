# <PERSON><PERSON><PERSON> Pattern in Eko Codebase

This document outlines the Data Access Object (DAO) pattern as implemented in the Eko codebase, serving as a reference for both existing and new DAO implementations.

## Core DAO Pattern

The Eko codebase follows a consistent DAO pattern with these key characteristics:

1. **Static Class Methods**: DAOs are implemented as classes with static methods for CRUD operations
2. **Connection and Cursor Management**: Database connections are passed in rather than created internally
3. **Pydantic Models**: All DAOs work with Pydantic models for data validation and serialization
4. **ID Return Pattern**: Create methods return the model with the database ID set
5. **Explicit Error Handling**: Validation errors and database errors are handled explicitly

## DAO Structure Template

Each DAO typically follows this structure:

```python
class ExampleData:
    """
    Handles CRUD operations for examples in the ana_examples table.
    
    DDL for table creation:
    CREATE TABLE IF NOT EXISTS ana_examples (
        id SERIAL PRIMARY KEY,
        name TEXT NOT NULL,
        ...
    );
    """

    @staticmethod
    def create(conn: Connection, model: ExampleModel, ...) -> ExampleModel:
        """Create a new example record"""
        with conn.cursor() as cursor:
            cursor.execute("""
                INSERT INTO ana_examples (name, ...)
                VALUES (%s, ...)
                RETURNING id
            """, (model.name, ...))
            
            example_id = cursor.fetchone()[0]
        conn.commit()
        
        # Set ID on model before returning
        model.id = example_id
        return model
        
    @staticmethod
    def get_by_id(conn: Connection, example_id: int) -> Optional[ExampleModel]:
        """Retrieve an example by ID"""
        with conn.cursor(row_factory=dict_row) as cursor:
            cursor.execute("""
                SELECT id, name, ...
                FROM ana_examples
                WHERE id = %s
            """, (example_id,))
            
            row = cursor.fetchone()
            
            if not row:
                return None
                
            return ExampleModel.model_validate(row)
    
    @staticmethod
    def update(conn: Connection, example_id: int, model: ExampleModel) -> ExampleModel:
        """Update an existing example"""
        with conn.cursor() as cursor:
            cursor.execute("""
                UPDATE ana_examples
                SET name = %s, ...
                WHERE id = %s
            """, (model.name, ..., example_id))
            
        conn.commit()
        return model
```

## Best Practices

### 1. Connection Management

- **Accept connections**: DAOs should accept a database connection as a parameter, not create one
- **Commit after writes**: Commit the connection after INSERT, UPDATE, or DELETE operations
- **No transaction dependency**: DAOs should not depend on external transaction management unless specified

```python
@staticmethod
def create(conn: Connection, model: ExampleModel) -> ExampleModel:
    # Execute SQL
    conn.commit()  # Commit within the DAO
    return model
```

### 2. Error Handling

- **Validate parameters**: Check for required parameters before executing SQL
- **Raise specific exceptions**: Use ValueError for validation errors
- **Log errors**: Use loguru for error logging

```python
if not model.name:
    raise ValueError("Name is required")

try:
    # Execute SQL
except Exception as e:
    logger.error(f"Database error: {e}")
    conn.rollback()
    raise
```

### 3. Return Types

- **Always return models**: Return Pydantic models, not raw database rows
- **Set IDs on models**: Set the database ID on the model before returning it
- **Use Optional types**: Use Optional[ModelType] for methods that might return None

```python
@staticmethod
def get_by_id(conn: Connection, example_id: int) -> Optional[ExampleModel]:
    # Execute SQL
    if not row:
        return None
    
    return ExampleModel.model_validate(row)
```

### 4. Row Factory

- **Use dict_row**: Use psycopg's dict_row for cursor row factory to simplify mapping to models
- **Pydantic validation**: Use model_validate or model_validate_json to convert database rows to models

```python
with conn.cursor(row_factory=dict_row) as cursor:
    # Execute query
    model = ExampleModel.model_validate(row)
```

### 5. Batch Operations

- **Implement batch methods**: For performance-critical operations, implement batch create/update methods
- **Executemany**: Use cursor.executemany for batch operations

```python
@staticmethod
def create_batch(conn: Connection, models: List[ExampleModel]) -> List[ExampleModel]:
    # Use executemany pattern
    return models
```

## Utility Methods

Include standard utility methods in your DAOs:

1. **get_by_id**: Retrieve a record by its primary key
2. **list_records**: List records with optional filtering
3. **count_records**: Count records with optional filtering
4. **get_latest**: Get the most recent record

## Examples in the Codebase

Reference existing DAOs for examples:

1. `RunData`: For analysis runs in the ana_runs table
2. `StatementData`: For statements in the ana_statements table
3. `EffectData`: For effects in the ana_effect_values table
4. `EffectFlagData`: For effect flags in the ana_effect_flags table 
5. `EntityData`: For entities in the kg_base_entities table

## Trace Data Integration Pattern

**CRITICAL**: Trace data must be passed as separate parameters, NEVER stored in business model fields.

### Correct DAO Pattern with Trace Data

```python
import json
from typing import Optional, Dict, Any
from eko.analysis_v2.trace_collector import TraceCollector

class ExampleData:
    @staticmethod
    def create(conn: Connection, model: ExampleModel, trace_json: Optional[Dict[str, Any]] = None) -> ExampleModel:
        """Create a new example record with optional trace data."""
        with conn.cursor() as cursor:
            cursor.execute("""
                INSERT INTO ana_examples (name, trace_json)
                VALUES (%s, %s)
                RETURNING id
            """, (model.name, json.dumps(trace_json) if trace_json else None))
            
            example_id = cursor.fetchone()[0]
        conn.commit()
        
        model.id = example_id
        return model
        
    @staticmethod
    def get_by_id(conn: Connection, example_id: int) -> Optional[ExampleModel]:
        """Retrieve an example by ID (trace data retrieved separately if needed)."""
        with conn.cursor(row_factory=dict_row) as cursor:
            cursor.execute("""
                SELECT id, name  -- Exclude trace_json from normal business queries
                FROM ana_examples
                WHERE id = %s
            """, (example_id,))
            
            row = cursor.fetchone()
            if not row:
                return None
                
            return ExampleModel.model_validate(row)
    
    @staticmethod
    def get_trace_data(conn: Connection, example_id: int) -> Optional[Dict[str, Any]]:
        """Retrieve trace data separately when needed."""
        with conn.cursor() as cursor:
            cursor.execute("""
                SELECT trace_json
                FROM ana_examples
                WHERE id = %s
            """, (example_id,))
            
            row = cursor.fetchone()
            if row and row[0]:
                return json.loads(row[0])
            return None
```

### Business Model Pattern (NO trace_data fields)

```python
class ExampleModel(BaseModel):
    id: Optional[int] = None
    name: str
    # NO trace_data field here!
    
class ExampleModelWithTrace(BaseModel):
    """Only use this pattern if you specifically need trace data in response models."""
    id: Optional[int] = None
    name: str
    trace_data: Optional[Dict[str, Any]] = None  # Only for API responses, not business logic
```

### Function Integration Pattern

```python
def process_examples(conn: Connection, models: List[ExampleModel], trace_collector: Optional[TraceCollector] = None):
    """Process examples with optional trace collection."""
    results = []
    
    for model in models:
        if trace_collector:
            trace_collector.start_step(f"processing_{model.name}")
            
        # Do processing work
        processed_model = ExampleData.create(conn, model, trace_collector.get_trace_json() if trace_collector else None)
        
        if trace_collector:
            trace_collector.end_step(f"processing_{model.name}", result={"success": True})
            
        results.append(processed_model)
    
    return results
```

## Integration Pattern

Higher-level functions should handle connection creation and closure:

```python
def high_level_function():
    close_conn = False
    if conn is None:
        conn = get_bo_conn()
        close_conn = True
    try:
        # Use DAOs with the connection
        model = ExampleData.create(conn, ...)
    finally:
        if close_conn:
            conn.close()
```

## Type Enums

Use string constants for enum-like values to ensure type safety:

```python
class ExampleStatus:
    """Possible values for example_status enum in the database"""
    ACTIVE = "active"
    INACTIVE = "inactive"
```

## Connection References

Always use the appropriate connection function:

1. `get_bo_conn()`: For the main backoffice database
2. `get_dash_conn()`: For dashboard metrics tables
3. `get_cus_conn()`: For customer-facing database