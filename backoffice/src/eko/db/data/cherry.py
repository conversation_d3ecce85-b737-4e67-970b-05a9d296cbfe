from eko.analysis_v2.cherry_models import CherryPickingAnalysis
from psycopg import Connection
from psycopg.rows import dict_row
from typing import List, Optional, Dict, Any


class CherryData:
    """
    Handles CRUD operations for cherry picking analysis in the ana_cherry table.

    DDL for table creation:

    CREATE TABLE IF NOT EXISTS ana_cherry (
        id SERIAL PRIMARY KEY,
        run_id INTEGER NOT NULL,
        entity_xid TEXT NOT NULL,
        label TEXT NOT NULL,
        red_flags JSONB NOT NULL,
        green_flags JSONB NOT NULL,
        model TEXT NOT NULL,
        score INTEGER NOT NULL,
        explanation TEXT NOT NULL,
        analysis TEXT NOT NULL,
        reason TEXT NOT NULL,
        citations JSONB,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
        UNIQUE(run_id, entity_xid, label)
    );

    CREATE INDEX IF NOT EXISTS ana_cherry_run_id_idx ON ana_cherry(run_id);
    CREATE INDEX IF NOT EXISTS ana_cherry_entity_xid_idx ON ana_cherry(entity_xid);
    """

    @staticmethod
    def create(conn: Connection, run_id: int, entity_xid: str, label: str, red_flags: List[int], 
              green_flags: List[int], model: str, analysis: CherryPickingAnalysis) -> int:
        """
        Creates a new cherry picking analysis record in the database.
        
        Args:
            conn: Database connection
            run_id: The run ID
            entity_xid: Entity XID
            label: Group label
            red_flags: List of red flag IDs
            green_flags: List of green flag IDs
            model: Model name
            analysis: CherryPickingAnalysis object containing analysis data
            
        Returns:
            ID of the created cherry picking record
        """
        with conn.cursor() as cursor:
            # Insert into ana_cherry
            cursor.execute(
                """
                INSERT INTO ana_cherry (
                    run_id, entity_xid, label, red_flags, green_flags, model, score,
                    explanation, analysis, reason, citations, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW()
                )
                ON CONFLICT (run_id, entity_xid, label) 
                DO UPDATE SET 
                    red_flags = EXCLUDED.red_flags,
                    green_flags = EXCLUDED.green_flags,
                    model = EXCLUDED.model,
                    score = EXCLUDED.score,
                    explanation = EXCLUDED.explanation,
                    analysis = EXCLUDED.analysis,
                    reason = EXCLUDED.reason,
                    citations = EXCLUDED.citations
                RETURNING id
                """,
                (
                    run_id,
                    entity_xid,
                    label,
                    red_flags,
                    green_flags,
                    model,
                    analysis.score,
                    analysis.explanation,
                    analysis.analysis,
                    analysis.reason,
                    analysis.citations,
                ),
            )
            
            cherry_id = cursor.fetchone()[0]
            
        conn.commit()
        return cherry_id

    @staticmethod
    def get_by_id(conn: Connection, cherry_id: int) -> Dict[str, Any]:
        """
        Retrieves a cherry picking analysis by ID.
        
        Args:
            conn: Database connection
            cherry_id: Cherry picking analysis ID
            
        Returns:
            Dictionary representing the cherry picking analysis
        """
        with conn.cursor(row_factory=dict_row) as cursor:
            cursor.execute(
                """
                SELECT * FROM ana_cherry WHERE id = %s
                """, (cherry_id,)
            )
            
            row = cursor.fetchone()
            
            if not row:
                raise ValueError(f"Cherry picking analysis with ID {cherry_id} not found")
                
            return row

    @staticmethod
    def get_by_entity(conn: Connection, entity_xid: str, run_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Retrieves all cherry picking analyses for a given entity, optionally filtering by run ID.
        
        Args:
            conn: Database connection
            entity_xid: Entity XID
            run_id: Optional run ID to filter by
            
        Returns:
            List of cherry picking analysis dictionaries
        """
        analyses = []
        with conn.cursor(row_factory=dict_row) as cursor:
            if run_id is not None:
                cursor.execute(
                    """
                    SELECT * FROM ana_cherry 
                    WHERE entity_xid = %s AND run_id = %s
                    ORDER BY id
                    """, (entity_xid, run_id)
                )
            else:
                cursor.execute(
                    """
                    SELECT * FROM ana_cherry 
                    WHERE entity_xid = %s 
                    ORDER BY id
                    """, (entity_xid,)
                )
                
            analyses = cursor.fetchall()
                
        return analyses

    @staticmethod
    def delete(conn: Connection, cherry_id: int) -> bool:
        """
        Deletes a cherry picking analysis from the database.
        
        Args:
            conn: Database connection
            cherry_id: ID of the cherry picking analysis to delete
            
        Returns:
            True if the analysis was deleted, False if it wasn't found
        """
        with conn.cursor() as cursor:
            cursor.execute("DELETE FROM ana_cherry WHERE id = %s", (cherry_id,))
            deleted = cursor.rowcount > 0
            
        conn.commit()
        
        return deleted
