from concurrent.futures.process import ProcessPoolExecutor

from loguru import logger
from psycopg.errors import UniqueViolation

from eko.commands.scrape import add_metadata_only
from eko.db import get_bo_conn
from eko.db._deprecated_xfer import (
    xfer_entities,
    xfer_issues,
    xfer_model_sections,
    xfer_runs,
    sync_xfer_tables_command,
    xfer_gw_claims,
    xfer_gw_promises,
    xfer_gw_vague,
    xfer_selective,
)
from eko.llm.main import get_embedding
from eko.log.log import set_log_context, log_info
from eko.pdf.main import download_pdf
from eko.util.hash import sha256_hash_file

issue_section_fix_map = {
    "doughnut": {"justice": "justice", "biodiversity_ocean": "ocean", "employment": "work", "economy": "safe_space",
                 "pollution_air": "air_pollution", "finance": "safe_space", "supply_chain": "safe_space",
                 "ozone": "ozone_depletion", "governance": "safe_space", "energy_production": "energy",
                 "biodiversity_welfare": "biodiversity",
                 "ocean": "ocean_acidification", "wealth": "safe_space", "climate": "climate_change",
                 "water_use": "water", "health": "health",
                 "society": "social_equity", "consumer": "safe_space", "society_equality": "social_equity",
                 "climate_change_c_o2": "climate_change"},
    "sdg": {"industry": "9_industry_innovation_infrastructure", "governance": "9_industry_innovation_infrastructure",
            "technology": "9_industry_innovation_infrastructure", "climate_change_c_o2": "13_climate_action",
            "economy": "8_decent_work_economic_growth", "employment": "8_decent_work_economic_growth",
            "justice": "16_peace_and_justice", "finance": "8_decent_work_economic_growth",
            "health": "3_good_health_wellbeing", "agriculture":"15_life_on_land", 
            "resources":"12_responsible_consumption",
            "biodiversity_welfare": "15_life_on_land","consumer":"9_industry_innovation_infrastructure",
            "society":"16_peace_and_justice", "diversity":"16_peace_and_justice","pollution_air":"7_affordable_clean_energy"}

}


def fix_issues_command():
    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            logger.info("Fixing synonyms for issues")
            cur.execute(
                "INSERT INTO kg_issue_synonyms select issue, issue from _deprecated_kg_issues ON CONFLICT(query_text) DO NOTHING")
            logger.info("Fixing issue section map")
            for model, map in issue_section_fix_map.items():
                for from_section, to_section in map.items():
                    cur.execute("""INSERT INTO kg_issue_section_map (issue, model, model_section)
                                   select _deprecated_kg_issues.issue, %s, %s from _deprecated_kg_issues left join kg_issue_section_map map on map.issue=_deprecated_kg_issues.issue and map.model=%s
    where map.issue is null and ethical_group = %s ON CONFLICT DO NOTHING""", (model, to_section, model, from_section))
                    conn.commit()
                    cur.execute("""select _deprecated_kg_issues.issue, ethical_group from _deprecated_kg_issues left join kg_issue_section_map map on map.issue=_deprecated_kg_issues.issue and map.model=%s
                                   where map.issue is null""", (model,))
                    rows = cur.fetchall()
                for issue, ethical_group in rows:
                    logger.info(f"Still unmapped {issue} ({ethical_group}) for {model}")


def fix_metadata_command():
    set_log_context("check-for-reports", None)
    log_info("main", "Started Fixing Report Metadata")
    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            cur.execute(
                "SELECT id, url FROM kg_documents WHERE status != 'deleted' AND  (select count(*) from kg_document_pages where doc_id = kg_documents.id) = 0")
            rows = cur.fetchall()
            futures = []
            with ProcessPoolExecutor() as executor:
                for doc_id, url, in rows:
                    futures.append(executor.submit(add_metadata_only, url, doc_id))
            for future in futures:
                try:
                    future.result()
                except Exception as e:
                    logger.exception(f"Failed to add metadata for a report: {e}")

    log_info("main", "Finished Check for New Reports")


def fix_doc_hash():
    set_log_context("fix-report-hashes", None)
    log_info("main", "Started Report Hash Fix")

    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            cur.execute("SELECT id, url FROM kg_documents WHERE hash IS NULL AND status != 'deleted'")
            rows = cur.fetchall()
            for id, url in rows:
                download_pdf(url, '/tmp/temp.pdf')
                hash = sha256_hash_file('/tmp/temp.pdf')
                logger.info(f"Updating hash for report {id} to {hash}")
                try:
                    cur.execute("UPDATE kg_documents SET hash = %s WHERE id = %s", (hash, id))
                except UniqueViolation as e:
                    conn.rollback()
                    logger.error(f"Hash {hash} already exists for another report, deleting this report.")
                    cur.execute("UPDATE kg_documents SET status = 'deleted' WHERE id = %s", (id,))
                conn.commit()
        conn.commit()
    log_info("main", "Finished Report Hash Fix")


def fix_if_embeddings_command():
    set_log_context("fix-if-embeddings", None)

    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            cur.execute("SELECT id, flag_text FROM ana_issue_flags WHERE embedding IS NULL order by id desc")
            rows = cur.fetchall()
            for id, flag_text in rows:
                embedding = get_embedding(flag_text)
                logger.info(f"Updating embedding for issue_flag {id}")
                cur.execute("UPDATE ana_issue_flags SET embedding = %s WHERE id = %s", (embedding, id))
                conn.commit()


def fix_xfer(run_id: int):
    xfer_runs()
    xfer_gw_claims(run_id)
    xfer_gw_promises(run_id)
    xfer_gw_vague(run_id)
    xfer_selective(run_id)
    xfer_entities()
    xfer_issues()
    xfer_model_sections()
    sync_xfer_tables_command()
