"""score_flag.py
====================
Unified scoring system for virtual entities based on red flag analysis.
New calculation: 100 - (average percentage score of the top 10% of red flags)
"""

from __future__ import annotations

import math
import logging
from datetime import datetime
from typing import List

from psycopg import Connection

from eko.db import get_cus_conn
from eko.models.xfer.xfer_score import XferScoreModel

logger = logging.getLogger(__name__)


def select_entities_with_flags(cur, run_id: int):
    """Get all entities that have flags for the given run."""
    cur.execute(
        """
        SELECT DISTINCT ve.id, ve.short_id
        FROM ana_effect_flags ef
                 JOIN kg_virt_entities ve ON ve.id = ef.virtual_entity_id
        WHERE ef.run_id = %s
        """,
        (run_id,),
    )
    return cur.fetchall()


class UnifiedScoreSync:
    """Unified scoring system for virtual entities using top 10% red flag average."""

    @classmethod
    def sync_score_to_xfer_v2(cls, conn: Connection, run_id: int) -> List[str]:
        """
        Sync entity scores to xfer_score table using the new unified calculation.

        New formula: 100 - (average percentage score of the top 10% of red flags)
        """
        synced: List[str] = []

        with conn.cursor() as cur:
            entities = select_entities_with_flags(cur, run_id)

        for entity_id, entity_xid in entities:
            try:
                with conn.cursor() as cur:
                    # Get red flags for this entity
                    cur.execute(
                        """
                        SELECT 
                            AVG(ef.impact) AS value,
                            COUNT(*) AS flag_count,
                            ef.title
                        FROM ana_effect_flags ef
                        WHERE ef.confidence > 60
                          AND ef.impact > 1
                          AND ef.effect_type = 'red'
                          AND ef.virtual_entity_id = %s
                          AND ef.run_id = %s
                        GROUP BY ef.title
                        ORDER BY value DESC
                        """,
                        (entity_id, run_id),
                    )
                    red_flag_rows = cur.fetchall()
                    
                    # Get green flags for telemetry (not used in scoring)
                    cur.execute(
                        """
                        SELECT 
                            AVG(ef.impact) AS value,
                            COUNT(*) AS flag_count,
                            ef.title
                        FROM ana_effect_flags ef
                        WHERE ef.confidence > 60
                          AND ef.impact > 1
                          AND ef.effect_type = 'green'
                          AND ef.virtual_entity_id = %s
                          AND ef.run_id = %s
                        GROUP BY ef.title
                        """,
                        (entity_id, run_id),
                    )
                    green_flag_rows = cur.fetchall()

                # Calculate the new unified score
                final_score = cls._calculate_unified_score(red_flag_rows)
                
                # Prepare red and green flag data for telemetry
                red_scores = [float(row[0]) for row in red_flag_rows if row[0] is not None]
                green_scores = [float(row[0]) for row in green_flag_rows if row[0] is not None]
                
                red_count = len(red_flag_rows)
                green_count = len(green_flag_rows)

                cls._persist_score(
                    entity_xid, 
                    run_id, 
                    final_score,
                    red_count, 
                    green_count,
                    red_scores, 
                    green_scores,
                    timestamp=datetime.now()
                )
                synced.append(entity_xid)
                
            except Exception as e:
                logger.error(f"UnifiedScoreSync failed for entity {entity_xid} in run {run_id}: {e}")
                logger.exception(e)
                raise

        logger.info("UnifiedScoreSync: %d entities processed for run %s", len(synced), run_id)
        return synced

    @classmethod
    def _calculate_unified_score(cls, red_flag_rows: List) -> int:
        """
        Calculate unified score: 100 - (average percentage score of the top 10% of red flags)
        
        Args:
            red_flag_rows: List of tuples (value, count, title) from red flag query
            
        Returns:
            Score from 0-100 (higher is better)
        """
        if not red_flag_rows:
            return 100  # Perfect score if no red flags
            
        # Extract scores, filtering out None values
        red_scores = [float(row[0]) for row in red_flag_rows if row[0] is not None]
        
        if not red_scores:
            return 100  # Perfect score if no valid red flag scores
            
        # Sort scores in descending order (highest first)
        red_scores.sort(reverse=True)
        
        # Calculate top 10% count (minimum 1)
        top_10_percent_count = max(1, math.ceil(len(red_scores) * 0.1))
        
        # Get top 10% of scores
        top_scores = red_scores[:top_10_percent_count]
        
        # Calculate average of top 10%
        average_top_score = sum(top_scores) / len(top_scores)
        
        # Convert to final score: 100 - average percentage
        final_score = int(round(100 - average_top_score))
        
        # Ensure score is within bounds
        return max(0, min(100, final_score))

    @staticmethod
    def _rating_text(score: int) -> str:
        """Convert score to rating text."""
        if score >= 85:
            return "Great"
        elif score >= 75:
            return "Very Good"
        elif score >= 60:
            return "Good"
        elif score >= 40:
            return "Poor"
        else:
            return "Very Poor"

    @staticmethod
    def _severity_label(score: int) -> str:
        """Convert score to severity label."""
        inv = 100 - score  # Invert score for severity
        if inv >= 60:
            return "Very Serious"
        elif inv >= 40:
            return "Serious"
        elif inv >= 20:
            return "Major"
        elif inv >= 10:
            return "Minor"
        elif inv >= 5:
            return "Very Minor"
        else:
            return "Trivial"

    @classmethod
    def _persist_score(cls, entity_xid: str, run_id: int, score: int,
                       red_count: int, green_count: int,
                       red_scores: List[float], green_scores: List[float],
                       timestamp: datetime):
        """Persist score to database using XferScoreModel."""
        import numpy as np
        
        xfer_score = XferScoreModel(
            entity_xid=entity_xid,
            score=score,
            rating_text=cls._rating_text(score),
            minor_major_text=cls._severity_label(score),
            red_flags_count=red_count,
            green_flags_count=green_count,
            red_flags_score=round(sum(red_scores), 2) if red_scores else 0.0,
            green_flags_score=round(sum(green_scores), 2) if green_scores else 0.0,
            average_red=float(np.mean(red_scores)) if red_scores else 0.0,
            average_green=float(np.mean(green_scores)) if green_scores else 0.0,
            median_red=float(np.median(red_scores)) if red_scores else 0.0,
            median_green=float(np.median(green_scores)) if green_scores else 0.0,
            created_at=timestamp.isoformat(),
        )
        model_json = xfer_score.model_dump_json()

        with get_cus_conn() as cus_conn:
            with cus_conn.cursor() as cus_cur:
                cus_cur.execute(
                    """
                    INSERT INTO xfer_score (entity_xid, run_id, score, model)
                    VALUES (%s, %s, %s, %s)
                    ON CONFLICT (entity_xid, run_id)
                        DO UPDATE SET score = EXCLUDED.score,
                                      model = EXCLUDED.model
                    """,
                    (entity_xid, run_id, score, model_json),
                )
            cus_conn.commit()
