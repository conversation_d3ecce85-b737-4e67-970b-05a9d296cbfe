from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    effect_text_merging_similiarity_threshold: float = 0.75 # cosine SIMILARITY !!!! i.e. 1.0-eps
    effect_flag_merging_text_eps: float = 0.2 #Cosine
    effect_flag_merging_domain_eps: float = 0.2 #Cosine
    effect_flag_merging_year_adjacency_threshold: int = 5
    effect_flag_top_n_domains: int = 5

    statement_clustering_method: str = "DBSCAN"
    statement_clustering_eps: float = 0.25  #Cosine
    statement_clustering_min_samples: int = 1
    harm_score_weighting: float = 2.0

    predictive_statement_clustering_eps: float = 0.2  #Cosine
    predictive_statement_clustering_min_samples: int = 3
    predictive_statement_clustering_method: str = "DBSCAN"
    predictive_minimum_regression_datapoints: int = 4
    # Effect flag credibility threshold
    effect_flag_min_credibility: int = (
        40  # Minimum credibility score (0-100) for effect flags to be included in CMS and customer app
    )

    # Vague terms analysis settings
    vague_terms_climatebert_model: str = "climatebert/environmental-claims"
    vague_terms_detection_threshold: float = 0.7
    vague_terms_greenwashing_density_threshold: float = 0.05

    # # Cherry picking analysis settings
    # cherry_picking_dbscan_eps: float = 0.35
    # cherry_picking_dbscan_min_samples: int = 5
    # cherry_picking_significant_red_impact_threshold: float = 0.3
    # cherry_picking_minor_green_impact_threshold: float = 0.2
    # cherry_picking_min_green_flags: int = 5

    cherry_picking_min_repeat_count:int=4
    cherry_picking_statement_similarity_threshold:float=0.7
    cherry_picking_domain_similarity_threshold:float=0.5
    cherry_picking_negative_impact_threshold:float=-0.01
    cherry_picking_positive_impact_threshold:float=0.01

    flooding_min_positive_cluster_size:int= 3
    flooding_small_positive_threshold:float= 0.3
    flooding_similarity_threshold:float= 0.7
    flooding_negative_impact_threshold:float= -0.3

    # Cherry picking and flooding merging settings
    # These are specific settings for cherry picking and flooding merging, different from effect flags
    cherry_picking_merging_domain_eps:float=0.3  # Cosine distance threshold for domain-based merging
    cherry_picking_merging_text_eps:float=0.5    # Cosine distance threshold for text-based merging
    flooding_merging_domain_eps:float=0.3        # Cosine distance threshold for domain-based merging
    flooding_merging_text_eps:float=0.5          # Cosine distance threshold for text-based merging

    # Claims and promises merging settings
    # These are specific settings for claims and promises merging
    claims_merging_domain_eps: float = 0.25  # Cosine distance threshold for domain-based merging
    claims_merging_text_eps:float=0.5            # Cosine distance threshold for text-based merging
    claims_merging_year_adjacency_threshold:int=2  # Maximum year difference for claims to be merged
    claims_minimum_confidence: int = 50
    promises_merging_domain_eps:float=0.3        # Cosine distance threshold for domain-based merging
    promises_merging_text_eps:float=0.5          # Cosine distance threshold for text-based merging
    promises_merging_year_adjacency_threshold:int=2  # Maximum year difference for promises to be merged

    max_page_size_chars:int=10000

    # Statement filtering settings
    statement_min_domain_credibility: int = 50  # Minimum credibility score (0-100) for domains to be included in statement queries
    #  ('activist', 'regulator', 'ngo', 'media', 'q&a', 'research', 'reference', 'commercial', 'personal', 'e-commerce', 'cdn', 'archive', 'data', 'api', 'government', 'education', 'host', 'saas', 'lobbying');
    statement_allowed_domain_roles: list[str] = [
        "activist",
        "regulator",
        "ngo",
        "media",
        "research",
        "reference",
        "cdn",
        "archive",
        "data",
        "api",
        "government",
        "education",
        "host",
        "saas",
        "lobbying",
    ]  # Domain roles allowed for statements

    #  ('unknown', 'misc', 'ngo', 'social media', 'news', 'finance', 'hosted', 'science', 'charity', 'education', 'government', 'health', 'human rights', 'lobbying', 'political', 'security', 'transportation', 'technology', 'entertainment', 'sports', 'arts', 'travel', 'hospitality', 'real estate', 'environmental', 'legal', 'manufacturing', 'telecommunication', 'utilities', 'gaming', 'automotive', 'cryptocurrency', 'data', 'religion', 'community', 'food and beverage', 'fashion', 'jobs', 'events', 'marketing', 'software', 'adult', 'agriculture', 'animals', 'nature', 'architecture', 'beauty', 'books', 'business', 'comics', 'dating', 'design', 'directories', 'family', 'history', 'home and garden', 'humor', 'lifestyle', 'literature', 'military', 'movies', 'music', 'photography', 'shopping', 'vehicles', 'weather', 'fitness', 'biotech');
    statement_excluded_domain_categories: list[str] = [
        "unknown",
        "social media",
        "hosted",
        "lobbying",
        "entertainment",
        "sports",
        "travel",
        "cryptocurrency",
        "adult",
        "dating",
        "design",
        "family",
        "home and garden",
        "humor",
        "lifestyle",
        "movies",
        "music",
        "photography",
        "shopping",
        "weather",
        "fitness",
    ]  # Domain categories to exclude from statements

    #flags
    flag_severity_midpoint: float = 12.0  # Midpoint for flag severity scoring
    flag_severity_slope: float = 0.4      # Slope for flag severity scoring
    flag_recency_lambda: float = 0.4      # Lambda for flag recency weighting
    bayes_alpha_prior: float = 2.0        # Alpha prior for Bayesian scoring
    bayes_beta_prior: float = 8.0         # Beta prior for Bayesian scoring

    llm_record_cost_in_db:bool= False

    # Pipeline tracker settings
    flag_pipeline_tracker_db_persistence: bool = False  # Whether to persist pipeline tracking data to the database
    flag_persistent_llm_usage_info: bool = False  # Whether to persist LLM Usage Stats


settings = Settings()
