"""
Clustering functionality for predictive analytics v2.

This module provides functions for clustering statements by Domain+Subject+Object
and tracking the evolution of clusters over time.
"""
from psycopg.rows import DictRow
from typing import List, Dict, Optional, Any, Tuple, cast

import numpy as np
from loguru import logger
from psycopg import Cursor

from eko.analysis_v2.effects.clustering import perform_clustering, ClusteringMethod
from eko.analysis_v2.trace_collector import TraceCollector
from eko.db import get_bo_conn
from eko.models.vector.demise.demise_model import DEMISEModel, DOMAIN_MODEL_VECTOR_SIZE, ENTITY_MODEL_VECTOR_SIZE
from eko.models.vector.demise.domain import DomainModel
from eko.models.vector.derived.enums import PipelineStage
from eko.models.virtual_entity import VirtualEntityExpandedModel
from eko.prediction_v2.dao import DSO_ClusterDAO
from eko.prediction_v2.models import DSO_ClusterModel
from eko.settings import settings


def get_statements_by_year(
    cursor: <PERSON>ursor,
    virtual_entity_id: int,
    start_year: int,
    end_year: int
) -> Dict[int, List[Dict[str, Any]]]:
    """
    Get statements grouped by year for a virtual entity.

    Args:
        cursor: Database cursor
        virtual_entity_id: Virtual entity ID
        start_year: Start year (inclusive)
        end_year: End year (inclusive)

    Returns:
        Dictionary mapping years to lists of statements
    """
    from psycopg.rows import dict_row

    query = """
    SELECT
        s.id,
        s.statement_text as text,
        s.start_year as year,
        s.demise_embedding,
        s.model_json,
        s.impact_value
    FROM
        kg_statements s
    JOIN
        kg_virt_entity_map m ON (s.company_id = m.base_entity_id OR m.base_entity_id = ANY(s.subject_entities))
    WHERE
        m.virt_entity_id = %s
        AND s.start_year BETWEEN %s AND %s
        AND s.demise_embedding IS NOT NULL
        AND (s.is_environmental OR s.is_social OR s.is_governance OR s.is_animal_welfare)
        AND (s.statement_category = 'action' OR s.statement_category = 'event')
        AND ABS(impact_value) > 0.05
    ORDER BY
        s.start_year, s.id
    """

    # Create a new cursor with dict_row factory
    with cursor.connection.cursor(row_factory=dict_row) as dict_cursor:
        dict_cursor.execute(query, (virtual_entity_id, start_year, end_year))
        rows = dict_cursor.fetchall()

    # Group by year
    statements_by_year = {}
    for row in rows:
        year = row["year"]
        if year not in statements_by_year:
            statements_by_year[year] = []
        statements_by_year[year].append(row)

    return statements_by_year


def extract_dso_components(demise_embedding: Any) -> Tuple[List[float], List[float], List[float]]:
    """
    Extract Domain, Subject, and Object components from a DEMISE embedding.

    Args:
        demise_embedding: Full DEMISE embedding (can be a list, array, or string representation)

    Returns:
        Tuple of (domain_vector, subject_vector, object_vector)
    """
    # Ensure all values are floats

    # Handle string representation of arrays
    if isinstance(demise_embedding, str):
        # Remove brackets and split by commas
        if demise_embedding.startswith('[') and demise_embedding.endswith(']'):
            demise_embedding = demise_embedding[1:-1]

        # Split by commas and convert to floats
        float_embedding = [float(val.strip()) for val in demise_embedding.split(',')]
    else:
        # Convert all values to float
        float_embedding = [float(val) for val in demise_embedding]

    # Convert to numpy array for easier manipulation
    embedding_array = np.array(float_embedding)

    # Create a temporary DEMISEModel to extract components
    demise_model = DEMISEModel.from_np(embedding_array)

    # Extract domain, subject, and object vectors
    domain_vector = demise_model.domain.to_vector()
    subject_vector = demise_model.subject.to_vector()
    object_vector = demise_model.object.to_vector()

    return domain_vector, subject_vector, object_vector

def extract_core_domain_keys(domain_centroid: List[float], threshold: float = 0.7) -> List[str]:
    """
    Extract core domain keys and their descriptions from a domain centroid vector.

    Core domain keys are those with values above the threshold, plus the top 3 values
    regardless of threshold.

    Args:
        domain_centroid: Domain centroid vector
        threshold: Threshold for considering a domain key as core (default: 0.7)

    Returns:
        Tuple of (core_domain_keys, core_domain_descriptions)
    """
    try:
        domain_model = cast(DomainModel,DomainModel.from_vector(domain_centroid))

        # Get all domain keys and values as a dictionary
        domain_dict = domain_model.to_kv()

        # Sort by value in descending order
        sorted_items = sorted(domain_dict.items(), key=lambda x: x[1], reverse=True)

        # Get keys above threshold and top 3 keys
        core_keys = set()

        # First add top 3 keys
        for key, value in sorted_items[:3]:
            if key not in core_keys:
                core_keys.add(key)

        # Then add any other keys above threshold
        for key, value in sorted_items:
            if value >= threshold and key not in core_keys:
                core_keys.add(key)

        return list(core_keys)

    except Exception as e:
        logger.error(f"Error extracting core domain keys: {e}")
        return []


def get_domain_key_description(key: str) -> str:
    """
    Get a human-readable description for a domain key.

    Args:
        key: Domain key in the format 'domain.category.field'

    Returns:
        Human-readable description of the domain key
    """
    try:
        # Create a temporary DomainModel to get descriptions
        from eko.models.vector.demise.domain import DomainModel
        domain_model = DomainModel.model_construct()

        # Split the key into parts
        parts = key.split('.')

        # Handle different key formats
        if len(parts) == 3:  # domain.category.field
            category, field = parts[1], parts[2]

            # Navigate to the right category
            if hasattr(domain_model, category):
                category_model = getattr(domain_model, category)

                # Get the field description
                if hasattr(category_model, field):
                    field_info = category_model.model_fields.get(field)
                    if field_info and field_info.description:
                        return field_info.description
                    elif field_info and field_info.title:
                        return field_info.title

        # Fallback: return the key itself
        return key

    except Exception as e:
        logger.error(f"Error getting domain key description for {key}: {e}")
        return key


def create_dso_clusters(
    virtual_entity: VirtualEntityExpandedModel,
    start_year: int,
    end_year: int,
    trace_collector: Optional[TraceCollector] = None,
    run_id: Optional[int] = None
) -> List[DSO_ClusterModel]:
    """
    Create Domain+Subject+Object clusters for a virtual entity.

    Args:
        virtual_entity: Virtual entity model
        start_year: Start year (inclusive)
        end_year: End year (inclusive)
        trace_collector: Optional trace collector
        run_id: Optional run ID, if not provided a new run will be created

    Returns:
        List of DSO cluster models
    """
    if trace_collector:
        trace_collector.start_step("temporal_clustering")

    logger.info(f"Creating DSO clusters for {virtual_entity.name} ({start_year}-{end_year})")

    created_clusters = []

    with get_bo_conn() as conn:
        # Create a run if not provided
        if run_id is None:
            from eko.db.data.run import RunData
            run = RunData.create_predictive_run(
                conn=conn,
                start_year=start_year,
                end_year=end_year,
                target=virtual_entity.short_id
            )
            run_id = run.id
            logger.info(f"Created new run with ID {run_id}")

        with conn.cursor() as cursor:
            # Get statements grouped by year
            statements_by_year = get_statements_by_year(
                cursor, virtual_entity.id, start_year, end_year
            )

            # Process each year
            for year, statements in statements_by_year.items():
                if len(statements) < settings.predictive_statement_clustering_min_samples:
                    logger.info(f"Skipping year {year} - not enough statements ({len(statements)})")
                    continue

                logger.info(f"Processing year {year} with {len(statements)} statements")

                # Extract embeddings for clustering
                statement_ids = [s["id"] for s in statements]

                # Extract Domain+Subject+Object components for clustering
                dso_vectors = []
                valid_statement_ids = []

                for i, statement in enumerate(statements):
                    demise= DEMISEModel.model_validate(statement["model_json"])
                    try:

                        dso= demise.get_dso_vector()
                        # Combine the vectors for clustering
                        dso_vector = np.array(dso)
                        dso_vectors.append(dso_vector)
                        valid_statement_ids.append(statement["id"])
                    except Exception as e:
                        logger.warning(f"Error processing statement {statement['id']}: {e}")

                # Update statement_ids to only include valid ones
                statement_ids = valid_statement_ids

                if not dso_vectors:
                    logger.warning(f"No valid DSO vectors found for year {year}")
                    continue

                # Check if all vectors have the same length
                vector_lengths = [len(v) for v in dso_vectors]
                if len(set(vector_lengths)) > 1:
                    logger.warning(f"Inconsistent vector lengths: {set(vector_lengths)}")

                    # Find the most common length
                    from collections import Counter
                    length_counts = Counter(vector_lengths)
                    most_common_length = length_counts.most_common(1)[0][0]

                    # Filter to only include vectors of the most common length
                    filtered_vectors = []
                    filtered_ids = []
                    for i, v in enumerate(dso_vectors):
                        if len(v) == most_common_length:
                            filtered_vectors.append(v)
                            filtered_ids.append(statement_ids[i])

                    dso_vectors = filtered_vectors
                    statement_ids = filtered_ids

                    logger.info(f"Filtered to {len(dso_vectors)} vectors with length {most_common_length}")

                # Convert to numpy array
                try:
                    dso_array = np.array(dso_vectors)
                except ValueError as e:
                    logger.error(f"Failed to create numpy array: {e}")
                    logger.debug(f"Vector shapes: {[v.shape if hasattr(v, 'shape') else len(v) for v in dso_vectors]}")
                    continue


                from sklearn.cluster import DBSCAN
                n_samples = dso_array.shape[0]


                # Apply DBSCAN with the provided parameters
                dbscan = DBSCAN(eps=settings.predictive_statement_clustering_eps, min_samples=settings.predictive_statement_clustering_min_samples, metric='cosine')
                clusters = dbscan.fit_predict(dso_array)

                n_clusters = len(set(clusters)) - (1 if -1 in clusters else 0)


                # from sklearn.cluster import KMeans
                # kmeans = KMeans(n_clusters=min(8, n_samples), random_state=42, n_init="auto")
                # clusters = kmeans.fit_predict(dso_array)
                # n_clusters = len(set(clusters))

                # Process each cluster
                for cluster_idx in range(n_clusters):
                    # Get statements in this cluster
                    cluster_mask = clusters == cluster_idx
                    cluster_statement_ids = [statement_ids[i] for i, is_in_cluster in enumerate(cluster_mask) if is_in_cluster]

                    if len(cluster_statement_ids) < settings.predictive_statement_clustering_min_samples:
                        logger.debug(f"Skipping small cluster with {len(cluster_statement_ids)} statements")
                        continue

                    # Calculate cluster centroids for each component
                    cluster_dso_vectors = dso_array[cluster_mask]

                    # Calculate separate centroids for domain, subject, and object
                    domain_centroid = np.mean(cluster_dso_vectors[:, :DOMAIN_MODEL_VECTOR_SIZE], axis=0).tolist()
                    subject_centroid = np.mean(cluster_dso_vectors[:, DOMAIN_MODEL_VECTOR_SIZE:DOMAIN_MODEL_VECTOR_SIZE+ENTITY_MODEL_VECTOR_SIZE], axis=0).tolist()
                    object_centroid = np.mean(cluster_dso_vectors[:, DOMAIN_MODEL_VECTOR_SIZE+ENTITY_MODEL_VECTOR_SIZE:DOMAIN_MODEL_VECTOR_SIZE+ENTITY_MODEL_VECTOR_SIZE*2], axis=0).tolist()

                    # Calculate coherence (average distance to centroid)
                    distances = np.linalg.norm(cluster_dso_vectors - np.mean(cluster_dso_vectors, axis=0), axis=1)
                    coherence = float(np.mean(distances))

                    # Create DSO cluster model
                    # At this point, run_id should not be None due to our earlier check
                    assert run_id is not None, "run_id is None when creating DSO_ClusterModel"

                    cluster_model = DSO_ClusterModel(
                        run_id=run_id,
                        virtual_entity_id=virtual_entity.id,
                        year=year,
                        domain_centroid=domain_centroid,
                        subject_centroid=subject_centroid,
                        object_centroid=object_centroid,
                        statement_ids=sorted(cluster_statement_ids),
                        size=len(cluster_statement_ids),
                        coherence=coherence
                    )

                    # Save to database
                    created_cluster = DSO_ClusterDAO.create(cursor, cluster_model)
                    created_clusters.append(created_cluster)

                    logger.info(f"Created DSO cluster for year {year} with {len(cluster_statement_ids)} statements")

            conn.commit()

    if trace_collector:
        trace_collector.end_step("temporal_clustering", result={
            "clusters_created": len(created_clusters),
            "years_processed": len(statements_by_year)
        })

    return created_clusters


def get_dso_clusters(
    cursor: Cursor[DictRow],
    virtual_entity_id: int,
    run_id: int
) -> List[DSO_ClusterModel]:
    """
    Get DSO clusters for a virtual entity.

    Args:
        cursor: Database cursor
        virtual_entity_id: Virtual entity ID
        run_id: Run ID

    Returns:
        List of DSO cluster models
    """
    return DSO_ClusterDAO.find_by_virtual_entity_and_run(cursor, virtual_entity_id, run_id)
