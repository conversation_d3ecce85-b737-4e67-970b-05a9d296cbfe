# Analysis v2 Module Documentation

This module contains the various analysis components for Eko.

## Main Components

### Effect Analysis

The effects analysis pipeline processes statements to generate effect instances and flags.

### Selective Highlighting Detection

Detects selective highlighting practices by companies:

1. **Cherry Picking**: When a company repeatedly promotes a few positive impacts while downplaying significant negative impacts in the same domain.
   - Identifies similar positive statements repeated across disclosures
   - Detects negative impacts in similar domain spaces that are given less prominence

2. **Flooding**: When a company uses multiple small positive impacts to distract from significant negative impacts.
   - Clusters small positive impacts in similar domains
   - Identifies when these clusters appear alongside significant negative impacts in the same domain

### Usage

```bash
# Run selective highlighting analysis for all entities
cd backoffice && uv run python main.py ana selective-highlighting analyze

# Run for a specific entity
cd backoffice && uv run python main.py ana selective-highlighting analyze --entity-id 1
# OR using entity name with fuzzy matching
cd backoffice && uv run python main.py ana selective-highlighting analyze --entity "Inflexion and Trust"

# Run only cherry picking detection
cd backoffice && uv run python main.py ana selective-highlighting analyze --no-flooding

# Run only flooding detection
cd backoffice && uv run python main.py ana selective-highlighting analyze --no-cherry-picking

# Dry run (don't save results)
cd backoffice && uv run python main.py ana selective-highlighting analyze --dry-run
```

### Technical Implementation

1. **Vector Processing**: The system processes vectors from the `domain_embedding` column in `kg_statements_v2`. These are stored as string representations of arrays like `[0.1,0.2,...]` and are parsed to numeric vectors for processing.

2. **Similarity Calculation**: Uses cosine similarity in the domain vector space to identify related statements.

3. **Database Tables**:
   - `ana_cherry`: Stores cherry picking instances with 1024-dimensional `domain_vector` column
   - `ana_flooding`: Stores flooding instances with 1024-dimensional `domain_vector` column

4. **Tracking**: Uses the new TraceCollector system with JSONB trace_data storage for comprehensive pipeline tracking.

### Configuration

Hyperparameters for selective highlighting detection are stored in `eko/config/settings.py` under the `SELECTIVE_HIGHLIGHTING` key:

```python
SELECTIVE_HIGHLIGHTING = {
    "cherry_picking": {
        "min_repeat_count": 2,                  # Minimum repeats across different documents
        "statement_similarity_threshold": 0.15,  # Maximum cosine distance for similar statements
        "domain_similarity_threshold": 0.25,     # Maximum cosine distance for related domains
        "negative_impact_threshold": -0.2,       # Maximum impact value for negative statements
        "positive_impact_threshold": 0.2         # Minimum impact value for positive statements
    },
    "flooding": {
        "min_positive_cluster_size": 3,          # Minimum size for a cluster of small positives
        "small_positive_threshold": 0.2,         # Maximum impact value for small positive statements
        "domain_similarity_threshold": 0.25,     # Maximum cosine distance for DBSCAN clustering
        "negative_impact_threshold": -0.3        # Maximum impact value for negative statements
    }
}
```

## Trace Collection System (TraceCollector)

**As of January 2025**, the analysis_v2 module uses a modern trace collection system that replaced the old
`TraceabilityTracker` approach.

### Architecture

**New System (TraceCollector):**

- **Location**: `eko.analysis_v2.trace_collector`
- **Storage**: JSONB `trace_data` columns in analysis tables with GIN indexes
- **Data Structure**: Comprehensive Pydantic models with source data, processing steps, relationships, and quality
  metrics
- **Integration**: Embedded directly in analysis functions, no separate tracker parameter passing

**Old System (Deprecated):**

- **Files**: `pipeline_tracker_extended.py`, `tracker_example.py` (legacy, not actively used)
- **Pattern**: Required `tracker` parameter passed through function chains
- **Storage**: Distributed across multiple `dash.trk_*` tables

### Database Schema

All analysis tables now include `trace_json JSONB` columns with GIN indexes:

```sql
-- Selective highlighting
ALTER TABLE ana_cherry ADD COLUMN trace_json JSONB;
ALTER TABLE ana_flooding ADD COLUMN trace_json JSONB;
CREATE INDEX CONCURRENTLY idx_ana_cherry_trace_json ON ana_cherry USING GIN (trace_json);
CREATE INDEX CONCURRENTLY idx_ana_flooding_trace_json ON ana_flooding USING GIN (trace_json);

-- Claims and promises analysis  
ALTER TABLE ana_claims ADD COLUMN trace_json JSONB;
ALTER TABLE ana_promises ADD COLUMN trace_json JSONB;
CREATE INDEX CONCURRENTLY idx_ana_claims_trace_json ON ana_claims USING GIN (trace_json);
CREATE INDEX CONCURRENTLY idx_ana_promises_trace_json ON ana_promises USING GIN (trace_json);
```

### Usage Pattern

**Creating and Using TraceCollector:**

```python
from eko.analysis_v2.trace_collector import create_trace_collector

# Create trace collector
trace_collector = create_trace_collector(str(run_id), "selective_highlighting")

# Track processing steps
trace_collector.start_step("statement_analysis")
# ... processing work ...
trace_collector.end_step("statement_analysis", 
                        result={"statements_processed": 150})

# Add source data
trace_collector.add_source_statements(statements)
trace_collector.add_source_effects(effects)

# Set quality metrics
trace_collector.set_quality_metrics(
    confidence_score=0.85,
    data_completeness=0.95,
    anomalies_detected=["positive statement in red flag"]
)

# Get trace data for storage
trace_json = trace_collector.get_trace_json()

# Store in DAO with trace data (trace data passed separately, not in model)
dao.create(conn, business_model, trace_json)
```

### Anomaly Detection

The TraceCollector includes built-in quality control via `detect_flag_anomalies()`:

```python
# Automatically detect quality issues
anomalies = trace_collector.detect_flag_anomalies(effect_flag)
# Returns: ["Positive impact statement (score: 0.7) in RED flag: statement text..."]
```

**Anomaly Types Detected:**

- Positive statements in RED flags (harm/negative effects)
- Negative statements in GREEN flags (benefit/positive effects)
- Impact score mismatches with flag type

### Migration Notes (January 2025)

**Critical Changes Made:**

1. **utils.py Fix**: Removed `tracker` parameter from `filter_valid_statements()` that was causing "Tracker must be
   provided from the top level" errors
2. **Function Signatures**: All analysis functions no longer require `tracker` parameters
3. **DAO Updates**: Updated to handle trace_json serialization/deserialization automatically
4. **Quality Preservation**: All tracking functionality preserved, including anomaly detection

**Files Updated During Migration:**

- `selective_highlighting/detection_pgvector.py` - Updated to use TraceCollector
- `selective_highlighting/merging.py` - Updated to use TraceCollector
- `heart/trust_and_reliability/claim_evidence.py` - Removed tracker usage
- `heart/trust_and_reliability/promise_analysis.py` - Removed tracker usage
- `effects/effect_flags.py` - Integrated anomaly detection with TraceCollector
- `effects/utils.py` - **Critical fix**: Removed tracker parameter requirement
- `prediction_v2/cli.py` - Updated to use new trace system

**Verification:**

- System self-test passes with 99 unit tests
- No remaining tracker references in active analysis code
- All quality control functionality preserved

### Trace Data Separation Principles (July 2025)

**CRITICAL ARCHITECTURAL RULE**: Trace data must NEVER be stored as fields in business model classes.

**❌ INCORRECT Pattern:**

```python
class BusinessModel(BaseModel):
    trace_data: Optional[Dict[str, Any]] = None  # DON'T DO THIS!
    
# Accessing trace data from model
if model.trace_data:
    process_trace(model.trace_data)  # WRONG!
```

**✅ CORRECT Pattern:**

```python
class BusinessModel(BaseModel):
    # Business fields only, NO trace_data field
    id: Optional[int] = None
    business_field: str
    
# Function signatures with trace_collector as separate parameter
def process_function(model: BusinessModel, trace_collector: Optional[TraceCollector] = None):
    if trace_collector:
        trace_collector.start_step("processing")
        # ... do work ...
        trace_json = trace_collector.get_trace_json()
        
# DAO methods accept trace data as separate parameter
def create(conn: Connection, model: BusinessModel, trace_json: Dict[str, Any]) -> BusinessModel:
    # Store trace data in database JSONB column, not in model
```

**Key Implementation Rules:**

1. **No trace_data fields in business models** - Models should only contain business logic fields
2. **Pass trace_collectors as function parameters** - Use `trace_collector: Optional[TraceCollector] = None`
3. **Store trace data via DAO parameters** - `dao.create(conn, model, trace_json)`
4. **Retrieve trace data from database** - Query `trace_json` JSONB columns when needed
5. **Use TraceCollector inheritance** - `child_collector.inherit_from(parent_collector.get_trace_json())`

**Database Storage:**

- All `ana_*` tables have `trace_json JSONB` columns with GIN indexes
- Trace data stored separately from business model serialization
- Efficient querying via PostgreSQL JSONB operators

**Function Signature Patterns:**

```python
# Effect flag processing
def _process_single_merged_flag(
    merged_flag: EffectFlagModel,
    effect_type: EffectType,
    virtual_entity: VirtualEntityExpandedModel,
    name: str,
    model_sections,
    run_id: int,
    trace_collector: Optional[TraceCollector] = None  # Separate parameter
) -> Optional[EffectFlagModel]:

# Flag merging
def merge_similar_effect_flags(
    effect_flags: List[EffectFlagModel], 
    trace_collector: Optional[TraceCollector] = None  # Separate parameter
) -> List[EffectFlagModel]:

# Data transfer
def convert_and_persist_xfer_flags(
    conn: Connection,
    effect_flags: List[EffectFlagModel],
    run_id: int,
    entity_xid: str,
    trace_data_map: Optional[Dict[int, Dict[str, Any]]] = None  # Separate parameter
) -> List[int]:
```

**Benefits of This Architecture:**

- Clean separation of concerns between business logic and debugging/audit data
- Prevents accidental serialization of large trace data in business contexts
- Maintains trace data integrity while keeping models lightweight
- Supports efficient database storage and querying
- Follows fail-fast principles with explicit parameter passing
