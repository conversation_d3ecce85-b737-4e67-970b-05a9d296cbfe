"""
Effect flag creation and processing.

This module provides functions for creating and processing effect flags.
"""

import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Optional

from loguru import logger
from psycopg import Connection

from eko.analysis_v2.citations import verify_analysis
# Import the rest of the functions from the effect_flags_helpers.py file
from eko.analysis_v2.effects.effect_flags_helpers import (
    merge_similar_effect_flags,
    process_effect_flag
)
from eko.analysis_v2.effects.model_section_assignment import assign_model_sections, get_model_sections
from eko.analysis_v2.trace_collector import TraceCollector, create_trace_collector
from eko.db.data.effect_flag import EffectFlagData
from eko.db.data.xfer import XferData
from eko.db import get_bo_conn
from eko.models.vector.derived.effect import EffectModel, EffectFlagModel
from eko.models.vector.derived.effect_type import EffectType
from eko.models.virtual_entity import VirtualEntityExpandedModel


def _process_single_effect_model(
    effect_model: EffectModel, effect_type: EffectType, run_id: int, virtual_entity: VirtualEntityExpandedModel
) -> List[EffectFlagModel]:
    """
    Worker function to process a single effect model.

    This function creates its own database connection to ensure thread safety.

    Args:
        effect_model: The effect model to process
        effect_type: The effect type (RED or GREEN)
        run_id: ID of the analysis run
        virtual_entity: The expanded virtual entity model

    Returns:
        List of EffectFlagModel objects
    """
    # Create a new database connection for this thread
    with get_bo_conn() as conn:
        statement_ids = [s.id for s in effect_model.statements if s.id]
        with conn.cursor() as cursor:
            flags = process_effect_flag(cursor, effect_model, statement_ids, effect_type, run_id, virtual_entity)
            return flags


def _process_single_merged_flag(
    merged_flag: EffectFlagModel,
    effect_type: EffectType,
    virtual_entity: VirtualEntityExpandedModel,
    name: str,
    model_sections,
    run_id: int,
    trace_collector: Optional[TraceCollector] = None
) -> Optional[EffectFlagModel]:
    """
    Worker function to process a single merged flag in post-merge processing.

    This function creates its own database connection to ensure thread safety.

    Args:
        merged_flag: The merged flag to process
        effect_type: The effect type (RED or GREEN)
        virtual_entity: The expanded virtual entity model
        name: Entity name for verification
        model_sections: Model sections for assignment
        run_id: Run ID for trace collection
        trace_collector: Optional trace collector for inheriting trace data

    Returns:
        Processed and stored EffectFlagModel, or None if verification failed
    """
    # Create a new database connection for this thread
    with get_bo_conn() as conn:
        harm_benefit = 'harm/bad' if effect_type == EffectType.RED else 'benefit/good'
        updated_flag = verify_analysis(
            merged_flag,
            name,
            min_citations=1,
            min_documents=1,
            addional_instructions=f"The analysis must be about the {harm_benefit} done by {virtual_entity.for_referencing_in_prompts()} and not any other entity. If the analysis is not about them doing {harm_benefit} return INVALID. The analysis must be about {virtual_entity.title}'s actions.",
        )
        if updated_flag is None:
            logger.warning(f"Flag {merged_flag.title} failed verification and will not be stored")
            return None

        # Assign model sections to the flag
        updated_flag = assign_model_sections(updated_flag, model_sections)
        logger.info(f"Assigned model sections to flag: {updated_flag.model_sections}")

        # Create or inherit trace collector for the flag
        if trace_collector is None:
            flag_trace_collector = create_trace_collector(str(run_id), "flag_creation")
        else:
            # Create child collector inheriting from parent
            flag_trace_collector = create_trace_collector(str(run_id), "flag_verification")
            flag_trace_collector.inherit_from(trace_collector.get_trace_json())
        
        flag_trace_collector.start_step("flag_verification")
        flag_trace_collector.end_step("flag_verification", 
                                result={"verified": True, "citations_count": len(updated_flag.citations)})
        flag_trace_collector.start_step("model_section_assignment")
        flag_trace_collector.end_step("model_section_assignment", 
                                result={"sections_assigned": len(updated_flag.model_sections)})
        flag_trace_collector.add_metadata("entity_name", virtual_entity.name)
        flag_trace_collector.add_metadata("effect_type", effect_type.value)
        flag_trace_collector.add_metadata("flag_title", updated_flag.title)
        flag_trace_collector.add_metadata("flag_id", updated_flag.id)
        trace_json = flag_trace_collector.get_trace_json()
        
        # Trace data will be stored separately via DAO parameter
        
        # Store the flag with model sections and trace data
        stored_flag = EffectFlagData.create(conn, updated_flag, trace_json)

        # Detect and record anomalies using the new trace system
        anomalies = flag_trace_collector.detect_flag_anomalies(stored_flag)
        if anomalies:
            logger.warning(f"Detected {len(anomalies)} anomalies in flag {stored_flag.id}")

        # Flag stored successfully
        logger.debug(f"Stored flag {stored_flag.id} with trace data")

        return stored_flag


def create_effect_flags_from_effects(
        conn: Connection,
        effects: List[EffectModel],
        run_id: int,
        virtual_entity: VirtualEntityExpandedModel,
        max_workers: int = 4,
        trace_collector: Optional['TraceCollector'] = None
) -> List[int]:
    """
    Create effect flags from effect models for an entity.

    Args:
        conn: Database connection
        effects: List of EffectModel objects to create flags for
        run_id: ID of the analysis run
        virtual_entity: The expanded virtual entity model
        max_workers: Maximum number of worker threads for parallel processing
        trace_collector: Optional trace collector for detailed tracking

    Returns:
        List of effect flag IDs
    """
    # Initialize trace collector if not provided
    if trace_collector is None:
        from eko.analysis_v2.trace_collector import create_trace_collector

        trace_collector = create_trace_collector(str(run_id), "effect_flags")

    # Extract entity name and virtual entity ID
    name = virtual_entity.name
    # We now use the virtual entity ID directly instead of base entities
    virt_entity_id = virtual_entity.id

    # Start timing overall flag creation
    start_time = time.time()

    # logger.info(effects)

    def process_effects(effect_type: EffectType) -> List[EffectFlagModel]:
        effect_models_by_type = [model for model in effects if model.effect_type == effect_type]
        if not effect_models_by_type:
            logger.info(f"No {effect_type.value} effect clusters found")

            # No effect models to process
            logger.debug(f"No {effect_type.value} effect models to process")

            return []

        logger.info(f"Processing {len(effect_models_by_type)} {effect_type.value} effect clusters with {max_workers} workers")

        # Starting processing for this effect type
        logger.debug(f"Starting processing of {len(effect_models_by_type)} {effect_type.value} effect models")

        effect_flags = []

        # Use ThreadPoolExecutor for parallel processing
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_model = {
                executor.submit(
                    _process_single_effect_model,
                    effect_model,
                    effect_type,
                    run_id,
                    virtual_entity
                ): effect_model for effect_model in effect_models_by_type
            }

            # Collect results as they complete
            for future in as_completed(future_to_model):
                effect_model = future_to_model[future]
                flags = future.result()
                effect_flags.extend(flags)
                logger.debug(f"Completed processing effect model {effect_model.id}, got {len(flags)} flags")

        # Completed processing for this effect type
        logger.debug(f"Completed processing {len(effect_models_by_type)} {effect_type.value} effect models, created {len(effect_flags)} flags")

        return effect_flags

    def merge_flags(flags: List[EffectFlagModel], effect_type: EffectType, max_workers: int = 4):
        if not flags:
            return []

        merge_start_time = time.time()
        logger.info(f"Processing {len(flags)} {effect_type.value} effect flags for merging")

        # Starting merging process
        logger.debug(f"Starting merge of {len(flags)} {effect_type.value} flags")

        if len(flags) > 1:
            merged_flags = merge_similar_effect_flags(flags)
        else:
            merged_flags = flags

        merge_time_ms = int((time.time() - merge_start_time) * 1000)
        logger.info(f"Merged {len(flags)} {effect_type.value} effect flags into {len(merged_flags)}")

        # Merging completed
        logger.debug(f"Merged {len(flags)} {effect_type.value} flags into {len(merged_flags)} in {merge_time_ms}ms")

        # Process the merged flags in parallel
        store_start_time = time.time()
        stored_flags = []
        model_sections = get_model_sections()

        logger.info(f"Processing {len(merged_flags)} merged {effect_type.value} flags with {max_workers} workers")

        # Use ThreadPoolExecutor for parallel post-merge processing
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_flag = {
                executor.submit(
                    _process_single_merged_flag,
                    merged_flag,
                    effect_type,
                    virtual_entity,
                    name,
                    model_sections,
                    run_id
                ): merged_flag for merged_flag in merged_flags
            }

            # Collect results as they complete
            for future in as_completed(future_to_flag):
                merged_flag = future_to_flag[future]
                stored_flag = future.result()
                if stored_flag is not None:
                    stored_flags.append(stored_flag)
                    logger.debug(f"Completed processing merged flag {merged_flag.title}")
                else:
                    logger.debug(f"Merged flag {merged_flag.title} was not stored (failed verification)")

        store_time_ms = int((time.time() - store_start_time) * 1000)

        # Flag storage completed
        logger.debug(f"Stored {len(stored_flags)} {effect_type.value} flags in {store_time_ms}ms")

        return stored_flags

    # Process red flags
    red_start_time = time.time()
    red_flags = process_effects(EffectType.RED)
    red_stored_flags = merge_flags(red_flags, EffectType.RED, max_workers)
    red_processing_time_ms = int((time.time() - red_start_time) * 1000)

    # Process green flags
    green_start_time = time.time()
    green_flags = process_effects(EffectType.GREEN)
    green_stored_flags = merge_flags(green_flags, EffectType.GREEN, max_workers)
    green_processing_time_ms = int((time.time() - green_start_time) * 1000)

    # Combine all flags
    all_effect_flags = red_stored_flags + green_stored_flags
    flag_ids = [flag.id for flag in all_effect_flags if flag.id]

    # Overall flag creation completed
    total_processing_time_ms = int((time.time() - start_time) * 1000)
    logger.info(f"Created {len(red_stored_flags)} red flags and {len(green_stored_flags)} green flags in {total_processing_time_ms}ms")

    # Convert effect flags to XferEffectFlagModel and persist to xfer_flags
    xfer_start_time = time.time()

    # Gather trace data for all flags
    trace_data_map = {}
    for flag in all_effect_flags:
        if flag.id:
            # Retrieve trace data from database for this flag
            with conn.cursor() as cursor:
                cursor.execute("SELECT trace_json FROM ana_effect_flags WHERE id = %s", (flag.id,))
                row = cursor.fetchone()
                if row and row[0]:
                    trace_data_map[flag.id] = row[0]
                else:
                    trace_data_map[flag.id] = {}
    
    # Convert and persist all effect flags
    logger.info(f"Converting and persisting {len(all_effect_flags)} effect flags to xfer_flags")
    persisted_ids = XferData.convert_and_persist_xfer_flags(conn, all_effect_flags, run_id, virtual_entity.short_id, trace_data_map)

    xfer_processing_time_ms = int((time.time() - xfer_start_time) * 1000)

    # Transfer process completed
    logger.info(f"Transferred {len(persisted_ids)} flags to xfer_flags in {xfer_processing_time_ms}ms")

    # Flag creation pipeline completed
    logger.info(f"Completed flag creation pipeline for {virtual_entity.name}")

    logger.info(f"Created a total of {len(flag_ids)} effect flags, transferred {len(persisted_ids)} to xfer_flags")
    return flag_ids
