"""
Promise Analysis module for finding and analyzing promises made by entities.

This module handles searching for promises that have been made by companies
in their ESG statements and determining if they were kept or broken.
"""
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

import os
import re
import traceback
from loguru import logger
from psycopg import Cursor
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional, cast

from eko import eko_var_path
from eko.analysis_v2.citations import valid_citations
from eko.analysis_v2.heart.trust_and_reliability.claims_and_promises import process_entity_analysis, MIN_EVIDENCE_COUNT, \
    PromiseModel, search_for_statements, MAX_EVIDENCE_COUNT, BaseVerdict, \
    BaseEvidence
from eko.analysis_v2.heart.trust_and_reliability.merging import merge_similar_promises
from eko.analysis_v2.heart.trust_and_reliability.promise_report import generate_promise_report
from eko.db import get_bo_conn
from eko.db.data.run import RunData
from eko.db.data.statement import StatementData
from eko.db.data.xfer import XferData
from eko.entities.queries import get_entity_by_id
from eko.llm import LLMModel
from eko.llm.main import call_llms_typed, LLMOptions
from eko.models.statement_metadata import StatementAndMetadata
from eko.models.vector.demise.demise_model import DEMISEModel
from eko.models.vector.derived.enums import PipelineStage
from eko.models.virtual_entity import VirtualEntityExpandedModel
from eko.nlp.util import extract_eko_citations

# Define output directory for debug information
OUTPUT_DIR = os.path.join(eko_var_path, "promises_analysis")
os.makedirs(OUTPUT_DIR, exist_ok=True)


class PromiseEvidence(BaseEvidence):
    """Model for evidence related to a promise."""
    fulfills_promise: bool = False



class PromiseVerdict(BaseVerdict):
    """Model for a verdict on a promise."""

    id: Optional[int] = Field(None, description="The database ID of this verdict")
    promise_kept: bool
    promise_doc: str = ""  # Use doc_title from BaseVerdict
    evidence: Dict[str, Any] = {}
    statement: Optional[StatementAndMetadata] = Field(None, description="The statement the promise is about")
    text: str = Field("", description="Promise text content")
    context: Optional[str] = Field(None, description="Context for the promise")
    llm_greenwashing: bool = Field(False, description="LLM greenwashing assessment")
    company_id: int = Field(0, description="Company ID associated with the promise")
    esg_promise: bool = Field(True, description="Whether this is an ESG promise")

    @property
    def verdict_for(self) -> str:
        """Returns a string representation of the verdict for tracking."""
        if self.promise_kept:
            return "PROMISE_KEPT"
        else:
            return "PROMISE_BROKEN"


class EvidenceSummaryResponse(BaseModel):
    """Pydantic model for evidence summarization LLM response."""
    fulfillment: str = Field(..., description="Whether the evidence shows the promise was fulfilled or unfulfilled")
    confidence: int = Field(..., ge=1, le=100, description="Confidence score from 1-100")
    summary: str = Field(..., description="Summary of the evidence with citations")


class PromiseVerdictResponse(BaseModel):
    """Pydantic model for promise verdict LLM response."""

    promise_kept: str = Field(..., description="Whether the promise was kept (YES, NO, UNKNOWN, or IRRELEVANT)")
    greenwashing: str = Field(..., description="Whether the promise constitutes greenwashing (YES, NO, or UNKNOWN)")
    confidence: int = Field(..., ge=1, le=100, description="Confidence score from 1-100")
    verdict: str = Field(..., description="Detailed verdict with reasoning and citations")
    summary: str = Field(..., description="Concise summary of the verdict")
    conclusion: str = Field(..., description="Single sentence conclusion")


def find_promises(cur: Cursor, virtual_entity: VirtualEntityExpandedModel, start_year: int, end_year: int) -> List[PromiseModel]:
    """
    Find statements that are promises from the given virtual entity.

    Args:
        cur: Database cursor
        virtual_entity: VirtualEntityExpandedModel to find promises for
        start_year: Start year for promises
        end_year: End year for promises

    Returns:
        List of promises as PromiseModel objects
    """
    logger.info(f"Finding promises for virtual entity {virtual_entity.name} from {start_year} to {end_year}")

    # Get a list of base entity IDs from the virtual entity
    entity_ids = [entity.id for entity in virtual_entity.base_entities]

    # If there are no entity IDs, return an empty list
    if not entity_ids:
        logger.warning("No entity IDs found for virtual entity")
        return []

    # Create a parameterized query with placeholders for each entity ID
    entity_ids_str = ','.join(str(id) for id in entity_ids)

    # Build the query with proper parameterization
    # Using a string format for the entity IDs part, but parameterized queries for the rest
    query = f"""
        WITH entities AS (SELECT id FROM kg_base_entities WHERE id IN ({entity_ids_str}))
        SELECT
            kg_statements.id AS statement_id,
            kg_statements.statement_text AS text,
            kg_statements.context AS context,
            kg_statements.doc_id,
            COALESCE(kg_statements.start_year, doc.year) AS doc_year,
            kg_statements.model_json,
            doc.title AS doc_title,
            (select array_agg(ent.id) from kg_base_entities ent JOIN kg_document_authors a ON ent.id = a.entity_id WHERE a.doc_id=doc.id ) as doc_authors,
            kg_statements.start_year,  -- Explicitly include start_year
            kg_statements.end_year     -- Explicitly include end_year
        FROM kg_statements
        JOIN entities ON (kg_statements.company_id = entities.id OR entities.id = ANY(kg_statements.subject_entities) OR entities.id = ANY(kg_statements.object_entities))
        JOIN kg_documents doc ON kg_statements.doc_id = doc.id
        WHERE (kg_statements.is_animal_welfare  OR kg_statements.is_environmental OR kg_statements.is_social OR kg_statements.is_governance)
            AND 'disclosure' = ANY(doc.research_categories)
            AND kg_statements.statement_category='promise'
            AND (
                -- Use statement year range if available
                (kg_statements.start_year IS NOT NULL AND
                 -- Statements overlap with the requested time range if:
                 -- NOT (end_year < start_range OR start_year > end_range)
                 NOT (COALESCE(kg_statements.end_year, kg_statements.start_year) < %s OR kg_statements.start_year > %s))
                OR
                -- Fallback to document year
                (kg_statements.start_year IS NULL AND doc.year BETWEEN %s AND %s)
            )
        ORDER BY COALESCE(kg_statements.start_year, doc.year) ASC, kg_statements.id ASC
    """

    # Execute with just the year parameters (entity IDs are now in the query string)
    params = [start_year, end_year, start_year, end_year]
    try:
        cur.execute(query, params)
    except Exception as e:
        logger.exception(f"Error executing query: {e}")
        return []

    all_statements = cur.fetchall()
    logger.info(f"Found {len(all_statements)} total statements")

    # Filter for statements that are promises
    promises = []
    for statement in all_statements:
        # Unpack only the first 8 values we need, ignoring any additional values
        # The query returns more columns than we're unpacking here
        statement_id = statement[0]
        text = statement[1]
        context = statement[2]
        doc_id = statement[3]
        doc_year = statement[4]
        model_json = statement[5]
        doc_title = statement[6]
        doc_authors = statement[7]

        if not model_json:
            logger.warning(f"Statement {statement_id} has no model JSON")
            continue

        # Create a DEMISE model instance
        model = DEMISEModel.from_kv(model_json)
        # Cast to the correct type to avoid type checking issues
        demise_model = cast(DEMISEModel, model)
        logger.info(demise_model.statement.to_kv_sparse())

        # Get statement_start_year and statement_end_year from the database if available
        # The query now returns start_year and end_year in selection
        statement_start_year = statement[8] if len(statement) > 8 else None  # Assuming start_year is at index 8
        statement_end_year = statement[9] if len(statement) > 9 else None    # Assuming end_year is at index 9

        promises.append(PromiseModel(
            statement_id=statement_id,
            statement=StatementData.get_by_id(cur.connection,statement_id),
            text=text,
            context=context,
            doc_id=doc_id,
            doc_year=doc_year,
            start_year=statement_start_year,  # Add start_year from the database
            end_year=statement_end_year,      # Add end_year from the database
            doc_title=doc_title,
            doc_authors=[get_entity_by_id(i) for i in doc_authors] if doc_authors else [],
            demise_model=model,
            virtual_entity_id=virtual_entity.id,
            virtual_entity_short_id=virtual_entity.short_id,
            virtual_entity=virtual_entity
        ))


    logger.info(f"Found {len(promises)} promises out of {len(all_statements)} statements")
    return promises


def search_for_evidence(
    cur: Cursor,
    virtual_entity: VirtualEntityExpandedModel,
    promise: PromiseModel,
    max_evidence: int = MAX_EVIDENCE_COUNT
) -> List[PromiseEvidence]:
    """
    Search for evidence related to a promise.

    Args:
        cur: Database cursor
        virtual_entity: VirtualEntityExpandedModel the promise is from
        promise: Promise to search evidence for
        max_evidence: Maximum number of evidence items to return

    Returns:
        List of PromiseEvidence items
    """
    promise_year = promise.get_year()  # Use get_year() which prefers start_year if available
    promise_id = promise.statement_id
    promise_model = promise.demise_model

    logger.info(f"Searching for evidence related to promise {promise_id} from year {promise_year}")

    # Get the promise vector using the DEMISE model
    promise_vector = promise_model.get_promise_vector()

    # Search for statements about this virtual entity from after the promise year
    similar_items = search_for_statements(
        cur=cur,
        virtual_entity=virtual_entity,
        statement_id=promise_id,
        doc_year=promise_year,  # Parameter is still named doc_year for compatibility
        vector=promise_vector,
        year_comparison="> %s",  # Look for statements after the promise year
        max_items=max_evidence
    )

    # Convert to PromiseEvidence objects
    evidence_items = [
        PromiseEvidence(
            statement_id=item['statement_id'],
            doc_id=item['doc_id'],
            doc_page_id=item['doc_page_id'],
            text=item['text'],
            extended_text=item['extended_text'],
            context=item['context'],
            doc_year=item['doc_year'],
            relevance_score=item['relevance_score']
        )
        for item in similar_items
    ]

    return evidence_items


def filter_evidence(
    cur: Cursor,
    virtual_entity: VirtualEntityExpandedModel,
    promise: PromiseModel,
    evidence_items: List[PromiseEvidence]
) -> List[PromiseEvidence]:
    """
    Use LLM to filter evidence items for relevance and summarize them.

    Args:
        cur: Database cursor
        virtual_entity: VirtualEntityExpandedModel the promise is from
        promise: The promise being evaluated
        evidence_items: List of evidence items to filter

    Returns:
        List of filtered and summarized evidence items
    """
    if not evidence_items:
        return []

    promise_text = promise.text
    promise_context = promise.context or ""

    logger.info(f"Filtering and summarizing {len(evidence_items)} evidence items")

    filtered_evidence = []

    # Process evidence items in parallel
    with ThreadPoolExecutor(max_workers=8) as executor:
        # Create futures for summarization tasks
        future_to_evidence = {
            executor.submit(
                summarize_and_verify_evidence,
                cur,
                virtual_entity,
                promise_text,
                promise_context,
                evidence
            ): evidence
            for evidence in evidence_items
        }

        # Process results as they complete
        for future in as_completed(future_to_evidence):
            original_evidence = future_to_evidence[future]
            try:
                result = future.result()
                if result:
                    filtered_evidence.append(result)
            except Exception as e:
                logger.error(f"Error processing evidence {original_evidence.statement_id}: {e}")

    # Sort by relevance score
    filtered_evidence.sort(key=lambda x: x.relevance_score, reverse=True)

    logger.info(f"After filtering, have {len(filtered_evidence)} relevant evidence items")
    return filtered_evidence


def summarize_and_verify_evidence(
    cur: Cursor,  # Now used for expanding citations
    virtual_entity: VirtualEntityExpandedModel,
    promise_text: str,
    promise_context: str,
    evidence: PromiseEvidence
) -> Optional[PromiseEvidence]:
    """
    Summarize evidence and verify its relevance to the promise.

    Args:
        _cur: Database cursor (not used but kept for API compatibility)
        virtual_entity: VirtualEntityExpandedModel the promise is from
        promise_text: The promise text
        promise_context: The promise context
        evidence: Evidence to summarize and verify

    Returns:
        Updated evidence item or None if not relevant
    """
    try:
        # Sanitize text to remove any mentions of other entities
        # Using the first base entity for compatibility with the old function
        base_entity = virtual_entity.base_entities[0] if virtual_entity.base_entities else None
        if not base_entity:
            return None

        sanitized_text = evidence.extended_text

        # Create prompt for summarization and relevance check
        system_prompt = """You are an expert analyst examining corporate promises and evidence.
        You are not conversational, please do not include any preamble or discursive elements."""

        user_prompt = f"""<promise>{promise_text}</promise>
        <promise_context>{promise_context}</promise_context>

        <evidence year="{evidence.doc_year}">
        {sanitized_text}
        </evidence>

        First determine if this evidence from {evidence.doc_year} is directly relevant to the promise.
        The evidence must specifically discuss the same topic as the promise and be about {virtual_entity.name}.

        If the evidence is relevant:
        1. Provide a concise summary (300-500 words) of how the evidence relates to the promise.
        2. Include all relevant quotes, dates, quantities, and specifics from the evidence.
        3. Determine if the evidence shows that the promise was fulfilled or remains unfulfilled.

        If the evidence is not relevant to the promise, simply respond with "NOT RELEVANT".
        """

        # Call the LLM
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

        try:
            # Use call_llms_typed with the EvidenceSummaryResponse model
            response = call_llms_typed(
                [LLMModel.NORMAL_CHEAP],
                messages,
                16000,
                response_model=EvidenceSummaryResponse,
                options=LLMOptions(
                    temperature=0.2,
                    escalate_to=[LLMModel.NORMAL_HQ],
                    thinking=True,
                    metadata={"label": "summarize-promise-evidence"},
                ),
            )

            # If we get here, we have a valid response
            # Update the evidence object with the response fields
            evidence.llm_summary = response.summary
            evidence.fulfills_promise = response.fulfillment == "FULFILLED"
            evidence.confidence = response.confidence

            # If confidence is too low, consider not relevant
            if evidence.confidence < 70:  # Using constant from claims_and_promises
                return None

            return evidence

        except Exception as inner_e:
            # If we get an exception, it might be because the evidence is not relevant
            # Check if the response contains "NOT RELEVANT"
            logger.warning(f"Error in LLM response processing: {inner_e}")
            return None

    except Exception as e:
        logger.exception(f"Error in summarize_and_verify_evidence: {e}")
        return None


def determine_verdict(
    cur: Cursor,
    virtual_entity: VirtualEntityExpandedModel,
    promise: PromiseModel,
    evidence_items: List[PromiseEvidence],
    run_id: int,
    end_year: int
) -> Optional[PromiseVerdict]:
    """
    Determine whether the promise was kept based on the evidence.

    Args:
        cur: Database cursor
        virtual_entity: VirtualEntityExpandedModel the promise is from
        promise: The promise being evaluated
        evidence_items: List of evidence items
        run_id: Run ID
        end_year: End year for the analysis period

    Returns:
        Verdict or None if can't determine
    """
    # Check if the promise has a target date in the future (beyond end_year)
    # First, try to extract target year from the promise text
    promise_text = promise.text
    promise_context = promise.context or ""
    target_year = None

    # Look for years in the format "by 20XX" or "by the year 20XX" or similar patterns
    year_patterns = [
        r'by\s+(\d{4})',
        r'by\s+the\s+year\s+(\d{4})',
        r'by\s+end\s+of\s+(\d{4})',
        r'until\s+(\d{4})',
        r'in\s+(\d{4})',
        r'reach.*by\s+(\d{4})',
        r'achieve.*by\s+(\d{4})',
        r'complete.*by\s+(\d{4})',
        r'target.*(\d{4})',
        r'goal.*(\d{4})'
    ]

    combined_text = f"{promise_text} {promise_context}"
    for pattern in year_patterns:
        matches = re.findall(pattern, combined_text, re.IGNORECASE)
        if matches:
            # Take the latest year mentioned as the target
            years = [int(year) for year in matches if 2000 <= int(year) <= 2100]
            if years:
                target_year = max(years)
                break

    # If we found a target year and it's beyond the end_year, skip this promise
    if target_year and target_year > end_year:
        logger.info(f"Skipping promise with target year {target_year} which is beyond analysis end year {end_year}")
        return None

    # Also check if the promise's end_year field (if set) is beyond the analysis end_year
    if promise.end_year and promise.end_year > end_year:
        logger.info(f"Skipping promise with end_year {promise.end_year} which is beyond analysis end year {end_year}")
        return None

    if not evidence_items or len(evidence_items) < MIN_EVIDENCE_COUNT:
        logger.info(f"Not enough evidence ({len(evidence_items)}) to determine verdict")
        return None

    promise_text = promise.text
    promise_context = promise.context or ""
    promise_id = promise.statement_id

    # Prepare evidence for the LLM
    evidence_blocks = []
    for i, evidence in enumerate(evidence_items):
        evidence_blocks.append(
            f"""<evidence page_id="{evidence.doc_page_id}" year="{evidence.doc_year}" {'fulfills="yes"' if evidence.fulfills_promise else 'fulfills="no"'}>
            <text>{evidence.text}</text>
            <summary>{evidence.llm_summary}</summary>
            </evidence>"""
        )

    evidence_text = "\n\n".join(evidence_blocks)

    # Create the verdict prompt
    system_prompt = f"""You are an expert ESG analyst evaluating corporate promises against evidence.
    You are not conversational, please do not include any preamble or discursive elements.

    IMPORTANT: 
    1. If the promise has a target date in the future (beyond {end_year}), you should indicate this in your analysis.
       Promises with future target dates should not be evaluated as broken, as there is still time to fulfill them.
    2. If the evidence provided is inconclusive, contradictory, or insufficient to determine whether the promise was kept or broken,
       you MUST respond with "UNKNOWN" for promise_kept. Do not guess or make assumptions."""

    user_prompt = f"""<promise>{promise_text}</promise>
    <promise_context>{promise_context}</promise_context>
    <promise_year>{promise.doc_year}</promise_year>
    <analysis_end_year>{end_year}</analysis_end_year>

    <note>{virtual_entity.title} is also known as {virtual_entity.aka}</note>

    Here is the evidence related to this promise by {virtual_entity.title}:

    {evidence_text}

    First, check if the promise has a specific target date or deadline. If the target date is beyond {end_year}, note this in your analysis and do not evaluate it as broken, as there is still time to fulfill it.

    Analyze the evidence to determine whether the promise was kept and whether it constitutes greenwashing.

    Rules:
    1. The promise is considered kept if there is concrete evidence of its fulfillment.
    2. The promise constitutes greenwashing if it misleadingly exaggerates environmental benefits or achievements.
    3. Consider the timeframe - if the promise had a specific target date that hasn't been reached yet, the promise can't be judged as broken.
    4. Include citations from the evidence in your analysis using the format [^page_id] you can get page_id from the <evidence> tag.
    5. Your verdict should be thorough (800-1200 words) and well-reasoned.
    6. All conclusions must be based solely on the evidence provided.
    7. If the evidence is inconclusive or insufficient to determine whether the promise was kept or broken, respond with "UNKNOWN" for `promise_kept`.

    Response guidelines for `promise_kept`:
    - "YES": The promise was clearly kept based on the evidence
    - "NO": The promise was clearly broken based on the evidence
    - "UNKNOWN": The evidence is inconclusive, contradictory, or insufficient to determine if the promise was kept or broken
      Examples of when to use UNKNOWN:
      • Evidence mentions the topic but doesn't confirm or deny fulfillment
      • Evidence is too vague to determine actual progress
      • No evidence directly addresses the specific promise made
      • Contradictory evidence that doesn't clearly lean one way
    - "IRRELEVANT": This is not a promise, not made by {virtual_entity.title}, or has a target date beyond {end_year}

    For `greenwashing`:
    - "YES": The promise clearly constitutes greenwashing
    - "NO": The promise does not constitute greenwashing
    - "UNKNOWN": Cannot determine if it's greenwashing based on available evidence

    """

    # Call the LLM
    messages = [{"role": "system", "content": system_prompt}, {"role": "user", "content": user_prompt}]

    # Use call_llms_typed with the PromiseVerdictResponse model
    response = call_llms_typed(
        [LLMModel.NORMAL_CHEAP],
        messages,
        16000,
        response_model=PromiseVerdictResponse,
        options=LLMOptions(
            thinking=False,
            eval=lambda x: valid_citations(cur, x.verdict, 1, 1) if x else False,
            temperature=0.2,
            escalate_to=[LLMModel.NORMAL_ALL_ROUNDER_ALT],
        ),
    )

    if not response:
        logger.info(f"No response from LLM for promise verdict analysis of promise {promise.statement_id}")
        return None

    if response.promise_kept == "IRRELEVANT":
        logger.info(f"Promise {promise.statement_id} marked as IRRELEVANT - skipping")
        return None
    
    if response.promise_kept == "UNKNOWN":
        logger.info(f"Promise {promise.statement_id} has inconclusive evidence - marking as UNKNOWN and skipping")
        return None

    # Extract citation IDs
    citation_ids = extract_eko_citations(response.verdict)

    # Convert citation IDs to Citation objects
    from eko.analysis_v2.citations import expand_citations
    citations = []
    try:
        # Only expand citations if we have IDs
        if citation_ids:
            citations = expand_citations(cur, citation_ids)
            logger.info(f"Expanded {len(citation_ids)} citation IDs to {len(citations)} Citation objects")
    except Exception as e:
        logger.error(f"Error expanding citations: {e}")
        logger.exception(e)

    # Prepare evidence for storage
    evidence_dict = {}
    for i, evidence in enumerate(evidence_items):
        evidence_dict[str(i)] = {
            "statement_id": evidence.statement_id,
            "text": evidence.text,
            "summary": evidence.llm_summary,
            "fulfills": evidence.fulfills_promise,
            "doc_year": evidence.doc_year,
            "doc_id": evidence.doc_id,
            "doc_page_id": evidence.doc_page_id,
            "confidence": evidence.confidence
        }

    # Get the statement from the database
    statement = promise.statement
    # try:
        # # Query the statement from the database
        # cur.execute(
        #     """
        #     SELECT s.*
        #     FROM kg_statements s
        #     WHERE s.id = %s
        #     """, (promise_id,)
        # )
        # stmt_row = cur.fetchone()
        # if stmt_row:
        #     # Create a StatementAndMetadata object
        #     from eko.models.statement_metadata import StatementMetadata
        #     from eko.models.vector.demise.demise_model import DEMISEModel
        # 
        #     # Extract statement data
        #     statement_text = stmt_row[1]  # Assuming statement_text is at index 1
        #     context = stmt_row[2]  # Assuming context is at index 2
        # 
        #     # Get model_json from the statement
        #     cur.execute(
        #         """
        #         SELECT model_json
        #         FROM kg_statements
        #         WHERE id = %s
        #         """, (promise_id,)
        #     )
        #     model_row = cur.fetchone()
        #     model_json = model_row[0] if model_row else None
        # 
        #     # Create a DEMISE model from the model_json
        #     demise_model = None
        #     if model_json:
        #         try:
        #             demise_model = DEMISEModel.from_kv(model_json)
        #         except Exception as e:
        #             logger.error(f"Error creating DEMISE model: {e}")
        # 
        #     if demise_model:
        #         # Create a StatementMetadata object from the DEMISE model
        #         metadata = StatementMetadata(
        #             domain=demise_model.domain,
        #             statement_category=demise_model.statement.statement_type,
        #             is_environmental=demise_model.domain.is_environmental,
        #             is_social=demise_model.domain.is_social,
        #             is_governance=demise_model.domain.is_governance,
        #             is_animal_welfare=demise_model.domain.is_animal_welfare
        #         )
        # 
        #         # Create a StatementAndMetadata object
        #         statement = StatementAndMetadata(
        #             id=promise_id,
        #             statement_text=statement_text,
        #             context=context,
        #             metadata=metadata,
        #             demise=demise_model,
        #             doc_id=promise.doc_id,
        #             doc_year=promise.doc_year
        #         )
    
    # except Exception as e:
    #     logger.error(f"Error getting statement for promise {promise_id}: {e}")
    #     logger.exception(e)

    # Create the verdict
    return PromiseVerdict(
        id=None,  # Will be set by the database
        statement_id=promise_id,
        promise_kept=response.promise_kept == "YES",
        greenwashing=response.greenwashing == "YES",
        verdict=response.verdict,
        summary=response.summary,
        conclusion=response.conclusion,
        run_id=run_id,
        confidence=response.confidence,
        citations=citations,  # Now using Citation objects instead of IDs
        evidence=evidence_dict,
        company=virtual_entity.name,
        doc_title=promise.doc_title,
        doc_year=promise.doc_year,
        doc_authors=promise.doc_authors or [],  # Will need to convert these to Entity objects
        virtual_entity_id=virtual_entity.id,
        virtual_entity_short_id=virtual_entity.short_id,
        virtual_entity=virtual_entity,
        statement=statement  # Add the statement
    )





def store_results(cur: Cursor, verdict: PromiseVerdict) -> bool:
    """
    Store the verdict in the ana_promises_v2 table and then in the xfer_gw_promises table.

    Args:
        cur: Database cursor
        verdict: The verdict to store

    Returns:
        True if successful, False otherwise
    """
    try:
        from eko.db.data.promise import PromiseData

        # Get promise text from the statement
        cur.execute(
            "SELECT statement_text, context, company_id FROM kg_statements WHERE id = %s",
            (verdict.statement_id,)
        )
        promise_info = cur.fetchone()

        if not promise_info:
            logger.error(f"Failed to find statement {verdict.statement_id}")
            return False

        text, context, company_id = promise_info

        # First store in ana_promises_v2 table
        try:
            promise_id = PromiseData.create(cur.connection, verdict, text, context, company_id)
            logger.info(f"Stored promise in ana_promises_v2 with ID {promise_id}")

        except Exception as inner_e:
            logger.exception(f"Error storing in ana_promises_v2: {inner_e}")
            return False

        # If we get here, the operation was successful
        return True

    except Exception as e:
        logger.exception(f"Error storing verdict: {e}")
        cur.connection.rollback()
        return False


def process_promises(
    virtual_entity: VirtualEntityExpandedModel,
    start_year: int,
    end_year: int,
    run_id: int,
    max_promises: int = 100,
    debug: bool = False
) -> Dict[str, Any]:
    """
    Process promises for a virtual entity.

    Args:
        virtual_entity: The VirtualEntityModel to process promises for
        start_year: Start year for promises
        end_year: End year for promises
        run_id: Run ID for the analysis
        max_promises: Maximum number of promises to process
        debug: Whether to output debug information

    Returns:
        Dictionary with results information
    """
    # Debug mode enabled for detailed logging
    if debug:
        logger.info(f"Debug mode enabled for promises processing")

    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            # Starting the promise analysis pipeline
            logger.info(f"Starting promises analysis for {virtual_entity.name}")

            promises = find_promises(cur, virtual_entity, start_year, end_year)

            # Found promises to process
            logger.info(f"Found {len(promises)} promises to process")

            if not promises:
                logger.warning(f"No promises found for {virtual_entity.name}")
                return {"success": False, "error": "No promises found", "run_id": run_id}

            # Merge similar promises
            original_promise_count = len(promises)
            promises = merge_similar_promises(promises)
            logger.info(f"Merged {original_promise_count} promises into {len(promises)} promises")

            # Merging statistics
            merge_reduction_percentage = (original_promise_count - len(promises)) / original_promise_count * 100 if original_promise_count > 0 else 0
            logger.info(f"Promise merging reduced promises by {merge_reduction_percentage:.1f}%")

            # Limit number of promises to process
            promises = promises[:max_promises]

            # Process each promise
            successful_verdicts = 0
            for promise in promises:
                promise_id = promise.statement_id
                logger.info(f"Processing promise {promise_id}: {promise.text} from {promise.doc_year}")

                try:
                    # Starting to process promise
                    logger.debug(f"Starting to process promise {promise_id}")

                    # Search for evidence (look for statements AFTER the promise was made)
                    evidence_items = search_for_evidence(cur, virtual_entity, promise)

                    # Record evidence found
                    if evidence_items:
                        logger.debug(f"Found {len(evidence_items)} evidence items for promise {promise_id}")

                    if not evidence_items or len(evidence_items) < MIN_EVIDENCE_COUNT:
                        logger.warning(f"Not enough evidence for promise {promise_id} (only {len(evidence_items) if evidence_items else 0} items)")
                        continue

                    # Filter evidence
                    filtered_evidence = filter_evidence(cur, virtual_entity, promise, evidence_items)

                    if not filtered_evidence or len(filtered_evidence) < MIN_EVIDENCE_COUNT:
                        logger.warning(f"Not enough relevant evidence for promise {promise_id} after filtering (only {len(filtered_evidence)} items)")
                        continue

                    # Determine verdict
                    verdict = determine_verdict(cur, virtual_entity, promise, filtered_evidence, run_id, end_year)

                    if verdict:
                        # Created verdict for promise
                        logger.debug(f"Created verdict for promise {promise_id}")

                        # Store the verdict
                        if store_results(cur, verdict):
                            conn.commit()
                            successful_verdicts += 1

                            logger.debug(f"Stored verdict for promise {promise_id}")
                        else:
                            logger.error(f"Failed to store verdict for promise {promise_id}")
                    else:
                        logger.warning(f"Failed to determine verdict for promise {promise_id}")

                    # Completed processing this promise
                    logger.debug(f"Completed processing promise {promise_id}")

                except Exception as e:
                    logger.exception(f"Error processing promise {promise_id}: {e}")
                    traceback.print_stack()
                    logger.error(f"Error processing promise {promise_id}: {e}")
                    continue

            # Update run status to completed
            RunData.mark_completed(conn, run_id)

            # Promises analysis pipeline completed
            logger.info(f"Promises analysis completed with {successful_verdicts} successful verdicts")

            return {
                "success": True,
                "run_id": run_id,
                "total_promises": len(promises),
                "successful_verdicts": successful_verdicts,
                "entity": virtual_entity.name
            }


def run_promises(
    virtual_entity: VirtualEntityExpandedModel,
    start_year: int,
    end_year: int,
    run_id: int,
    debug: bool = False,
    report: bool = False
) -> Dict[str, Any]:
    """
    Command function for promise analysis.

    Args:
        virtual_entity: The VirtualEntityModel to analyze
        start_year: Start year for promises
        end_year: End year for promises
        run_id: Optional run ID
        debug: Whether to output debug information
        report: Whether to generate a human-readable report

    Returns:
        Dictionary with results information
    """
    results = process_entity_analysis(
        start_year=start_year,
        end_year=end_year,
        run_type="promises",
        process_fn=process_promises,
        virtual_entity=virtual_entity,
        run_id=run_id,
        debug=debug
    )

    with get_bo_conn() as conn:
        XferData.sync_promises_to_xfer_v2(conn, run_id)

    # Generate a report if requested
    if report and results.get("success", False):
        try:
            report_text = generate_promise_report(results)
            results["report"] = report_text

            # Save the report to a file
            report_dir = os.path.join(eko_var_path, "reports")
            os.makedirs(report_dir, exist_ok=True)

            entity_name = results.get("entity", "unknown").replace(" ", "_").lower()
            report_path = os.path.join(report_dir, f"promise_report_{entity_name}_{start_year}_{end_year}.txt")

            with open(report_path, "w", encoding="utf-8") as f:
                f.write(report_text)

            results["report_path"] = report_path
            logger.info(f"Saved promise report to {report_path}")
        except Exception as e:
            logger.error(f"Error generating promise report: {e}")
            results["report_error"] = str(e)

    return results
