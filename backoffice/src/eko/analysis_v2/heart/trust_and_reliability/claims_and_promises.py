"""
Common utility functions for claim evidence and promise analysis.

This module contains shared functions and classes used by both the claim evidence
and promise analysis systems for analyzing ESG statements.
"""
import re
from loguru import logger
from psycopg import Cursor
from psycopg.sql import SQL
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional, Callable, TypeVar

from eko.db import get_bo_conn
from eko.models import Entity
from eko.models.citation_model import Citation
from eko.models.statement_metadata import StatementAndMetadata
from eko.models.virtual_entity import VirtualEntityModel, VirtualEntityExpandedModel

# Constants for evidence analysis
MIN_EVIDENCE_COUNT = 1
MIN_DOCS_FOR_ANALYSIS = 2
MAX_EVIDENCE_COUNT = 20
SIMILARITY_THRESHOLD = 0.6
MIN_EVIDENCE_CONFIDENCE = 70


# Generic base class for evidence
T = TypeVar('T', bound=BaseModel)


class ClaimModel(BaseModel):
    """Model for a claim to be evaluated."""
    statement_id: int
    text: str
    context: Optional[str] = ""
    doc_id: int
    doc_year: int  # Original document year (kept for backward compatibility)
    start_year: Optional[int] = None  # Statement's start year
    end_year: Optional[int] = None  # Statement's end year
    doc_title: str
    doc_authors: List[Any] = Field(default_factory=list)
    demise_model: Any  # Can't type this directly due to circular imports
    virtual_entity_id: Optional[int] = None
    virtual_entity_short_id: Optional[str] = None
    virtual_entity: Optional["VirtualEntityExpandedModel"] = None  # Can't type directly due to circular imports
    importance: Optional[int] = Field(0, description="Importance of the claim (0-100)")
    # Merging information
    merged_from_ids: List[int] = Field(default_factory=list)  # IDs of claims that were merged to create this one
    is_merged: bool = False  # Whether this claim is a result of merging
    merge_type: Optional[str] = None  # Type of merge: "domain", "text", or "domain+text"
    statement: StatementAndMetadata = Field(..., description="The statement the claim is about")

    def get_year(self) -> int:
        """
        Get the most appropriate year for this claim.
        Prefers statement's start_year if available, otherwise falls back to doc_year.
        """
        return self.start_year if self.start_year is not None else self.doc_year


class PromiseModel(BaseModel):
    """Model for a promise to be evaluated."""
    statement_id: int
    statement: StatementAndMetadata
    text: str
    context: Optional[str] = ""
    doc_id: int
    doc_year: int  # Original document year (kept for backward compatibility)
    start_year: Optional[int] = None  # Statement's start year
    end_year: Optional[int] = None  # Statement's end year
    doc_title: str
    doc_authors: List[Any] = Field(default_factory=list)
    demise_model: Any  # Can't type this directly due to circular imports
    virtual_entity_id: Optional[int] = None
    virtual_entity_short_id: Optional[str] = None
    virtual_entity: Optional["VirtualEntityExpandedModel"] = None  # Can't type directly due to circular imports
    # Merging information
    merged_from_ids: List[int] = Field(default_factory=list)  # IDs of promises that were merged to create this one
    is_merged: bool = False  # Whether this promise is a result of merging
    merge_type: Optional[str] = None  # Type of merge: "domain", "text", or "domain+text"

    def get_year(self) -> int:
        """
        Get the most appropriate year for this promise.
        Prefers statement's start_year if available, otherwise falls back to doc_year.
        """
        return self.start_year if self.start_year is not None else self.doc_year


class BaseEvidence(BaseModel):
    """Base model for evidence items."""
    statement_id: int
    doc_id: int
    doc_page_id: int
    text: str
    extended_text: str
    context: str
    doc_year: int
    confidence: int = Field(default=0)
    relevance_score: float = Field(default=0.0)
    llm_summary: str = Field(default="")


class BaseVerdict(BaseModel):
    """Base model for analysis verdicts."""
    statement_id: int
    verdict: str
    summary: str
    conclusion: str
    run_id: int
    confidence: int
    citations: List["Citation"] = Field(default_factory=list)  # Using Citation objects
    company: str
    doc_title: str
    doc_year: int
    doc_authors: List[Entity] = Field(default_factory=list)
    greenwashing: bool = Field(default=False)
    virtual_entity_id: int
    virtual_entity_short_id: str
    virtual_entity: VirtualEntityModel


def extract_verdict_field(response: Optional[str], field: str) -> str:
    """
    Extract a field from the verdict response.

    Args:
        response: The LLM response
        field: The field to extract

    Returns:
        The extracted field content or empty string
    """
    if not response:
        return ""

    match = re.search(f'<{field}>(.*?)</{field}>', response, re.DOTALL)
    return match.group(1).strip() if match else ""


def search_for_statements(
    cur: Cursor,
    virtual_entity: VirtualEntityExpandedModel,
    statement_id: int,
    doc_year: int,  # Renamed from doc_year to reference_year for clarity but keeping parameter name for compatibility
    vector: List[float],
    year_comparison: str,
    max_items: int = MAX_EVIDENCE_COUNT
) -> List[Dict[str, Any]]:
    """
    Search for statements about this virtual entity with a specific year relationship.

    Args:
        cur: Database cursor
        virtual_entity: VirtualEntityModel to find statements for
        statement_id: The ID of the statement to exclude
        doc_year: The reference year for comparison
        vector: The vector to compare against
        year_comparison: SQL comparison for year (e.g., "< %s" or "> %s")
        max_items: Maximum number of items to return

    Returns:
        List of statements with similarity scores
    """
    entity_ids = [entity.id for entity in virtual_entity.base_entities]
    entity_ids_str = ','.join(str(id) for id in entity_ids)
    if not entity_ids:
        logger.warning(f"No base entities found for virtual entity {virtual_entity.id}")
        return []

    # Try to use the optimized vector search if the pg_vector extension is available
    return _search_for_statements_with_pg_vector(
        cur, entity_ids_str, statement_id, doc_year, vector, year_comparison, max_items
    )


def _search_for_statements_with_pg_vector(
    cur: Cursor,
    entity_ids_str: str,
    statement_id: int,
    doc_year: int,
    vector: List[float],
    year_comparison: str,
    max_items: int
) -> List[Dict[str, Any]]:
    """
    Optimized version that uses PostgreSQL vector operations.

    Args:
        cur: Database cursor
        entity_ids_str: Comma-separated string of entity IDs
        statement_id: The ID of the statement to exclude
        doc_year: The reference year for comparison
        vector: The vector to compare against
        year_comparison: SQL comparison for year (e.g., "< %s" or "> %s")
        max_items: Maximum number of items to return

    Returns:
        List of statements with similarity scores
    """
    logger.info("Using pg_vector for optimized similarity search")

    # For pg_vector, we need to cast the vector explicitly in the SQL
    # rather than passing it as a parameter to avoid type determination issues
    vector_str = '[' + ','.join(str(v) for v in vector) + ']'

    # Build the query with the vector literal directly embedded in the SQL
    # This avoids the parameter type determination issue
    query = f"""
        WITH entities AS (SELECT id FROM kg_base_entities WHERE id IN ({entity_ids_str})),
        vector_val AS (SELECT '{vector_str}'::vector AS vec)
        SELECT
            s.id AS statement_id,
            s.statement_text AS text,
            s.context AS context,
            s.doc_id,
            COALESCE(s.start_year, doc.year) AS doc_year,
            s.doc_page_id,
            -- Calculate cosine similarity directly in the database
            -- 1 - (s.domain_embedding <=> vector_val.vec) gives us similarity instead of distance
            1 - (s.domain_embedding <=> vector_val.vec) AS similarity_score
        FROM kg_statements s
        JOIN entities ON (s.company_id = entities.id OR entities.id = ANY(s.subject_entities) OR entities.id = ANY(s.object_entities))
        JOIN kg_documents doc ON s.doc_id = doc.id
        CROSS JOIN vector_val
        WHERE
            doc.status != 'deleted'
            AND 'disclosure' != ANY(doc.research_categories)
            -- Exclude documents where any author is part of the virtual entity
            AND NOT EXISTS (
                SELECT 1
                FROM kg_document_authors da
                WHERE da.doc_id = doc.id
                AND da.entity_id IN (SELECT id FROM entities)
            )
            AND (
                -- Use statement start_year if available
                (s.start_year IS NOT NULL AND s.start_year {year_comparison})
                -- Fall back to document year if statement year isn't available
                OR (s.start_year IS NULL AND doc.year {year_comparison})
            )
            AND s.id != %s  -- exclude the original statement
            -- Filter by similarity threshold directly in the database
            AND 1 - (s.domain_embedding <=> vector_val.vec) > %s
        ORDER BY similarity_score DESC  -- Higher score means higher similarity
        LIMIT %s
    """

    # Execute the query with parameters (no vector parameter)
    # Using SQL() to convert the string to a SQL object to avoid type errors
    cur.execute(
        SQL(query),
        (doc_year, doc_year, statement_id, SIMILARITY_THRESHOLD, max_items)
    )

    results = cur.fetchall()
    logger.info(f"Found {len(results)} statements with similarity above threshold using pg_vector")

    # Process the results to get extended text
    similar_items = []
    for result in results:
        statement_id, text, context, doc_id, doc_year, doc_page_id, similarity_score = result

        # Get extended text for context
        cur.execute(
            """
            SELECT
                string_agg(page_text, E'\n\n---\n\n' ORDER BY page) as full_text
            FROM kg_document_pages pages
            WHERE pages.doc_id = %s
            AND pages.page BETWEEN
                (SELECT page FROM kg_document_pages WHERE id = %s) - 1
            AND
                (SELECT page FROM kg_document_pages WHERE id = %s) + 1
            """,
            (doc_id, doc_page_id, doc_page_id)
        )

        extended_text_result = cur.fetchone()
        extended_text = extended_text_result[0] if extended_text_result else text

        # Add to list
        similar_items.append({
            'statement_id': statement_id,
            'doc_id': doc_id,
            'doc_page_id': doc_page_id,
            'text': text,
            'extended_text': extended_text,
            'context': context or "",
            'doc_year': doc_year,
            'relevance_score': float(similarity_score)
        })

    return similar_items


def validate_response_tags(response: Optional[str], required_tags: List[str]) -> bool:
    """
    Validate that the response contains all required tags.

    Args:
        response: LLM response to validate
        required_tags: List of required tags to check for

    Returns:
        True if all required tags are present, False otherwise
    """
    if not response:
        return False

    # Special case for "NOT RELEVANT" responses
    if "NOT RELEVANT" in response:
        return True

    # Check all required tags
    for tag in required_tags:
        if isinstance(tag, tuple):
            # For tags with specific values, like (tag_name, allowed_values)
            tag_name, allowed_values = tag
            pattern = f'<{tag_name}>({"|".join(allowed_values)})</{tag_name}>'
            if not re.search(pattern, response):
                return False
        else:
            # For simple tags that just need to exist
            if tag == "confidence":
                if not re.search(r'<confidence>\d+</confidence>', response):
                    return False
            elif tag == "summary":
                if not re.search(r'<summary>.*?</summary>', response, re.DOTALL):
                    return False
            elif tag == "verdict":
                if not re.search(r'<verdict>.*?</verdict>', response, re.DOTALL):
                    return False
            elif tag == "conclusion":
                if not re.search(r'<conclusion>.*?</conclusion>', response, re.DOTALL):
                    return False
            else:
                # Generic tag check
                if not re.search(f'<{tag}>.*?</{tag}>', response, re.DOTALL):
                    return False

    return True


def process_entity_analysis(
    start_year: int,
    end_year: int,
    run_type: str,
    process_fn: Callable[[VirtualEntityExpandedModel, int, int, int, int, bool], Dict[str, Any]],
    virtual_entity: VirtualEntityExpandedModel,
    run_id: int,
    debug: bool = False
) -> Dict[str, Any]:
    """
    Generic function to process claims or promises based on virtual entity.

    Args:
        start_year: Start year for analysis
        end_year: End year for analysis
        run_type: Type of run ("claims" or "promises")
        process_fn: Function to process the virtual entity
        run_id: Optional run ID
        debug: Whether to output debug information
        virtual_entity: VirtualEntityModel to use

    Returns:
        Dictionary with results information
    """
    try:
        # Use the virtual entity's name for the run
        entity_name = virtual_entity.name

        if not isinstance(run_id, int):
            return {"success": False, "error": "Invalid run_id, must be an integer"}

        # Process the virtual entity
        logger.info(f"Processing {run_type} for virtual entity: {virtual_entity.name} (ID: {virtual_entity.id})")
        result = process_fn(virtual_entity, start_year, end_year, run_id, 100, debug)

        # Sync the results to the xfer tables
        from eko.db.data.xfer import XferData
        with get_bo_conn() as conn:
            if run_type == "claims":
                logger.info(f"Syncing claims to xfer_claims table for run {run_id}")
                synced_ids = XferData.sync_claims_to_xfer_v2(conn, run_id)
                logger.info(f"Synced {len(synced_ids)} claims to xfer_claims table")
            elif run_type == "promises":
                logger.info(f"Syncing promises to xfer_promises table for run {run_id}")
                synced_ids = XferData.sync_promises_to_xfer_v2(conn, run_id)
                logger.info(f"Synced {len(synced_ids)} promises to xfer_promises table")

        # Return the results
        return {
            "success": True,
            "run_id": run_id,
            f"total_{run_type}": result.get(f"total_{run_type}", 0),
            "successful_verdicts": result.get("successful_verdicts", 0),
            "entity": virtual_entity.name
        }
    except Exception as e:
        logger.error(f"Error in process_entity_analysis: {e}")
        return {"success": False, "error": str(e)}
