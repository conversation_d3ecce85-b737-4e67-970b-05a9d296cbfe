"""
Claim Evidence module for finding and analyzing evidence for claims.

This module handles searching for evidence that supports or contradicts claims
made by companies in their ESG statements.
"""
from concurrent.futures import ThreadPoolExecutor, as_completed

import os
from datetime import datetime
from loguru import logger
from psycopg import Cursor
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional, cast

from eko import eko_var_path
from eko.analysis_v2.citations import valid_citations
from eko.analysis_v2.heart.trust_and_reliability.claim_report import generate_claim_report
from eko.analysis_v2.heart.trust_and_reliability.claims_and_promises import BaseEvidence, ClaimModel, \
    MAX_EVIDENCE_COUNT, process_entity_analysis, MIN_EVIDENCE_COUNT, \
    search_for_statements, BaseVerdict
from eko.analysis_v2.heart.trust_and_reliability.merging import merge_similar_claims
from eko.db import get_bo_conn
from eko.db.data.run import RunData
from eko.db.data.statement import StatementData
from eko.db.data.xfer import XferData
from eko.entities.queries import get_entity_by_id
from eko.llm import LLMModel
from eko.llm.dynamic_prompts.claim_importance import create_claim_importance_prompt
from eko.llm.main import call_llms_typed, call_llm_score, LLMOptions
from eko.models.statement_metadata import StatementAndMetadata
from eko.models.vector.demise.demise_model import DEMISEModel
from eko.models.virtual_entity import VirtualEntityExpandedModel
from eko.nlp.util import extract_eko_citations
from eko.settings import settings

# Define output directory for debug information
OUTPUT_DIR = os.path.join(eko_var_path, "claims_evidence")
os.makedirs(OUTPUT_DIR, exist_ok=True)


def score_claim_importance(claim_text: str, company_name: str, context: str = "") -> int:
    """
    Score the importance of a claim using LLM.

    Args:
        claim_text: The text of the claim to score
        company_name: Name of the company making the claim
        context: Additional context about the claim

    Returns:
        Importance score from 0-100
    """
    try:
        messages = create_claim_importance_prompt(claim_text, company_name, context)
        score = call_llm_score(messages, min=0, max=100, max_tokens=10, no_cache=False)
        logger.info(f"Claim importance score: {score} for claim: {claim_text[:100]}...")
        return score
    except Exception as e:
        logger.warning(f"Failed to score claim importance: {e}")
        return 50  # Default to medium importance if scoring fails


class ClaimEvidence(BaseEvidence):
    """Model for evidence related to a claim."""
    supports_claim: bool = False


class ClaimVerdict(BaseVerdict):
    """Model for a verdict on a claim."""

    id: Optional[int] = Field(None, description="The database ID of this verdict")
    valid_claim: bool
    counters: Dict[str, Any] = {}
    claim_doc: str = ""  # Use doc_title from BaseVerdict
    importance: Optional[int] = Field(0, description="Importance of the claim (0-100)")
    statement: Optional[StatementAndMetadata] = Field(None, description="The statement the claim is about")
    text: str = Field("", description="Claim text content")
    context: Optional[str] = Field(None, description="Context for the claim")
    llm_greenwashing: bool = Field(False, description="LLM greenwashing assessment")
    company_id: int = Field(0, description="Company ID associated with the claim")
    esg_claim: bool = Field(True, description="Whether this is an ESG claim")

    @property
    def verdict_for(self) -> str:
        """Returns a string representation of the verdict for tracking."""
        if self.valid_claim:
            return "VALID_CLAIM"
        else:
            return "INVALID_CLAIM"


class ClaimEvidenceSummaryResponse(BaseModel):
    """Pydantic model for claim evidence summarization LLM response."""
    support: str = Field(..., description="Whether the evidence supports or contradicts the claim")
    confidence: int = Field(..., ge=1, le=100, description="Confidence score from 1-100")
    summary: str = Field(..., description="Summary of the evidence with citations")


class ClaimVerdictResponse(BaseModel):
    """Pydantic model for claim verdict LLM response."""
    valid_claim: str = Field(..., description="Whether the claim is valid (YES, NO or IRRELEVANT)")
    greenwashing: str = Field(..., description="Whether the claim constitutes greenwashing (YES or NO)")
    confidence: int = Field(..., ge=1, le=100, description="Confidence score from 1-100")
    verdict: str = Field(..., description="Detailed verdict with reasoning and citations")
    summary: str = Field(..., description="Concise summary of the verdict")
    conclusion: str = Field(..., description="Single sentence conclusion")


def find_claims(cur: Cursor, virtual_entity: VirtualEntityExpandedModel, start_year: int, end_year: int) -> List[
    ClaimModel]:
    """
    Find statements that are claims from the given virtual entity.

    This function now includes importance scoring and filtering to screen out
    claims with importance < 30 before expensive evidence processing.

    Args:
        cur: Database cursor
        virtual_entity: VirtualEntityExpandedModel to find claims for
        start_year: Start year for claims
        end_year: End year for claims

    Returns:
        List of claims as ClaimModel objects with importance >= 30
    """
    from concurrent.futures import ThreadPoolExecutor, as_completed

    logger.info(f"Finding claims for virtual entity {virtual_entity.name} from {start_year} to {end_year}")

    # Get a list of base entity IDs from the virtual entity
    entity_ids = [entity.id for entity in virtual_entity.base_entities]

    # If there are no entity IDs, return an empty list
    if not entity_ids:
        logger.warning("No entity IDs found for virtual entity")
        return []

    # Create a parameterized query with placeholders for each entity ID
    entity_ids_str = ','.join(str(id) for id in entity_ids)

    # Build the query with proper parameterization
    # Using a string format for the entity IDs part, but parameterized queries for the rest
    query = f"""
        WITH entities AS (SELECT id FROM kg_base_entities WHERE id IN ({entity_ids_str}))
        SELECT
            kg_statements.id AS statement_id,
            kg_statements.statement_text AS text,
            kg_statements.context AS context,
            kg_statements.doc_id,
            COALESCE(kg_statements.start_year, doc.year) AS doc_year,
            kg_statements.model_json,
            doc.title AS doc_title,
            (select array_agg(ent.id) from kg_base_entities ent JOIN kg_document_authors a ON ent.id = a.entity_id WHERE a.doc_id=doc.id ) as doc_authors,
            kg_statements.start_year,  -- Explicitly include start_year
            kg_statements.end_year     -- Explicitly include end_year
        FROM kg_statements
        JOIN entities ON kg_statements.company_id = entities.id OR entities.id = ANY(kg_statements.subject_entities) OR entities.id = ANY(kg_statements.object_entities)
        JOIN kg_documents doc ON kg_statements.doc_id = doc.id
        WHERE
            doc.status != 'deleted'
            AND (kg_statements.is_animal_welfare  OR kg_statements.is_environmental OR kg_statements.is_social OR kg_statements.is_governance)
            AND 'disclosure' = ANY(doc.research_categories)
            AND kg_statements.statement_category='claim'
            AND kg_statements.impact_value > 0.0
            AND (
                -- Use statement year range if available
                (kg_statements.start_year IS NOT NULL AND
                 -- Statements overlap with the requested time range if:
                 -- NOT (end_year < start_range OR start_year > end_range)
                 NOT (COALESCE(kg_statements.end_year, kg_statements.start_year) < %s OR kg_statements.start_year > %s))
                OR
                -- Fallback to document year
                (kg_statements.start_year IS NULL AND doc.year BETWEEN %s AND %s)
            )
        ORDER BY COALESCE(kg_statements.start_year, doc.year) DESC, kg_statements.id DESC
    """

    # Execute with just the year parameters (entity IDs are now in the query string)
    params = [start_year, end_year, start_year, end_year]
    try:
        cur.execute(query, params)
    except Exception as e:
        logger.exception(f"Error executing query: {e}")
        return []

    all_statements = cur.fetchall()
    logger.info(f"Found {len(all_statements)} total statements")

    # Filter for statements that are claims and create ClaimModel objects
    potential_claims = []
    for statement in all_statements:
        # Unpack all values from the query result
        statement_id, text, context, doc_id, doc_year, model_json, doc_title, doc_authors, statement_start_year, statement_end_year = statement

        if not model_json:
            logger.warning(f"Statement {statement_id} has no model JSON")
            continue

        # Try to load the DEMISE model from JSON
        # Create a DEMISE model instance
        model = DEMISEModel.from_kv(model_json)
        # Cast to the correct type to avoid type checking issues
        demise_model = cast(DEMISEModel, model)
        logger.info(demise_model.statement.to_kv_sparse())

        potential_claims.append(
            ClaimModel(
                statement_id=statement_id,
                statement=StatementData.get_by_id(cur.connection, statement_id),
                text=text,
                context=context,
                doc_id=doc_id,
                doc_year=doc_year,
                start_year=statement_start_year,  # Using directly unpacked variable
                end_year=statement_end_year,  # Using directly unpacked variable
                doc_title=doc_title,
                doc_authors=[get_entity_by_id(i) for i in doc_authors],
                demise_model=model,
                virtual_entity_id=virtual_entity.id,
                virtual_entity_short_id=virtual_entity.short_id,
                virtual_entity=virtual_entity,
                importance=0,  # Will be set by importance scoring
            )
        )

    logger.info(f"Found {len(potential_claims)} potential claims out of {len(all_statements)} statements")

    if not potential_claims:
        return []

    # Score importance for all claims in parallel
    logger.info(f"Scoring importance for {len(potential_claims)} claims...")

    # Process claims in batches with ThreadPoolExecutor for parallel LLM calls
    scored_claims = []
    batch_size = 50  # Process in batches to avoid overwhelming the LLM API

    for i in range(0, len(potential_claims), batch_size):
        batch = potential_claims[i:i + batch_size]
        logger.info(f"Processing importance scoring batch {i // batch_size + 1}/{(len(potential_claims) + batch_size - 1) // batch_size}")

        with ThreadPoolExecutor(32) as executor:
            # Submit scoring tasks
            future_to_claim = {}
            for claim in batch:
                future = executor.submit(
                    score_claim_importance,
                    claim.text,
                    virtual_entity.name,
                    claim.context or ""
                )
                future_to_claim[future] = claim

            # Collect results and update claims
            for future in as_completed(future_to_claim):
                claim = future_to_claim[future]
                try:
                    importance_score = future.result()
                    claim.importance = importance_score
                    logger.debug(f"Claim {claim.statement_id}: importance score {importance_score}")
                except Exception as e:
                    logger.error(f"Error scoring claim {claim.statement_id}: {e}")
                    # Use default score of 50 for failed scoring
                    claim.importance = 50

                scored_claims.append(claim)

    # Filter out claims with importance < 30
    filtered_claims = [claim for claim in scored_claims if claim.importance >= 30]

    logger.info(f"After importance filtering (>= 30): {len(filtered_claims)} claims out of {len(scored_claims)} scored claims")

    # Sort by importance (highest first) for better processing order
    filtered_claims.sort(key=lambda x: x.importance, reverse=True)

    return filtered_claims


def search_for_evidence(
        cur: Cursor,
        virtual_entity: VirtualEntityExpandedModel,
        claim: ClaimModel,
        max_evidence: int = MAX_EVIDENCE_COUNT
) -> List[ClaimEvidence]:
    """
    Search for evidence related to a claim.

    Args:
        cur: Database cursor
        virtual_entity: VirtualEntityModel the claim is from
        claim: Claim to search evidence for
        max_evidence: Maximum number of evidence items to return

    Returns:
        List of ClaimEvidence items
    """
    claim_year = claim.get_year()  # Use get_year() which prefers start_year if available
    claim_id = claim.statement_id
    claim_model = claim.demise_model

    logger.info(f"Searching for evidence related to claim {claim_id} from year {claim_year}")

    # Get the claim vector using the DEMISE model
    claim_vector = claim_model.get_claim_vector()

    # Search for statements about this virtual entity from before the claim year
    similar_items = search_for_statements(
        cur=cur,
        virtual_entity=virtual_entity,
        statement_id=claim_id,
        doc_year=claim_year,  # Parameter is still named doc_year for compatibility
        vector=claim_vector,
        year_comparison="< %s",  # Look for statements before the claim year
        max_items=max_evidence
    )

    # Convert to ClaimEvidence objects
    evidence_items = [
        ClaimEvidence(
            statement_id=item['statement_id'],
            doc_id=item['doc_id'],
            doc_page_id=item['doc_page_id'],
            text=item['text'],
            extended_text=item['extended_text'],
            context=item['context'],
            doc_year=item['doc_year'],
            relevance_score=item['relevance_score']
        )
        for item in similar_items
    ]

    return evidence_items


def filter_evidence(
        cur: Cursor,
        virtual_entity: VirtualEntityExpandedModel,
        claim: ClaimModel,
        evidence_items: List[ClaimEvidence]
) -> List[ClaimEvidence]:
    """
    Use LLM to filter evidence items for relevance and summarize them.

    Args:
        cur: Database cursor
        virtual_entity: VirtualEntityExpandedModel the claim is from
        claim: The claim being evaluated
        evidence_items: List of evidence items to filter

    Returns:
        List of filtered and summarized evidence items
    """
    if not evidence_items:
        return []

    claim_text = claim.text
    claim_context = claim.context or ""

    logger.info(f"Filtering and summarizing {len(evidence_items)} evidence items")

    filtered_evidence = []

    # Process evidence items in parallel
    with ThreadPoolExecutor(max_workers=8) as executor:
        # Create futures for summarization tasks
        future_to_evidence = [
            executor.submit(summarize_and_verify_evidence, cur, virtual_entity, claim_text, claim_context, evidence)
            for evidence in evidence_items
        ]

        # Process results as they complete
        for future in as_completed(future_to_evidence):
            result = future.result()
            if result:
                filtered_evidence.append(result)

    # Sort by relevance score
    filtered_evidence.sort(key=lambda x: x.relevance_score, reverse=True)

    logger.info(f"After filtering, have {len(filtered_evidence)} relevant evidence items")
    return filtered_evidence


def summarize_and_verify_evidence(
        cur: Cursor,  # Kept for API compatibility but not used in this function
        virtual_entity: VirtualEntityExpandedModel,
        claim_text: str,
        claim_context: str,
        evidence: ClaimEvidence
) -> Optional[ClaimEvidence]:
    """
    Summarize evidence and verify its relevance to the claim.

    Args:
        cur: Database cursor (kept for API compatibility but not used in this function)
        virtual_entity: VirtualEntityExpandedModel the claim is from
        claim_text: The claim text
        claim_context: The claim context
        evidence: Evidence to summarize and verify

    Returns:
        Updated evidence item or None if not relevant
    """
    # Sanitize text to remove any mentions of other entities
    # Using the first base entity for compatibility with the old function
    base_entity = virtual_entity.base_entities[0] if virtual_entity.base_entities else None
    if not base_entity:
        return None

    sanitized_text =  evidence.extended_text

    # Create prompt for summarization and relevance check
    system_prompt = """You are an expert analyst examining corporate claims and evidence.
    You are not conversational, please do not include any preamble or discursive elements."""

    user_prompt = f"""<claim>{claim_text}</claim>
    <claim_context>{claim_context}</claim_context>

    <evidence year="{evidence.doc_year}">
    {sanitized_text}
    </evidence>

    First determine if this evidence from {evidence.doc_year} is directly relevant to the claim.
    The evidence must specifically discuss the same topic as the claim and be about {virtual_entity.name}.

    If the evidence is relevant:
    1. Provide a concise summary (300-500 words) of how the evidence relates to the claim.
    2. Include all relevant quotes, dates, quantities, and specifics from the evidence.
    3. Determine if the evidence supports or contradicts the claim.

    If the evidence is not relevant to the claim, simply respond with "NOT RELEVANT".
    """

    # Call the LLM
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt}
    ]

    # Use call_llms_typed with the ClaimEvidenceSummaryResponse model
    response = call_llms_typed(
        [LLMModel.NORMAL_CHEAP],
        messages,
        16000,
        response_model=ClaimEvidenceSummaryResponse,
        options=LLMOptions(temperature=0.2, escalate_to=[LLMModel.NORMAL_HQ])

    )

    # If we get here, we have a valid response
    # Update the evidence object with the response fields
    evidence.llm_summary = response.summary
    evidence.supports_claim = response.support == "SUPPORTS"
    evidence.confidence = response.confidence

    # If confidence is too low, consider not relevant
    if evidence.confidence < settings.claims_minimum_confidence: 
        return None

    return evidence

    


def determine_verdict(
        cur: Cursor,
        virtual_entity: VirtualEntityExpandedModel,
        claim: ClaimModel,
        evidence_items: List[ClaimEvidence],
        run_id: int
) -> Optional[ClaimVerdict]:
    """
    Determine whether the claim is valid based on the evidence.

    Args:
        cur: Database cursor
        virtual_entity: VirtualEntityExpandedModel the claim is from
        claim: The claim being evaluated
        evidence_items: List of evidence items
        run_id: Run ID

    Returns:
        Verdict or None if can't determine
    """
    if not evidence_items or len(evidence_items) < MIN_EVIDENCE_COUNT:
        logger.info(f"Not enough evidence ({len(evidence_items)}) to determine verdict")
        return None

    claim_text = claim.text
    claim_context = claim.context or ""
    claim_id = claim.statement_id

    # Prepare evidence for the LLM
    evidence_blocks = []
    for i, evidence in enumerate(sorted(evidence_items, key=lambda x: x.statement_id)):
        evidence_blocks.append(
            f"""<evidence page_id="{evidence.doc_page_id}" year="{evidence.doc_year}" {'supports="yes"' if evidence.supports_claim else 'supports="no"'}>
            <text>{evidence.text}</text>
            <summary>{evidence.llm_summary}</summary>
            </evidence>"""
        )

    evidence_text = "\n\n".join(evidence_blocks)

    # Create the verdict prompt
    system_prompt = """You are an expert ESG analyst evaluating corporate claims against evidence.
    You are not conversational, please do not include any preamble or discursive elements."""

    user_prompt = f"""<claim>{claim_text}</claim>
    <claim_context>{claim_context}</claim_context>
    <claim_year>{claim.doc_year}</claim_year>

    <note>{virtual_entity.title} is also known as {virtual_entity.aka}</note>

    Here is the evidence related to this claim by {virtual_entity.title}:

    {evidence_text}

    Analyze the evidence to determine whether the claim is valid and whether it constitutes greenwashing.

    Rules:
    1. The claim is valid if there is concrete evidence supporting it.
    2. The claim is greenwashing if it misleadingly exaggerates environmental benefits or achievements.
    3. A claim can be technically valid but still constitute greenwashing if it misleads through omission or exaggeration.
    4. Include citations from the evidence in your analysis using the format [^page_id] you can get page_id from the <evidence> tag.
    5. Your verdict should be thorough (800-1200 words) and well-reasoned.
    6. All conclusions must be based solely on the evidence provided.
    7. Never refer to the raw data in the prompt as '<claim_year>' or such like, just refer to it as the year. You are writing a report for publication.
    8. Don't assume the customer has teh same understanding of the claim as you do, so be very clear and explicit in your analysis.
    9. The customer can't see the information you have, so quote and cite as needed.

    If this is not a claim or it was not made by '{virtual_entity.title}' then respond with "IRRELEVANT" for `valid_claim`.
    If it is about the future (that is beyond {datetime.now().year}), respond with "IRRELEVANT" for `valid_claim`.

    """

    # Call the LLM
    try:
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

        # Use call_llms_typed with the ClaimVerdictResponse model
        response = call_llms_typed(
            [LLMModel.NORMAL_CHEAP],
            messages,
            16000,
            response_model=ClaimVerdictResponse,
            options=LLMOptions(
                temperature=0.2,
                escalate_to=[LLMModel.NORMAL_HQ],
                thinking=True,
                eval=lambda x: valid_citations(cur, x.verdict, 1, 1) if x else False,
            ),
        )

        if response.valid_claim == "IRRELEVANT":
            return None

        # Extract citation IDs
        citation_ids = extract_eko_citations(response.verdict)

        # Convert citation IDs to Citation objects
        from eko.analysis_v2.citations import expand_citations
        citations = []
        try:
            # Only expand citations if we have IDs
            if citation_ids:
                citations = expand_citations(cur, citation_ids)
                logger.info(f"Expanded {len(citation_ids)} citation IDs to {len(citations)} Citation objects")
        except Exception as e:
            logger.error(f"Error expanding citations: {e}")
            logger.exception(e)

        # Prepare counter evidence for storage
        counters = {}
        for i, evidence in enumerate(evidence_items):
            counters[str(i)] = {
                "statement_id": evidence.statement_id,
                "text": evidence.text,
                "summary": evidence.llm_summary,
                "supports": evidence.supports_claim,
                "doc_year": evidence.doc_year,
                "doc_id": evidence.doc_id,
                "doc_page_id": evidence.doc_page_id,
                "confidence": evidence.confidence
            }

        # Create the verdict (importance is already set in the claim from find_claims())
        return ClaimVerdict(
            id=None,  # Will be set by the database
            statement_id=claim_id,
            valid_claim=response.valid_claim == "YES",
            greenwashing=response.greenwashing == "YES",
            verdict=response.verdict,
            summary=response.summary,
            conclusion=response.conclusion,
            run_id=run_id,
            confidence=response.confidence,
            citations=citations,  # Now using Citation objects instead of IDs
            counters=counters,
            company=virtual_entity.name,
            doc_title=claim.doc_title,
            doc_year=claim.doc_year,
            doc_authors=claim.doc_authors or [],  # Will need to convert these to Entity objects
            virtual_entity_id=virtual_entity.id,
            virtual_entity_short_id=virtual_entity.short_id,
            virtual_entity=virtual_entity,
            importance=claim.importance,  # Use importance from the claim (set in find_claims())
            statement=claim.statement
        )
    except Exception as e:
        logger.error(f"Error in determine_verdict: {e}")
        return None


def store_results(cur: Cursor, verdict: ClaimVerdict) -> bool:
    """
    Store the verdict in the ana_claims table and then in the xfer_gw_claims table.

    Args:
        cur: Database cursor
        verdict: The verdict to store

    Returns:
        True if successful, False otherwise
    """
    try:
        from eko.db.data.claim import ClaimData
        from eko.db.data.statement import StatementData

        # Get claim text and other data from the statement
        cur.execute(
            "SELECT statement_text, context, company_id FROM kg_statements WHERE id = %s",
            (verdict.statement_id,)
        )
        claim_info = cur.fetchone()

        if not claim_info:
            logger.error(f"Failed to find statement {verdict.statement_id}")
            return False

        text, context, company_id = claim_info

        # Get the full statement with metadata
        try:
            statement = StatementData.get_by_id(cur.connection, verdict.statement_id)
            if statement:
                verdict.statement = statement
        except Exception as e:
            logger.warning(f"Could not get statement metadata for {verdict.statement_id}: {e}")
            # Continue without the statement metadata

        # First store in ana_claims table
        try:
            claim_id = ClaimData.create(cur.connection, verdict, text, context, company_id)
            logger.info(f"Stored claim in ana_claims with ID {claim_id}")
            return True  # Successfully stored the claim

        except Exception as inner_e:
            logger.exception(f"Error storing in ana_claims: {inner_e}")
            return False

    except Exception as e:
        logger.exception(f"Error storing verdict: {e}")
        cur.connection.rollback()
        return False


def process_claims(
        virtual_entity: VirtualEntityExpandedModel,
        start_year: int,
        end_year: int,
        run_id: int,
        max_claims: int = 100,
        debug: bool = False
) -> Dict[str, Any]:
    """
    Process claims for a virtual entity.

    Args:
        virtual_entity: The VirtualEntityModel to process claims for
        start_year: Start year for claims
        end_year: End year for claims
        run_id: Run ID for the analysis
        max_claims: Maximum number of claims to process
        debug: Whether to output debug information

    Returns:
        Dictionary with results information
    """
    # Debug mode enabled for detailed logging
    if debug:
        logger.info("Debug mode enabled for claims processing")

    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            # Starting the claims analysis pipeline
            logger.info(f"Starting claims analysis for {virtual_entity.name}")

            claims = find_claims(cur, virtual_entity, start_year, end_year)

            # Found claims to process
            logger.info(f"Found {len(claims)} claims to process")

            if not claims:
                logger.warning(f"No claims found for {virtual_entity.name}")
                return {"success": False, "error": "No claims found", "run_id": run_id}

            # Merge similar claims
            original_claim_count = len(claims)
            claims = merge_similar_claims(claims)
            logger.info(f"Merged {original_claim_count} claims into {len(claims)} claims")

            # Merging statistics
            merge_reduction_percentage = (original_claim_count - len(claims)) / original_claim_count * 100 if original_claim_count > 0 else 0
            logger.info(f"Claim merging reduced claims by {merge_reduction_percentage:.1f}%")

            # Limit number of claims to process
            claims = claims[:max_claims]

            # Process each claim
            successful_verdicts = 0
            for claim in claims:
                claim_id = claim.statement_id
                logger.info(f"Processing claim {claim_id}: {claim.text}")

                try:
                    # Starting to process claim
                    logger.debug(f"Starting to process claim {claim_id}")

                    # Search for evidence
                    evidence_items = search_for_evidence(cur, virtual_entity, claim)

                    # Record evidence found
                    if evidence_items:
                        logger.debug(f"Found {len(evidence_items)} evidence items for claim {claim_id}")

                    if not evidence_items or len(evidence_items) < MIN_EVIDENCE_COUNT:
                        logger.warning(f"Not enough evidence for claim {claim_id} (only {len(evidence_items) if evidence_items else 0} items)")
                        continue

                    # Filter evidence
                    filtered_evidence = filter_evidence(cur, virtual_entity, claim, evidence_items)

                    if not filtered_evidence or len(filtered_evidence) < MIN_EVIDENCE_COUNT:
                        logger.warning(f"Not enough relevant evidence for claim {claim_id} after filtering (only {len(filtered_evidence)} items)")
                        continue

                    # Determine verdict
                    verdict = determine_verdict(cur, virtual_entity, claim, filtered_evidence, run_id)

                    if verdict:
                        logger.debug(f"Created verdict for claim {claim_id}")

                        # Store the verdict
                        if store_results(cur, verdict):
                            conn.commit()
                            successful_verdicts += 1
                            logger.debug(f"Stored verdict for claim {claim_id}")
                        else:
                            logger.error(f"Failed to store verdict for claim {claim_id}")
                    else:
                        logger.warning(f"Failed to determine verdict for claim {claim_id}")

                    # Completed processing this claim
                    logger.debug(f"Completed processing claim {claim_id}")

                except Exception as e:
                    logger.error(f"Error processing claim {claim_id}: {e}")
                    continue

            # Update run status to completed
            RunData.mark_completed(conn, run_id)

            # Claims analysis pipeline completed
            logger.info(f"Claims analysis completed with {successful_verdicts} successful verdicts")

            return {
                "success": True,
                "run_id": run_id,
                "total_claims": len(claims),
                "successful_verdicts": successful_verdicts,
                "entity": virtual_entity.name
            }


def run_claims(
        virtual_entity: VirtualEntityExpandedModel,
        start_year: int,
        end_year: int,
        run_id: int,
        debug: bool = False,
        report: bool = False
) -> Dict[str, Any]:
    """
    Command function for claim evidence analysis.

    Args:
        virtual_entity: The VirtualEntityModel to analyze
        start_year: Start year for claims
        end_year: End year for claims
        run_id: Optional run ID
        debug: Whether to output debug information
        report: Whether to generate a human-readable report

    Returns:
        Dictionary with results information
    """
    results = process_entity_analysis(
        start_year=start_year,
        end_year=end_year,
        run_type="claims",
        process_fn=process_claims,
        virtual_entity=virtual_entity,
        run_id=run_id,
        debug=debug
    )

    with get_bo_conn() as conn:
        XferData.sync_claims_to_xfer_v2(conn, run_id)

    # Generate a report if requested
    if report and results.get("success", False):
        try:
            report_text = generate_claim_report(results)
            results["report"] = report_text

            # Save the report to a file
            report_dir = os.path.join(eko_var_path, "reports")
            os.makedirs(report_dir, exist_ok=True)

            entity_name = results.get("entity", "unknown").replace(" ", "_").lower()
            report_path = os.path.join(report_dir, f"claim_report_{entity_name}_{start_year}_{end_year}.txt")

            with open(report_path, "w", encoding="utf-8") as f:
                f.write(report_text)

            results["report_path"] = report_path
            logger.info(f"Saved claim report to {report_path}")
        except Exception as e:
            logger.error(f"Error generating claim report: {e}")
            results["report_error"] = str(e)

    return results
