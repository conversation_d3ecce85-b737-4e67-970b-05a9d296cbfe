"""
Main interface for selective highlighting detection.
"""
from loguru import logger
from typing import List, Dict, Any, Optional, Tuple, cast

from eko.analysis_v2.selective_highlighting.data import Cherry<PERSON>ickingDAO, FloodingDAO
from eko.analysis_v2.selective_highlighting.detection_pgvector import detect_cherry_picking_pgvector_knn, \
    detect_flooding_pgvector_knn
from eko.analysis_v2.selective_highlighting.merging import merge_similar_cherry_picking_models, \
    merge_similar_flooding_models
from eko.analysis_v2.trace_collector import create_trace_collector
from eko.analysis_v2.selective_highlighting.models import CherryPickingModel, FloodingModel
from eko.db import get_bo_conn
from eko.db.data.run import AnalysisRunModel
from eko.db.data.xfer import XferData
from eko.models.virtual_entity import VirtualEntityExpandedModel
from eko.typing import not_none
from eko.util.profiler import print_profiling_summary


def get_virtual_entities(entity_id: Optional[int] = None) -> List[Dict[str, Any]]:
    """
    Get all virtual entities or a specific one.

    Args:
        entity_id: Optional ID of the virtual entity to get

    Returns:
        List of virtual entities
    """
    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            if entity_id is not None:
                cur.execute(
                    """
                    SELECT id, name, eko_id, type
                    FROM kg_virt_entities
                    WHERE id = %s
                    """,
                    (entity_id,)
                )
            else:
                cur.execute(
                    """
                    SELECT id, name, eko_id, type
                    FROM kg_virt_entities
                    WHERE type = 'company'
                    """
                )

            entities = []
            for row in cur.fetchall():
                id_, name, eko_id, entity_type = row
                entities.append({
                    "id": id_,
                    "name": name,
                    "eko_id": eko_id,
                    "type": entity_type
                })

            return entities


def analyze_selective_highlighting(
    run_model: AnalysisRunModel,
    virtual_entity: VirtualEntityExpandedModel,
    cherry_picking: bool = True,
    flooding: bool = True,
    save_results: bool = True
) -> Tuple[List[CherryPickingModel], List[FloodingModel]]:
    """
    Analyze selective highlighting for one or all virtual entities.

    Args:
        run_model: Run model for tracking
        virt_entity_id: Optional ID of the virtual entity to analyze
        cherry_picking: Whether to detect cherry picking
        flooding: Whether to detect flooding
        save_results: Whether to save results to database (False for dry run)

    Returns:
        Tuple of (cherry_picking_instances, flooding_instances)
    """
    # No longer using PipelineTracker
    cherry_picking_results = []
    flooding_results = []

    entity_name = virtual_entity.title

    logger.info(f"Analyzing selective highlighting for entity: {entity_name} (ID: {virtual_entity.id})")

    # Clear existing results for this entity if we're saving results
    if save_results:
        if cherry_picking:
            CherryPickingDAO.delete_by_entity(virtual_entity.id)

        if flooding:
            FloodingDAO.delete_by_entity(virtual_entity.id)

    # Detect cherry picking
    if cherry_picking:
        # Create trace collector for cherry picking detection
        cp_trace_collector = create_trace_collector(str(run_model.id), "cherry_picking_detection")
        
        # Get initial cherry picking instances
        cp_instances = detect_cherry_picking_pgvector_knn(virtual_entity, run_model.id, cp_trace_collector)

        # Merge similar cherry picking instances
        if len(cp_instances) > 1:
            logger.info(f"Merging similar cherry picking instances for {entity_name}")
            merge_trace_collector = create_trace_collector(str(run_model.id), "cherry_picking_merging")
            cp_instances = merge_similar_cherry_picking_models(cp_instances, merge_trace_collector)
            logger.info(f"After merging: {len(cp_instances)} cherry picking instances for {entity_name}")

        # Trace data will be stored separately in trace_json column by DAO

        for instance in cp_instances:
            # Save to database if not in dry run mode
            if save_results:    
                # Pass trace data separately to DAO for storage in trace_json column
                trace_data = cp_trace_collector.get_trace_data() if cp_trace_collector else None
                saved_instance = CherryPickingDAO.create(instance, trace_data)
                cherry_picking_results.append(saved_instance)
            else:
                cherry_picking_results.append(instance)

        logger.info(f"Final count: {len(cp_instances)} cherry picking instances for {entity_name}")

    # Detect flooding
    if flooding:
        # Create trace collector for flooding detection
        flood_trace_collector = create_trace_collector(str(run_model.id), "flooding_detection")
        
        # Get initial flooding instances
        flooding_instances = detect_flooding_pgvector_knn(virtual_entity, run_model.id, flood_trace_collector)

        # Merge similar flooding instances
        if len(flooding_instances) > 1:
            logger.info(f"Merging similar flooding instances for {entity_name}")
            merge_trace_collector = create_trace_collector(str(run_model.id), "flooding_merging")
            flooding_instances = merge_similar_flooding_models(flooding_instances, merge_trace_collector)
            logger.info(f"After merging: {len(flooding_instances)} flooding instances for {entity_name}")

        for instance in flooding_instances:
            # Save to database if not in dry run mode
            if save_results:
                # Pass trace data separately to DAO for storage in trace_json column
                trace_data = flood_trace_collector.get_trace_data() if flood_trace_collector else None
                saved_instance = FloodingDAO.create(instance, trace_data)
                flooding_results.append(saved_instance)
            else:
                flooding_results.append(instance)

        logger.info(f"Final count: {len(flooding_instances)} flooding instances for {entity_name}")

    # Print profiling summary
    logger.info("Performance profiling summary:")
    print_profiling_summary()
    with get_bo_conn() as conn:
        XferData.sync_cherry_to_xfer_v2(conn, not_none(run_model.id))

    return cherry_picking_results, flooding_results
