"""
Data access for selective highlighting detection.
"""
import numpy as np
from loguru import logger
from psycopg2.extensions import register_adapter, AsIs
from typing import List, Optional, Dict, Any

from eko.analysis_v2.selective_highlighting.models import CherryPickingModel, FloodingModel
from eko.db import get_bo_conn
from eko.typing import not_none


def register_numpy_adapters():
    """Register adapters for NumPy arrays."""
    def adapt_numpy_float64(np_float64):
        return AsIs(float(np_float64))

    def adapt_numpy_int64(np_int64):
        return AsIs(int(np_int64))

    def adapt_numpy_array(numpy_array):
        return AsIs(f"'{{{','.join(str(x) for x in numpy_array)}}}'::float[]")

    register_adapter(np.float64, adapt_numpy_float64)
    register_adapter(np.int64, adapt_numpy_int64)
    register_adapter(np.ndarray, adapt_numpy_array)


register_numpy_adapters()


class CherryPickingDAO:
    """Data access object for cherry picking detection."""

    @staticmethod
    def create(model: CherryPickingModel, trace_data: Optional[Dict[str, Any]] = None) -> CherryPickingModel:
        """Create a new cherry picking instance."""
        with get_bo_conn() as conn:
            with conn.cursor() as cur:
                # Convert citations to JSON if they exist
                citations_json = None
                if hasattr(model, 'citations') and model.citations:
                    try:
                        import json
                        citations_json = json.dumps([citation.model_dump() for citation in model.citations])
                    except Exception as e:
                        logger.error(f"Error converting citations to JSON: {e}")

                # Get statement IDs from the full statement objects
                positive_statement_ids = model.positive_statement_ids
                negative_statement_ids = model.negative_statement_ids

                # Convert trace data to JSON if provided
                trace_json = None
                if trace_data:
                    try:
                        import json
                        trace_json = json.dumps(trace_data)
                    except Exception as e:
                        logger.error(f"Error converting trace data to JSON: {e}")

                cur.execute(
                    """
                    INSERT INTO ana_cherry (
                        virt_entity_id, positive_statement_ids, negative_statement_ids,
                        domain_vector, average_positive_impact, negative_impact,
                        repeat_count, run_id, analysis, reason, label, citations,
                        severity, confidence, authenticity, trace_json
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    RETURNING id, created_at
                    """,
                    (
                        model.virt_entity_id,
                        positive_statement_ids,
                        negative_statement_ids,
                        model.domain_vector,
                        model.average_positive_impact,
                        model.negative_impact,
                        model.repeat_count,
                        model.run_id,
                        model.analysis,
                        model.reason,
                        model.label,
                        citations_json,
                        model.severity,
                        model.confidence,
                        model.authenticity,
                        trace_json,
                    ),
                )
                result = cur.fetchone()
                if result:
                    id_, created_at = result
                    model.id = id_
                    model.created_at = created_at
                return model

    @staticmethod
    def get_by_entity(virt_entity_id: int, run_id: int) -> List[CherryPickingModel]:
        """Get all cherry picking instances for a virtual entity."""
        models = []
        with get_bo_conn() as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT * FROM ana_cherry
                    WHERE virt_entity_id = %s
                    AND run_id=%s
                    ORDER BY created_at DESC
                    """,
                    (virt_entity_id, run_id),
                )
                columns = [desc[0] for desc in cur.description] if cur.description else []
                for row in cur.fetchall():
                    # Convert row to dict using column names
                    row_dict = {columns[i]: value for i, value in enumerate(row)}

                    # Parse domain_vector if it's a string
                    if 'domain_vector' in row_dict and isinstance(row_dict['domain_vector'], str):
                        try:
                            # Remove brackets and split by comma
                            vector_str = row_dict['domain_vector'].strip('[]')
                            vector_values = [float(x.strip()) for x in vector_str.split(',')]
                            row_dict['domain_vector'] = vector_values
                        except Exception as e:
                            # If parsing fails, set to None
                            row_dict['domain_vector'] = None

                    # Parse citations if they exist
                    if 'citations' in row_dict and row_dict['citations']:
                        try:
                            import json
                            from eko.models.citation_model import Citation
                            # Check if citations is already a list (not a string)
                            if isinstance(row_dict['citations'], list):
                                citations_data = row_dict['citations']
                            else:
                                citations_data = json.loads(row_dict['citations'])
                            row_dict['citations'] = [Citation(**citation) for citation in citations_data]
                        except Exception as e:
                            logger.error(f"Error parsing citations: {e}")
                            logger.exception(e)
                            row_dict['citations'] = []

                    # Parse trace data if it exists
                    if 'trace_json' in row_dict and row_dict['trace_json']:
                        try:
                            import json
                            row_dict['trace_data'] = json.loads(row_dict['trace_json'])
                        except Exception as e:
                            logger.error(f"Error parsing trace data: {e}")
                            logger.exception(e)
                            row_dict['trace_data'] = None

                    # Get full statement objects for positive and negative statements
                    positive_statement_ids = row_dict.get('positive_statement_ids', [])
                    negative_statement_ids = row_dict.get('negative_statement_ids', [])

                    # Fetch statements using StatementData DAO
                    from eko.db.data.statement import StatementData
                    positive_statements = []
                    negative_statements = []

                    if positive_statement_ids:
                        positive_statements = list(StatementData.get_statements_by_ids(conn, positive_statement_ids).values())

                    if negative_statement_ids:
                        negative_statements = list(StatementData.get_statements_by_ids(conn, negative_statement_ids).values())

                    # Create model with full statement objects
                    model = CherryPickingModel(
                        id=row_dict.get('id'),
                        virt_entity_id=row_dict['virt_entity_id'],  # Required field, must exist
                        positive_statements=positive_statements,
                        negative_statements=negative_statements,
                        domain_vector=not_none(row_dict.get('domain_vector')),
                        average_positive_impact=float(row_dict['average_positive_impact']),  # Required field, convert to float
                        negative_impact=float(row_dict['negative_impact']),  # Required field, convert to float
                        repeat_count=int(row_dict['repeat_count']),  # Required field, convert to int
                        created_at=row_dict.get('created_at'),
                        run_id=row_dict.get('run_id'),
                        analysis=row_dict.get('analysis', ''),
                        reason=row_dict.get('reason', ''),
                        label=row_dict.get('label', ''),
                        citations=row_dict.get('citations', []),
                        severity=int(row_dict.get('severity', 0) or 0),
                        confidence=int(row_dict.get('confidence', 0) or 0),
                        authenticity=int(row_dict.get('authenticity', 0) or 0),
                        trace_data=row_dict.get('trace_data')
                    )

                    models.append(model)
                return models

    @staticmethod
    def delete_by_run(run_id: int) -> None:
        """Delete all cherry picking instances for a run."""
        with get_bo_conn() as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    DELETE FROM ana_cherry
                    WHERE run_id = %s
                    """,
                    (run_id,)
                )

    @staticmethod
    def delete_by_entity(virt_entity_id: int) -> None:
        """Delete all cherry picking instances for a virtual entity."""
        with get_bo_conn() as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    DELETE FROM ana_cherry
                    WHERE virt_entity_id = %s
                    """,
                    (virt_entity_id,)
                )


class FloodingDAO:
    """Data access object for flooding detection."""

    @staticmethod
    def create(model: FloodingModel, trace_data: Optional[Dict[str, Any]] = None) -> FloodingModel:
        """Create a new flooding instance with optional trace data."""
        with get_bo_conn() as conn:
            with conn.cursor() as cur:
                # Convert citations to JSON if they exist
                citations_json = None
                if hasattr(model, 'citations') and model.citations:
                    try:
                        import json
                        citations_json = json.dumps([citation.model_dump() for citation in model.citations])
                    except Exception as e:
                        logger.error(f"Error converting citations to JSON: {e}")

                # Get statement IDs from the full statement objects
                positive_statement_ids = model.positive_statement_ids
                negative_statement_ids = model.negative_statement_ids

                # Convert trace data to JSON if provided
                trace_json = None
                if trace_data:
                    try:
                        import json
                        trace_json = json.dumps(trace_data)
                    except Exception as e:
                        logger.error(f"Error converting trace data to JSON: {e}")

                cur.execute(
                    """
                    INSERT INTO ana_flooding (
                        virt_entity_id, positive_statement_ids, negative_statement_ids,
                        domain_vector, average_positive_impact, negative_impact,
                        positive_count, run_id, analysis, reason, label, citations,
                        severity, confidence, authenticity, trace_json
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    RETURNING id, created_at
                    """,
                    (
                        model.virt_entity_id,
                        positive_statement_ids,
                        negative_statement_ids,
                        model.domain_vector,
                        model.average_positive_impact,
                        model.negative_impact,
                        model.positive_count,
                        model.run_id,
                        model.analysis,
                        model.reason,
                        model.label,
                        citations_json,
                        model.severity,
                        model.confidence,
                        model.authenticity,
                        trace_json,
                    )
                )
                result = cur.fetchone()
                if result:
                    id_, created_at = result
                    model.id = id_
                    model.created_at = created_at
                return model

    @staticmethod
    def get_by_entity(virt_entity_id: int, run_id: int) -> List[FloodingModel]:
        """Get all flooding instances for a virtual entity."""
        models = []
        with get_bo_conn() as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT * FROM ana_flooding
                    WHERE virt_entity_id = %s
                    AND run_id=%s
                    ORDER BY created_at DESC
                    """,
                    (virt_entity_id,run_id)
                )
                columns = [desc[0] for desc in cur.description] if cur.description else []
                for row in cur.fetchall():
                    # Convert row to dict using column names
                    row_dict = {columns[i]: value for i, value in enumerate(row)}

                    # Parse domain_vector if it's a string
                    if 'domain_vector' in row_dict and isinstance(row_dict['domain_vector'], str):
                        try:
                            # Remove brackets and split by comma
                            vector_str = row_dict['domain_vector'].strip('[]')
                            vector_values = [float(x.strip()) for x in vector_str.split(',')]
                            row_dict['domain_vector'] = vector_values
                        except Exception as e:
                            # If parsing fails, set to None
                            row_dict['domain_vector'] = None

                    # Parse citations if they exist
                    if 'citations' in row_dict and row_dict['citations']:
                        try:
                            import json
                            from eko.models.citation_model import Citation
                            # Check if citations is already a list (not a string)
                            if isinstance(row_dict['citations'], list):
                                citations_data = row_dict['citations']
                            else:
                                citations_data = json.loads(row_dict['citations'])
                            row_dict['citations'] = [Citation(**citation) for citation in citations_data]
                        except Exception as e:
                            logger.error(f"Error parsing citations: {e}")
                            logger.exception(e)
                            row_dict['citations'] = []

                    # Parse trace data if it exists
                    if 'trace_json' in row_dict and row_dict['trace_json']:
                        try:
                            import json
                            row_dict['trace_data'] = json.loads(row_dict['trace_json'])
                        except Exception as e:
                            logger.error(f"Error parsing trace data: {e}")
                            logger.exception(e)
                            row_dict['trace_data'] = None

                    # Get full statement objects for positive and negative statements
                    positive_statement_ids = row_dict.get('positive_statement_ids', [])
                    negative_statement_ids = row_dict.get('negative_statement_ids', [])

                    # Fetch statements using StatementData DAO
                    from eko.db.data.statement import StatementData
                    positive_statements = []
                    negative_statements = []

                    if positive_statement_ids:
                        positive_statements = list(StatementData.get_statements_by_ids(conn, positive_statement_ids).values())

                    if negative_statement_ids:
                        negative_statements = list(StatementData.get_statements_by_ids(conn, negative_statement_ids).values())

                    # Create model with full statement objects
                    model = FloodingModel(
                        id=row_dict.get('id'),
                        virt_entity_id=row_dict['virt_entity_id'],  # Required field, must exist
                        positive_statements=positive_statements,
                        negative_statements=negative_statements,
                        domain_vector=not_none(row_dict.get('domain_vector')),
                        average_positive_impact=float(row_dict['average_positive_impact']),  # Required field, convert to float
                        negative_impact=float(row_dict['negative_impact']),  # Required field, convert to float
                        positive_count=int(row_dict['positive_count']),  # Required field, convert to int
                        created_at=row_dict.get('created_at'),
                        run_id=row_dict.get('run_id'),
                        analysis=row_dict.get('analysis', ''),
                        reason=row_dict.get('reason', ''),
                        label=row_dict.get('label', ''),
                        citations=row_dict.get('citations', []),
                        severity=int(row_dict.get('severity', 0) or 0),
                        confidence=int(row_dict.get('confidence', 0) or 0),
                        authenticity=int(row_dict.get('authenticity', 0) or 0),
                        trace_data=row_dict.get('trace_data')
                    )

                    models.append(model)
                return models

    @staticmethod
    def delete_by_run(run_id: int) -> None:
        """Delete all flooding instances for a run."""
        with get_bo_conn() as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    DELETE FROM ana_flooding
                    WHERE run_id = %s
                    """,
                    (run_id,)
                )

    @staticmethod
    def delete_by_entity(virt_entity_id: int) -> None:
        """Delete all flooding instances for a virtual entity."""
        with get_bo_conn() as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    DELETE FROM ana_flooding
                    WHERE virt_entity_id = %s
                    """,
                    (virt_entity_id,)
                )
