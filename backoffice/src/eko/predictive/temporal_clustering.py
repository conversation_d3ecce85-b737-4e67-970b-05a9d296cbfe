"""
Temporal clustering functionality for predictive analytics.

This module provides functions for clustering statements by year and tracking
the evolution of clusters over time.
"""

from typing import List, Dict, Optional, Any, cast

import numpy as np
from loguru import logger
from psycopg import Cursor

from eko.analysis_v2.effects.clustering import perform_clustering, ClusteringMethod
from eko.analysis_v2.trace_collector import TraceCollector
from eko.db import get_bo_conn
from eko.models.vector.demise.demise_model import DEMISEModel
from eko.models.vector.derived.enums import PipelineStage
from eko.models.virtual_entity import VirtualEntityExpandedModel
from eko.predictive.dao import TemporalClusterDAO
from eko.predictive.models import TemporalClusterModel
from eko.settings import settings


def get_statements_by_year(
    cursor: Cursor,
    virtual_entity_id: int,
    start_year: int,
    end_year: int
) -> Dict[int, List[Dict[str, Any]]]:
    """
    Get statements for a virtual entity grouped by year.

    Args:
        cursor: Database cursor
        virtual_entity_id: Virtual entity ID
        start_year: Start year (inclusive)
        end_year: End year (inclusive)

    Returns:
        Dictionary mapping years to lists of statements
    """
    try:
        # Query statements with their DEMISE embeddings
        cursor.execute(
            """
            SELECT s.id, s.statement_text, s.demise_embedding, s.model_json, s.start_year
            FROM kg_statements s
            JOIN kg_virt_entity_map vem ON s.company_id = vem.base_entity_id
            WHERE vem.virt_entity_id = %s
              AND s.start_year BETWEEN %s AND %s
              AND s.demise_embedding IS NOT NULL
              AND (s.is_environmental OR s.is_social OR s.is_governance OR s.is_animal_welfare)
            ORDER BY s.start_year
        """,
            (virtual_entity_id, start_year, end_year),
        )

        statements_by_year = {}
        for row in cursor.fetchall():
            year = row[4]
            if year not in statements_by_year:
                statements_by_year[year] = []

            # Parse the DEMISE model from JSON
            # If row[3] is already a dict, use model_validate instead of model_validate_json
            if isinstance(row[3], dict):
                demise_model = DEMISEModel.model_validate(row[3])
            else:
                demise_model = DEMISEModel.model_validate_json(row[3])

            statements_by_year[year].append({
                "id": row[0],
                "text": row[1],
                "embedding": row[2],
                "year": year
            })

        return statements_by_year
    except Exception as e:
        logger.exception(f"Error getting statements by year: {str(e)}")
        raise


def create_temporal_clusters(
    virtual_entity: VirtualEntityExpandedModel,
    start_year: int,
    end_year: int,
    trace_collector: Optional[TraceCollector] = None,
    run_id: Optional[int] = None
) -> List[TemporalClusterModel]:
    """
    Create temporal clusters for a virtual entity.

    Args:
        virtual_entity: Virtual entity model
        start_year: Start year (inclusive)
        end_year: End year (inclusive)
        trace_collector: Optional trace collector
        run_id: Optional run ID, if not provided a new run will be created

    Returns:
        List of created temporal cluster models
    """
    # If no run_id is provided, create a new run
    if run_id is None:
        with get_bo_conn() as conn:
            from eko.db.data.run import RunData
            run = RunData.create_historical_run(
                conn=conn,
                start_year=start_year,
                end_year=end_year,
                models=["predictive"],
                target=virtual_entity.short_id
            )
            run_id = run.id
            logger.info(f"Created new run with ID {run_id} for predictive analysis")

    # Ensure run_id is not None
    assert run_id is not None, "Run ID must be provided or created"
    if trace_collector:
        trace_collector.start_step("temporal_clustering")

    logger.info(f"Creating temporal clusters for {virtual_entity.name} ({start_year}-{end_year})")

    created_clusters = []

    with get_bo_conn() as conn:
        with conn.cursor() as cursor:
            # Get statements grouped by year
            statements_by_year = get_statements_by_year(
                cursor, virtual_entity.id, start_year, end_year
            )

            # Process each year
            for year, statements in statements_by_year.items():
                if len(statements) < settings.predictive_statement_clustering_min_samples:
                    logger.info(f"Skipping year {year} - not enough statements ({len(statements)})")
                    continue

                logger.info(f"Processing year {year} with {len(statements)} statements")

                # Extract embeddings for clustering
                statement_ids = [s["id"] for s in statements]

                # Convert embeddings to proper format
                embeddings = []
                for s in statements:
                    # Check if embedding is a string (JSON array) and convert it
                    if isinstance(s["embedding"], str):
                        try:
                            # Try to parse as JSON
                            import json
                            embedding = json.loads(s["embedding"])
                            embeddings.append(embedding)
                        except json.JSONDecodeError:
                            # If not valid JSON, try to parse as string representation of list
                            import ast
                            try:
                                embedding = ast.literal_eval(s["embedding"])
                                embeddings.append(embedding)
                            except (ValueError, SyntaxError):
                                logger.warning(f"Could not parse embedding for statement {s['id']}")
                                continue
                    else:
                        # Already a list or array
                        embeddings.append(s["embedding"])

                # Convert to numpy array
                effect_array = np.array(embeddings)

                # Perform clustering
                clusters, n_clusters = perform_clustering(
                    effect_array,
                    settings.predictive_statement_clustering_eps,
                    settings.predictive_statement_clustering_min_samples,
                    ClusteringMethod[settings.predictive_statement_clustering_method]
                )

                # Process each cluster
                for cluster_idx in range(n_clusters):
                    # Get statements in this cluster
                    cluster_mask = clusters == cluster_idx
                    cluster_statement_ids = [statement_ids[i] for i, is_in_cluster in enumerate(cluster_mask) if is_in_cluster]

                    if len(cluster_statement_ids) < settings.predictive_statement_clustering_min_samples:
                        logger.debug(f"Skipping small cluster with {len(cluster_statement_ids)} statements")
                        continue

                    # Calculate cluster centroid
                    cluster_embeddings = effect_array[cluster_mask]
                    centroid = np.mean(cluster_embeddings, axis=0).tolist()

                    # Calculate coherence (average distance to centroid)
                    distances = np.linalg.norm(cluster_embeddings - np.array(centroid), axis=1)
                    coherence = float(np.mean(distances))


                    # Create temporal cluster model
                    cluster_model = TemporalClusterModel(
                        run_id=run_id,
                        virtual_entity_id=virtual_entity.id,
                        year=year,
                        centroid=centroid,
                        statement_ids=cluster_statement_ids,
                        size=len(cluster_statement_ids),
                        coherence=coherence
                    )

                    # Save to database
                    created_cluster = TemporalClusterDAO.create(cursor, cluster_model)
                    created_clusters.append(created_cluster)

                    logger.info(f"Created temporal cluster for year {year} with {len(cluster_statement_ids)} statements")

            conn.commit()

    if trace_collector:
        trace_collector.end_step("temporal_clustering", result={
            "clusters_created": len(created_clusters),
            "years_processed": len(statements_by_year)
        })

    return created_clusters


def calculate_demise_centroid(vectors: np.ndarray) -> np.ndarray:
    """
    Calculate the centroid of multiple vectors by averaging them.
    Works purely with numpy arrays for efficiency and consistency.

    Args:
        vectors: Numpy array of vectors with shape (n_vectors, vector_size)

    Returns:
        Centroid vector as numpy array
    """
    if vectors.size == 0:
        raise ValueError("Cannot calculate centroid of empty array")

    # Verify that we have valid vectors with non-zero elements
    if np.all(vectors == 0):
        error_msg = f"ERROR: All vectors are zeros"
        logger.error(error_msg)
        raise ValueError(error_msg)

    # Calculate the mean vector (centroid)
    centroid_vector = np.mean(vectors, axis=0)

    # # Check if the centroid has non-zero values
    # if np.count_nonzero(centroid_vector) < 5:  # Require at least 5 non-zero values
    #     error_msg = f"ERROR: Centroid vector has too few non-zero values: {np.count_nonzero(centroid_vector)}"
    #     logger.error(error_msg)
    #
    #     # Log the input data to help diagnose the issue
    #     logger.error(f"Input vectors non-zero counts: {[np.count_nonzero(v) for v in vectors]}")
    #
    #     # Raise an exception
    #     raise ValueError(error_msg)
    #
    return centroid_vector


def get_temporal_clusters(
    cursor: Cursor,
    virtual_entity_id: int,
    start_year: int,
    end_year: int
) -> List[TemporalClusterModel]:
    """
    Get temporal clusters for a virtual entity within a year range.

    Args:
        cursor: Database cursor
        virtual_entity_id: Virtual entity ID
        start_year: Start year (inclusive)
        end_year: End year (inclusive)

    Returns:
        List of temporal cluster models
    """
    return TemporalClusterDAO.list_by_entity_and_year_range(
        cursor, virtual_entity_id, start_year, end_year
    )
