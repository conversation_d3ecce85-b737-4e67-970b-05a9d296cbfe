# Vector Models Knowledge Base

## Vector Model Structure

- The codebase uses a vector model framework based on `BaseVectorModel` to handle embeddings
- The main implementation is `DEMISEModel` for ethical analysis of statements
- The vectors are stored in PostgreSQL's vector type

## Effect and EffectFlag Models

- `EffectModel` and `EffectFlagModel` represent clustered statements with similar ethical impacts
- Both models use `start_year` and `end_year` fields to represent time ranges
- All statements in a single effect model must be from the same start year
- Effect flags from adjacent years (abs difference ≤ 1) can be merged during analysis
- The old `year` field is deprecated and should not be used

## Vector Sizes and Database Schema

- **demise_embedding**: 1024-dimensional vector (was previously 380-dimensional)
- **text_embedding**: 1536-dimensional vector
- **domain_vector**: 1024-dimensional vector in ana_cherry and ana_flooding tables

## Key Methods in BaseVectorModel

- `to_vector()`: Converts model to a vector (unsized)
- `to_fixed_size_vector(size=1024)`: Creates a vector of exactly 1024 dimensions by:
  - Truncating larger vectors
  - Padding smaller vectors with zeros
- `to_kv()`: Converts model to key-value pairs
- `to_kv_sparse()`: Converts model to sparse key-value pairs (non-zero values only)
- `from_sparse_kv()`: Creates model from sparse key-value representation
- Other utility methods for vector manipulation and documentation

## Statement Embeddings

- Statement embeddings are managed in `/backoffice/src/eko/db/data/statement_embeddings.py`
- `add_embeddings_to_statement()` function stores both demise_embedding and text_embedding
- Both embedding types are stored in the `kg_statements_v2` table

## Fix Commands

- The `fix statement-embeddings` command in `/backoffice/src/cli/fix_command.py` can update embeddings for statements
- This command processes statements in batches, generating both demise_embedding and text_embedding
- Can limit the number of statements processed with `--limit` option

## Working with Embeddings

When working with embeddings:
1. Always use `to_fixed_size_vector(1024)` for demise_embedding
2. Use standard `get_embedding()` for text_embedding (1536 dimensions)
3. Run database migrations when changing vector sizes
