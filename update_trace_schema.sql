-- Update trace schema: Add missing trace_json columns and clean up indexes
-- This script adds trace_json JSONB columns to important analysis tables that are missing them

\echo 'Updating trace schema for analysis tables...'

-- Add trace_json column to ana_runs (critical for run tracking)
\echo 'Adding trace_json to ana_runs...'
ALTER TABLE ana_runs
    ADD COLUMN IF NOT EXISTS trace_json JSONB;

-- Add trace_json column to ana_responsibility (if used)
\echo 'Adding trace_json to ana_responsibility...'
ALTER TABLE ana_responsibility
    ADD COLUMN IF NOT EXISTS trace_json JSONB;

-- Add trace_json column to ana_clusters (if used for clustering analysis)
\echo 'Adding trace_json to ana_clusters...'
ALTER TABLE ana_clusters
    ADD COLUMN IF NOT EXISTS trace_json JSONB;

-- Clean up duplicate indexes (keep the ones with consistent naming)
\echo 'Cleaning up duplicate trace indexes...'

-- Drop duplicate indexes (keep the _trace_json ones)
DROP INDEX IF EXISTS idx_ana_cherry_trace;
DROP INDEX IF EXISTS idx_ana_claims_trace;
DROP INDEX IF EXISTS idx_ana_flooding_trace;
DROP INDEX IF EXISTS idx_ana_promises_trace;

-- Ensure we have GIN indexes on all trace_json columns
\echo 'Creating GIN indexes for efficient JSONB querying...'

CREATE INDEX IF NOT EXISTS idx_ana_runs_trace_json ON ana_runs USING gin (trace_json);
CREATE INDEX IF NOT EXISTS idx_ana_responsibility_trace_json ON ana_responsibility USING gin (trace_json);
CREATE INDEX IF NOT EXISTS idx_ana_clusters_trace_json ON ana_clusters USING gin (trace_json);

-- Verify the schema updates
\echo 'Verifying trace_json columns...'
SELECT table_name,
       column_name,
       data_type
FROM information_schema.columns
WHERE table_schema = 'public'
  AND column_name = 'trace_json'
  AND table_name LIKE 'ana_%'
ORDER BY table_name;

\echo 'Verifying trace indexes...'
SELECT schemaname,
       tablename,
       indexname
FROM pg_indexes
WHERE indexname LIKE '%trace%'
  AND schemaname = 'public'
ORDER BY tablename, indexname;

\echo 'Trace schema update completed!'
