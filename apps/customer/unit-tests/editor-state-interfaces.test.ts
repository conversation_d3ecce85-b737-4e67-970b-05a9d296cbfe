/**
 * Unit tests for consolidated state interfaces
 * Tests the factory functions and type validation for editor state management
 */

import { describe, expect, test } from '@jest/globals';
import {
  createInitialComponentUpdateState,
  createInitialGroupStatusState,
  createInitialComponentRegistrationState,
  createInitialAutoSaveState,
  createInitialVersioningState,
  createInitialDocumentState,
  createInitialEditorState,
  createInitialComponentStatusState,
  createInitialComponentSyncState,
  createInitialPerformanceState,
  createInitialMonitoringState,
  createInitialEditorStoreState,
  ComponentStatus,
  type ComponentUpdateState,
  type GroupStatusState,
  type ComponentRegistrationState,
  type AutoSaveState,
  type EditorStoreState
} from '../components/editor/types/state-interfaces';

describe('State Interface Factory Functions', () => {
  
  describe('createInitialComponentUpdateState', () => {
    test('should create initial state with empty sets', () => {
      const state = createInitialComponentUpdateState();
      
      expect(state.updatingComponents).toBeInstanceOf(Set);
      expect(state.updatingComponents.size).toBe(0);
      expect(state.pendingSaves).toBeInstanceOf(Set);
      expect(state.pendingSaves.size).toBe(0);
      expect(state.processingUpdates).toBeInstanceOf(Set);
      expect(state.processingUpdates.size).toBe(0);
      expect(state.pendingRegistrations).toBeInstanceOf(Set);
      expect(state.pendingRegistrations.size).toBe(0);
    });

    test('should create independent instances', () => {
      const state1 = createInitialComponentUpdateState();
      const state2 = createInitialComponentUpdateState();
      
      state1.updatingComponents.add('test');
      expect(state2.updatingComponents.size).toBe(0);
    });
  });

  describe('createInitialGroupStatusState', () => {
    test('should create initial state with empty collections and false flag', () => {
      const state = createInitialGroupStatusState();
      
      expect(state.groupStatusUpdateTimeouts).toBeInstanceOf(Map);
      expect(state.groupStatusUpdateTimeouts.size).toBe(0);
      expect(state.pendingGroupUpdates).toBeInstanceOf(Set);
      expect(state.pendingGroupUpdates.size).toBe(0);
      expect(state.updateChain).toBeInstanceOf(Set);
      expect(state.updateChain.size).toBe(0);
      expect(state.isProcessingBatch).toBe(false);
    });
  });

  describe('createInitialComponentRegistrationState', () => {
    test('should create initial state with empty maps and sets', () => {
      const state = createInitialComponentRegistrationState();
      
      expect(state.orphanedComponents).toBeInstanceOf(Map);
      expect(state.orphanedComponents.size).toBe(0);
      expect(state.registeredComponents).toBeInstanceOf(Set);
      expect(state.registeredComponents.size).toBe(0);
      expect(state.componentHierarchy).toBeInstanceOf(Map);
      expect(state.componentHierarchy.size).toBe(0);
    });
  });

  describe('createInitialAutoSaveState', () => {
    test('should create initial state with null timeouts and defaults', () => {
      const state = createInitialAutoSaveState();
      
      expect(state.saveTimeoutId).toBeNull();
      expect(state.versionTimeoutId).toBeNull();
      expect(state.lastContent).toBe('');
      expect(state.lastData).toBeNull();
      expect(state.hasUnsavedChanges).toBe(false);
      expect(state.isAutoSaving).toBe(false);
    });
  });

  describe('createInitialVersioningState', () => {
    test('should create initial state with empty values', () => {
      const state = createInitialVersioningState();
      
      expect(state.currentVersion).toBe('');
      expect(state.versions).toEqual([]);
      expect(state.isCreatingVersion).toBe(false);
      expect(state.lastAutoSaveTime).toBe(0);
    });
  });

  describe('createInitialDocumentState', () => {
    test('should create initial state with default document values', () => {
      const state = createInitialDocumentState();
      
      expect(state.id).toBe('');
      expect(state.title).toBe('');
      expect(state.content).toBe('');
      expect(state.isLoading).toBe(false);
      expect(state.hasUnsavedChanges).toBe(false);
      expect(state.lastModified).toBeInstanceOf(Date);
      expect(state.entityChangeListeners).toBeInstanceOf(Set);
      expect(state.entityChangeListeners.size).toBe(0);
    });
  });

  describe('createInitialEditorState', () => {
    test('should create initial state with default editor values', () => {
      const state = createInitialEditorState();
      
      expect(state.isInitialized).toBe(false);
      expect(state.hasSetInitialContent).toBe(false);
      expect(state.renderCount).toBe(0);
      expect(state.lastRenderTime).toBe(0);
      expect(state.isEditorFocused).toBe(false);
    });
  });

  describe('createInitialComponentStatusState', () => {
    test('should create initial state with empty maps and sets', () => {
      const state = createInitialComponentStatusState();
      
      expect(state.componentStatuses).toBeInstanceOf(Map);
      expect(state.componentStatuses.size).toBe(0);
      expect(state.statusSyncTimeouts).toBeInstanceOf(Map);
      expect(state.statusSyncTimeouts.size).toBe(0);
      expect(state.lastSyncedStatuses).toBeInstanceOf(Map);
      expect(state.lastSyncedStatuses.size).toBe(0);
      expect(state.isSyncing).toBeInstanceOf(Set);
      expect(state.isSyncing.size).toBe(0);
      expect(state.previousStatuses).toBeInstanceOf(Map);
      expect(state.previousStatuses.size).toBe(0);
    });
  });

  describe('createInitialComponentSyncState', () => {
    test('should create initial state with empty maps', () => {
      const state = createInitialComponentSyncState();
      
      expect(state.isRegistered).toBeInstanceOf(Map);
      expect(state.isRegistered.size).toBe(0);
      expect(state.loadingStates).toBeInstanceOf(Map);
      expect(state.loadingStates.size).toBe(0);
      expect(state.errorStates).toBeInstanceOf(Map);
      expect(state.errorStates.size).toBe(0);
      expect(state.syncTimestamps).toBeInstanceOf(Map);
      expect(state.syncTimestamps.size).toBe(0);
    });
  });

  describe('createInitialPerformanceState', () => {
    test('should create initial state with empty maps and zero memory', () => {
      const state = createInitialPerformanceState();
      
      expect(state.renderCounts).toBeInstanceOf(Map);
      expect(state.renderCounts.size).toBe(0);
      expect(state.renderTimes).toBeInstanceOf(Map);
      expect(state.renderTimes.size).toBe(0);
      expect(state.memoryUsage).toBe(0);
      expect(state.componentLoadTimes).toBeInstanceOf(Map);
      expect(state.componentLoadTimes.size).toBe(0);
    });
  });

  describe('createInitialMonitoringState', () => {
    test('should create initial state with empty collections', () => {
      const state = createInitialMonitoringState();
      
      expect(state.activeScopes).toBeInstanceOf(Set);
      expect(state.activeScopes.size).toBe(0);
      expect(state.resourceUsage).toBeInstanceOf(Map);
      expect(state.resourceUsage.size).toBe(0);
      expect(state.errorCounts).toBeInstanceOf(Map);
      expect(state.errorCounts.size).toBe(0);
      expect(state.warnings).toEqual([]);
    });
  });

  describe('createInitialEditorStoreState', () => {
    test('should create complete initial state with all subsections', () => {
      const state = createInitialEditorStoreState();
      
      // Verify all top-level properties exist
      expect(state).toHaveProperty('document');
      expect(state).toHaveProperty('editor');
      expect(state).toHaveProperty('componentUpdate');
      expect(state).toHaveProperty('componentRegistration');
      expect(state).toHaveProperty('componentStatus');
      expect(state).toHaveProperty('componentSync');
      expect(state).toHaveProperty('groupStatus');
      expect(state).toHaveProperty('autoSave');
      expect(state).toHaveProperty('versioning');
      expect(state).toHaveProperty('performance');
      expect(state).toHaveProperty('monitoring');
      
      // Verify structure integrity
      expect(state.document.id).toBe('');
      expect(state.editor.isInitialized).toBe(false);
      expect(state.componentUpdate.updatingComponents).toBeInstanceOf(Set);
      expect(state.groupStatus.isProcessingBatch).toBe(false);
      expect(state.autoSave.hasUnsavedChanges).toBe(false);
    });

    test('should create independent state instances', () => {
      const state1 = createInitialEditorStoreState();
      const state2 = createInitialEditorStoreState();
      
      state1.componentUpdate.updatingComponents.add('test');
      state1.document.title = 'Test Document';
      
      expect(state2.componentUpdate.updatingComponents.size).toBe(0);
      expect(state2.document.title).toBe('');
    });
  });
});

describe('ComponentStatus Enum', () => {
  test('should have all expected status values', () => {
    expect(ComponentStatus.IDLE).toBe('idle');
    expect(ComponentStatus.LOADING).toBe('loading');
    expect(ComponentStatus.SUCCESS).toBe('success');
    expect(ComponentStatus.ERROR).toBe('error');
    expect(ComponentStatus.UPDATING).toBe('updating');
  });

  test('should be usable in type checking', () => {
    const status: ComponentStatus = ComponentStatus.LOADING;
    expect(status).toBe('loading');
  });
});

describe('State Interface Type Validation', () => {
  test('ComponentUpdateState should match expected structure', () => {
    const state: ComponentUpdateState = {
      updatingComponents: new Set<string>(['comp1', 'comp2']),
      pendingSaves: new Set<string>(['comp3']),
      processingUpdates: new Set<string>(),
      pendingRegistrations: new Set<string>(['comp4'])
    };
    
    expect(state.updatingComponents.has('comp1')).toBe(true);
    expect(state.pendingSaves.has('comp3')).toBe(true);
    expect(state.processingUpdates.size).toBe(0);
    expect(state.pendingRegistrations.has('comp4')).toBe(true);
  });

  test('GroupStatusState should match expected structure', () => {
    const state: GroupStatusState = {
      groupStatusUpdateTimeouts: new Map([['group1', 'timeout1']]),
      pendingGroupUpdates: new Set(['group2']),
      updateChain: new Set(['group3']),
      isProcessingBatch: true
    };
    
    expect(state.groupStatusUpdateTimeouts.get('group1')).toBe('timeout1');
    expect(state.pendingGroupUpdates.has('group2')).toBe(true);
    expect(state.updateChain.has('group3')).toBe(true);
    expect(state.isProcessingBatch).toBe(true);
  });

  test('AutoSaveState should handle timeout types correctly', () => {
    const timeout = setTimeout(() => {}, 1000);
    const state: AutoSaveState = {
      saveTimeoutId: timeout,
      versionTimeoutId: null,
      lastContent: 'test content',
      lastData: { test: true },
      hasUnsavedChanges: true,
      isAutoSaving: false
    };
    
    expect(state.saveTimeoutId).toBe(timeout);
    expect(state.versionTimeoutId).toBeNull();
    expect(state.lastContent).toBe('test content');
    expect(state.lastData).toEqual({ test: true });
    expect(state.hasUnsavedChanges).toBe(true);
    expect(state.isAutoSaving).toBe(false);
    
    clearTimeout(timeout);
  });
});

describe('State Interface Memory Management', () => {
  test('should allow proper cleanup of collections', () => {
    const state = createInitialEditorStoreState();
    
    // Add some data
    state.componentUpdate.updatingComponents.add('comp1');
    state.componentUpdate.updatingComponents.add('comp2');
    state.groupStatus.pendingGroupUpdates.add('group1');
    state.monitoring.activeScopes.add('scope1');
    
    // Verify data exists
    expect(state.componentUpdate.updatingComponents.size).toBe(2);
    expect(state.groupStatus.pendingGroupUpdates.size).toBe(1);
    expect(state.monitoring.activeScopes.size).toBe(1);
    
    // Clear collections
    state.componentUpdate.updatingComponents.clear();
    state.groupStatus.pendingGroupUpdates.clear();
    state.monitoring.activeScopes.clear();
    
    // Verify cleanup
    expect(state.componentUpdate.updatingComponents.size).toBe(0);
    expect(state.groupStatus.pendingGroupUpdates.size).toBe(0);
    expect(state.monitoring.activeScopes.size).toBe(0);
  });

  test('should handle Map operations correctly', () => {
    const state = createInitialEditorStoreState();
    
    // Add timeout tracking
    const timeout1 = setTimeout(() => {}, 1000);
    const timeout2 = setTimeout(() => {}, 2000);
    
    state.componentStatus.statusSyncTimeouts.set('comp1', timeout1);
    state.componentStatus.statusSyncTimeouts.set('comp2', timeout2);
    
    expect(state.componentStatus.statusSyncTimeouts.size).toBe(2);
    expect(state.componentStatus.statusSyncTimeouts.get('comp1')).toBe(timeout1);
    
    // Clean up timeouts
    state.componentStatus.statusSyncTimeouts.forEach(timeout => clearTimeout(timeout));
    state.componentStatus.statusSyncTimeouts.clear();
    
    expect(state.componentStatus.statusSyncTimeouts.size).toBe(0);
  });
});