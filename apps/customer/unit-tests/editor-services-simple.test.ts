/**
 * Unit tests for editor services
 * Tests actual methods that exist in componentService, dependencyService, and versioningService
 */

import { describe, expect, test, beforeEach, afterEach, jest } from '@jest/globals';

// Mock environment variables
process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test.supabase.co';
process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'test-key';

// Mock Supabase client
jest.mock('@/app/supabase/client', () => ({
  createClient: jest.fn(() => ({
    from: jest.fn(() => ({
      select: jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          data: [],
          error: null
        })
      }),
      insert: jest.fn().mockReturnValue({
        data: null,
        error: null
      }),
      upsert: jest.fn().mockReturnValue({
        data: null,
        error: null
      }),
      update: jest.fn().mockReturnValue({
        data: null,
        error: null
      }),
      delete: jest.fn().mockReturnValue({
        data: null,
        error: null
      })
    }))
  }))
}));

// Mock the logger to avoid console output during tests
jest.mock('../components/editor/services/utils/logger', () => ({
  logger: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn()
  }
}));

// Mock fetch for API calls
global.fetch = jest.fn();

// Import services after mocking
import { ComponentService } from '../components/editor/services/component/componentService';
import { DependencyService } from '../components/editor/services/component/dependencyService';
import { VersioningService } from '../components/editor/services/versioning/versioningService';
import { ComponentStatus } from '../components/editor/types/state-interfaces';

// Test component type matching ReportComponent interface
interface TestComponent {
  id: string;
  type: string;
  status: string;
  parentId?: string;
  dependencies?: string[];
  children?: string[];
  data?: any;
  title?: string;
  content?: string;
  endpoint?: string;
  prompt?: string;
  error?: string;
  lastRefreshed?: Date;
  preserved?: boolean;
  locked?: boolean;
}

describe('ComponentService', () => {
  let componentService: ComponentService;
  let mockComponents: Map<string, TestComponent>;

  beforeEach(() => {
    mockComponents = new Map();
    componentService = new ComponentService();
    (fetch as jest.Mock).mockClear();
  });

  describe('loadComponentContent', () => {
    test('should load component content successfully', async () => {
      const mockResponse = {
        ok: true,
        json: async () => ({ content: 'test content' })
      };
      (fetch as jest.Mock).mockResolvedValue(mockResponse);

      const result = await componentService.loadComponentContent('comp-123', '/api/test');
      
      expect(result.content).toBe('test content');
      expect(result.error).toBeUndefined();
      expect(fetch).toHaveBeenCalledWith('/api/test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({})
      });
    });

    test('should handle API errors', async () => {
      const mockResponse = {
        ok: false,
        statusText: 'Internal Server Error'
      };
      (fetch as jest.Mock).mockResolvedValue(mockResponse);

      const result = await componentService.loadComponentContent('comp-123', '/api/test');
      
      expect(result.content).toBe('');
      expect(result.error).toContain('API request failed');
    });

    test('should handle network errors', async () => {
      (fetch as jest.Mock).mockRejectedValue(new Error('Network error'));

      const result = await componentService.loadComponentContent('comp-123', '/api/test');
      
      expect(result.content).toBe('');
      expect(result.error).toContain('Network error');
    });
  });

  describe('validateHierarchy', () => {
    test('should return valid for correct hierarchy', () => {
      const parent: TestComponent = {
        id: 'parent-1',
        type: 'group',
        status: 'success',
        children: ['child-1']
      };

      const child: TestComponent = {
        id: 'child-1',
        type: 'section',
        status: 'idle',
        parentId: 'parent-1'
      };

      mockComponents.set('parent-1', parent);
      mockComponents.set('child-1', child);

      const result = componentService.validateHierarchy(mockComponents);
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('should detect orphaned components', () => {
      const child: TestComponent = {
        id: 'child-1',
        type: 'section',
        status: 'idle',
        parentId: 'non-existent'
      };

      mockComponents.set('child-1', child);

      const result = componentService.validateHierarchy(mockComponents);
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Component child-1 references non-existent parent non-existent');
    });

    test('should detect invalid child references', () => {
      const parent: TestComponent = {
        id: 'parent-1',
        type: 'group',
        status: 'success',
        children: ['missing-child']
      };

      mockComponents.set('parent-1', parent);

      const result = componentService.validateHierarchy(mockComponents);
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Component parent-1 references non-existent child missing-child');
    });
  });

  describe('getAllDescendants', () => {
    test('should return all descendants', () => {
      const root: TestComponent = {
        id: 'root',
        type: 'group',
        status: 'success',
        children: ['child1', 'child2']
      };

      const child1: TestComponent = {
        id: 'child1',
        type: 'section',
        status: 'idle',
        parentId: 'root',
        children: ['grandchild1']
      };

      const child2: TestComponent = {
        id: 'child2',
        type: 'section',
        status: 'idle',
        parentId: 'root'
      };

      const grandchild1: TestComponent = {
        id: 'grandchild1',
        type: 'text',
        status: 'idle',
        parentId: 'child1'
      };

      mockComponents.set('root', root);
      mockComponents.set('child1', child1);
      mockComponents.set('child2', child2);
      mockComponents.set('grandchild1', grandchild1);

      const descendants = componentService.getAllDescendants('root', mockComponents);
      expect(descendants).toHaveLength(3);
      expect(descendants.map(d => d.id)).toContain('child1');
      expect(descendants.map(d => d.id)).toContain('child2');
      expect(descendants.map(d => d.id)).toContain('grandchild1');
    });

    test('should return empty array for components with no children', () => {
      const component: TestComponent = {
        id: 'leaf',
        type: 'text',
        status: 'idle'
      };

      mockComponents.set('leaf', component);

      const descendants = componentService.getAllDescendants('leaf', mockComponents);
      expect(descendants).toEqual([]);
    });
  });

  describe('getAllAncestors', () => {
    test('should return all ancestors', () => {
      const grandparent: TestComponent = {
        id: 'grandparent',
        type: 'group',
        status: 'success'
      };

      const parent: TestComponent = {
        id: 'parent',
        type: 'group',
        status: 'success',
        parentId: 'grandparent'
      };

      const child: TestComponent = {
        id: 'child',
        type: 'section',
        status: 'idle',
        parentId: 'parent'
      };

      mockComponents.set('grandparent', grandparent);
      mockComponents.set('parent', parent);
      mockComponents.set('child', child);

      const ancestors = componentService.getAllAncestors('child', mockComponents);
      expect(ancestors).toHaveLength(2);
      expect(ancestors.map(a => a.id)).toContain('parent');
      expect(ancestors.map(a => a.id)).toContain('grandparent');
    });
  });
});

describe('DependencyService', () => {
  let dependencyService: DependencyService;
  let mockComponents: Map<string, TestComponent>;

  beforeEach(() => {
    mockComponents = new Map();
    dependencyService = new DependencyService();
  });

  afterEach(() => {
    dependencyService.clearAllWaiters();
  });

  describe('areDependenciesReady', () => {
    test('should return true when all dependencies are ready', () => {
      const dep1: TestComponent = {
        id: 'dep1',
        type: 'section',
        status: 'loaded'
      };

      const dep2: TestComponent = {
        id: 'dep2',
        type: 'section',
        status: 'preserved'
      };

      const component: TestComponent = {
        id: 'main',
        type: 'section',
        status: 'idle',
        dependencies: ['dep1', 'dep2']
      };

      mockComponents.set('dep1', dep1);
      mockComponents.set('dep2', dep2);
      mockComponents.set('main', component);

      const ready = dependencyService.areDependenciesReady('main', mockComponents);
      expect(ready).toBe(true);
    });

    test('should return false when some dependencies are not ready', () => {
      const dep1: TestComponent = {
        id: 'dep1',
        type: 'section',
        status: 'loaded'
      };

      const dep2: TestComponent = {
        id: 'dep2',
        type: 'section',
        status: 'loading'
      };

      const component: TestComponent = {
        id: 'main',
        type: 'section',
        status: 'idle',
        dependencies: ['dep1', 'dep2']
      };

      mockComponents.set('dep1', dep1);
      mockComponents.set('dep2', dep2);
      mockComponents.set('main', component);

      const ready = dependencyService.areDependenciesReady('main', mockComponents);
      expect(ready).toBe(false);
    });

    test('should return true for components with no dependencies', () => {
      const component: TestComponent = {
        id: 'main',
        type: 'section',
        status: 'idle'
      };

      mockComponents.set('main', component);

      const ready = dependencyService.areDependenciesReady('main', mockComponents);
      expect(ready).toBe(true);
    });
  });

  describe('getDependents', () => {
    test('should find all components that depend on a given component', () => {
      const targetComponent: TestComponent = {
        id: 'target',
        type: 'section',
        status: 'loaded'
      };

      const dependent1: TestComponent = {
        id: 'dep1',
        type: 'section',
        status: 'idle',
        dependencies: ['target']
      };

      const dependent2: TestComponent = {
        id: 'dep2',
        type: 'section',
        status: 'idle',
        dependencies: ['target', 'other']
      };

      const independent: TestComponent = {
        id: 'independent',
        type: 'section',
        status: 'idle',
        dependencies: ['other']
      };

      mockComponents.set('target', targetComponent);
      mockComponents.set('dep1', dependent1);
      mockComponents.set('dep2', dependent2);
      mockComponents.set('independent', independent);

      const dependents = dependencyService.getDependents('target', mockComponents);
      expect(dependents).toHaveLength(2);
      expect(dependents.map(d => d.id)).toContain('dep1');
      expect(dependents.map(d => d.id)).toContain('dep2');
      expect(dependents.map(d => d.id)).not.toContain('independent');
    });
  });

  describe('getDependencyGraph', () => {
    test('should build dependency graph correctly', () => {
      const comp1: TestComponent = {
        id: 'comp1',
        type: 'section',
        status: 'idle',
        dependencies: ['comp2']
      };

      const comp2: TestComponent = {
        id: 'comp2',
        type: 'section',
        status: 'loaded'
      };

      const comp3: TestComponent = {
        id: 'comp3',
        type: 'section',
        status: 'idle',
        dependencies: ['comp1', 'comp2']
      };

      mockComponents.set('comp1', comp1);
      mockComponents.set('comp2', comp2);
      mockComponents.set('comp3', comp3);

      const { graph, cycles } = dependencyService.getDependencyGraph(mockComponents);
      
      expect(graph).toEqual({
        comp1: ['comp2'],
        comp2: [],
        comp3: ['comp1', 'comp2']
      });
      expect(cycles).toHaveLength(0);
    });

    test('should detect circular dependencies', () => {
      const comp1: TestComponent = {
        id: 'comp1',
        type: 'section',
        status: 'idle',
        dependencies: ['comp2']
      };

      const comp2: TestComponent = {
        id: 'comp2',
        type: 'section',
        status: 'idle',
        dependencies: ['comp1']
      };

      mockComponents.set('comp1', comp1);
      mockComponents.set('comp2', comp2);

      const { cycles } = dependencyService.getDependencyGraph(mockComponents);
      expect(cycles.length).toBeGreaterThan(0);
    });
  });

  describe('getLoadOrder', () => {
    test('should return correct topological order', () => {
      const comp1: TestComponent = {
        id: 'comp1',
        type: 'section',
        status: 'idle',
        dependencies: ['comp2', 'comp3']
      };

      const comp2: TestComponent = {
        id: 'comp2',
        type: 'section',
        status: 'idle',
        dependencies: ['comp3']
      };

      const comp3: TestComponent = {
        id: 'comp3',
        type: 'section',
        status: 'loaded'
      };

      mockComponents.set('comp1', comp1);
      mockComponents.set('comp2', comp2);
      mockComponents.set('comp3', comp3);

      const { order, errors } = dependencyService.getLoadOrder(mockComponents);
      
      expect(errors).toHaveLength(0);
      expect(order).toHaveLength(3);
      expect(order.indexOf('comp3')).toBeLessThan(order.indexOf('comp2'));
      expect(order.indexOf('comp2')).toBeLessThan(order.indexOf('comp1'));
    });

    test('should detect circular dependencies in load order', () => {
      const comp1: TestComponent = {
        id: 'comp1',
        type: 'section',
        status: 'idle',
        dependencies: ['comp2']
      };

      const comp2: TestComponent = {
        id: 'comp2',
        type: 'section',
        status: 'idle',
        dependencies: ['comp1']
      };

      mockComponents.set('comp1', comp1);
      mockComponents.set('comp2', comp2);

      const { errors } = dependencyService.getLoadOrder(mockComponents);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0]).toContain('Circular dependencies detected');
    });
  });

  describe('validateDependencies', () => {
    test('should validate all dependencies exist', () => {
      const comp1: TestComponent = {
        id: 'comp1',
        type: 'section',
        status: 'idle',
        dependencies: ['comp2']
      };

      const comp2: TestComponent = {
        id: 'comp2',
        type: 'section',
        status: 'loaded'
      };

      mockComponents.set('comp1', comp1);
      mockComponents.set('comp2', comp2);

      const result = dependencyService.validateDependencies(mockComponents);
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('should detect missing dependencies', () => {
      const comp1: TestComponent = {
        id: 'comp1',
        type: 'section',
        status: 'idle',
        dependencies: ['missing-component']
      };

      mockComponents.set('comp1', comp1);

      const result = dependencyService.validateDependencies(mockComponents);
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Component comp1 depends on non-existent component missing-component');
    });
  });

  describe('getDependencyChain', () => {
    test('should return all transitive dependencies', () => {
      const comp1: TestComponent = {
        id: 'comp1',
        type: 'section',
        status: 'idle',
        dependencies: ['comp2']
      };

      const comp2: TestComponent = {
        id: 'comp2',
        type: 'section',
        status: 'idle',
        dependencies: ['comp3']
      };

      const comp3: TestComponent = {
        id: 'comp3',
        type: 'section',
        status: 'loaded'
      };

      mockComponents.set('comp1', comp1);
      mockComponents.set('comp2', comp2);
      mockComponents.set('comp3', comp3);

      const chain = dependencyService.getDependencyChain('comp1', mockComponents);
      expect(chain).toContain('comp2');
      expect(chain).toContain('comp3');
    });
  });

  describe('waitForDependencies', () => {
    test('should resolve immediately if dependencies are ready', async () => {
      const dep: TestComponent = {
        id: 'dep',
        type: 'section',
        status: 'loaded'
      };

      const component: TestComponent = {
        id: 'main',
        type: 'section',
        status: 'idle',
        dependencies: ['dep']
      };

      mockComponents.set('dep', dep);
      mockComponents.set('main', component);

      const startTime = Date.now();
      await dependencyService.waitForDependencies('main', mockComponents);
      const endTime = Date.now();

      expect(endTime - startTime).toBeLessThan(100); // Should resolve quickly
    });
  });

  describe('getWaiterStats', () => {
    test('should return correct waiter statistics', () => {
      const stats = dependencyService.getWaiterStats();
      expect(stats.totalWaiters).toBe(0);
      expect(stats.componentWaiters).toEqual({});
    });
  });
});

describe('VersioningService', () => {
  let versioningService: VersioningService;

  beforeEach(() => {
    versioningService = new VersioningService();
  });

  test('should create instance successfully', () => {
    expect(versioningService).toBeInstanceOf(VersioningService);
  });

  // Additional tests would require mocking the actual methods that exist in VersioningService
  // The service appears to focus on database operations for version management
});