import { RunType, RunTypeV2 } from '@/types'

/**
 * Converts a RunTypeV2 (from xfer_runs) to the old RunType format (from xfer_runs)
 * for backward compatibility with existing code.
 *
 * @param runV2 The run from xfer_runs table
 * @returns A run in the old format for compatibility
 */
export function convertRunV2ToRunV1(runV2: RunTypeV2|any): RunType {
  const model = runV2.model || {};

  // First check the table's completed_at column, then fall back to the model's completed_at
  let completedAt = runV2.completed_at || model.completed_at || null;

  if (completedAt) {
    // Check if the date is valid (not 1970-01-01 or invalid format)
    const dateObj = new Date(completedAt);
    if (isNaN(dateObj.getTime()) || dateObj.getFullYear() <= 1970) {
      completedAt = null;
    }
  }

  return {
    id: runV2.id,
    run_type: runV2.run_type,
    scope: runV2.scope,
    target: runV2.target || null,
    model: runV2.model,
    start_year: model.start_year,
    end_year: model.end_year || null,
    models: model.models || [],
    completed_at: completedAt
  } as RunType;
}

/**
 * Gets the models used in a RunTypeV2
 *
 * @param runV2 The run from xfer_runs table
 * @returns Array of model names
 */
export function getRunModels(runV2: RunTypeV2): string[] {
  return runV2.model.models || [];
}

/**
 * Checks if a run is completed
 *
 * @param runV2 The run from xfer_runs table
 * @returns True if the run is completed
 */
export function isRunCompleted(runV2: RunTypeV2): boolean {
  return runV2.model.status === 'completed';
}
