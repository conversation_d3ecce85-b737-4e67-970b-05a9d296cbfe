import { FlagTypeV2 } from '@/types'

/**
 * Helper functions for working with FlagTypeV2 objects
 * These functions provide easy access to commonly used properties
 * that are nested within the model field
 */

/**
 * Ensures the model field is properly parsed if it's a string
 *
 * @param flag The FlagTypeV2 object
 * @returns The FlagTypeV2 with properly parsed model
 */
export function ensureModelParsed(flag: FlagTypeV2): FlagTypeV2 {
  if (typeof flag.model === 'string') {
    return {
      ...flag,
      model: JSON.parse(flag.model)
    };
  }
  return flag;
}

/**
 * Gets the flag type (red/green) from a FlagTypeV2 object
 *
 * @param flag The FlagTypeV2 object
 * @returns The flag type (red/green)
 */
export function getFlagType(flag: FlagTypeV2): string {
  const parsedFlag = ensureModelParsed(flag);
  if (!parsedFlag.model) {
    throw new Error(`Flag model is undefined for flag ID: ${flag.id}`)
  }
  return parsedFlag.model.flag_type;
}

/**
 * Gets the flag title from a FlagTypeV2 object
 *
 * @param flag The FlagTypeV2 object
 * @returns The flag title
 */
export function getFlagTitle(flag: FlagTypeV2): string {
  const parsedFlag = ensureModelParsed(flag);
  if (!parsedFlag.model) {
    throw new Error(`Flag model is undefined for flag ID: ${flag.id}`)
  }
  return parsedFlag.model.flag_title;
}

/**
 * Gets the flag short title from a FlagTypeV2 object
 *
 * @param flag The FlagTypeV2 object
 * @returns The flag short title or null if not available
 */
export function getFlagShortTitle(flag: FlagTypeV2): string | null {
  const parsedFlag = ensureModelParsed(flag);
  if (!parsedFlag.model) {
    return null
  }
  // Use flag_title as fallback since flag_short_title might not exist in older models
  return (parsedFlag.model as any).flag_short_title || parsedFlag.model.flag_title || null;
}

/**
 * Gets the flag summary from a FlagTypeV2 object
 * Uses dedicated column for better performance
 *
 * @param flag The FlagTypeV2 object
 * @returns The flag summary or null if not available
 */
export function getFlagSummary(flag: FlagTypeV2): string | null {
  return (flag as any).flag_summary || null
}

/**
 * Gets the flag analysis from a FlagTypeV2 object
 * Uses dedicated column for better performance
 *
 * @param flag The FlagTypeV2 object
 * @returns The flag analysis or null if not available
 */
export function getFlagAnalysis(flag: FlagTypeV2): string | null {
  return (flag as any).flag_analysis || null
}

/**
 * Gets the flag statements from a FlagTypeV2 object
 * Uses dedicated JSONB column for better performance
 *
 * @param flag The FlagTypeV2 object
 * @returns The flag statements array or empty array if not available
 */
export function getFlagStatements(flag: FlagTypeV2): any[] {
  return (flag as any).flag_statements || []
}

/**
 * Gets the flag text for display in the dashboard
 * Uses dedicated column first, then fallback to title from JSON
 *
 * @param flag The FlagTypeV2 object
 * @returns The flag text (summary or title)
 */
export function getFlagText(flag: FlagTypeV2): string {
  // Use column data for summary
  const summary = (flag as any).flag_summary
  if (summary) return summary

  // Fallback to title from JSON model
  const parsedFlag = ensureModelParsed(flag);
  if (!parsedFlag.model) {
    return ''
  }
  return parsedFlag.model.flag_title || ''
}

/**
 * Gets the reason text (same as summary for backward compatibility)
 * Uses dedicated column for better performance
 *
 * @param flag The FlagTypeV2 object
 * @returns The reason text (summary)
 */
export function getReason(flag: FlagTypeV2): string {
  return (flag as any).flag_summary || ''
}

/**
 * Gets the citations from a FlagTypeV2 object
 *
 * @param flag The FlagTypeV2 object
 * @returns The citations array
 */
export function getCitations(flag: FlagTypeV2): any[] {
  const parsedFlag = ensureModelParsed(flag);
  if (!parsedFlag.model) {
    return []
  }
  return parsedFlag.model.citations || [];
}

/**
 * Gets the model sections from a FlagTypeV2 object
 *
 * @param flag The FlagTypeV2 object
 * @returns The model sections map
 */
export function getModelSections(flag: FlagTypeV2): Record<string, string> {
  const parsedFlag = ensureModelParsed(flag);
  if (!parsedFlag.model) {
    return {}
  }
  return parsedFlag.model.model_sections || {};
}

/**
 * Gets the is_disclosure_only flag from a FlagTypeV2 object
 *
 * @param flag The FlagTypeV2 object
 * @returns Whether all source documents are disclosures
 */
export function isDisclosureOnly(flag: FlagTypeV2): boolean {
  const parsedFlag = ensureModelParsed(flag);
  if (!parsedFlag.model) {
    return false
  }
  return parsedFlag.model.is_disclosure_only || false;
}

/**
 * Processes an array of FlagTypeV2 objects to ensure all models are properly parsed
 *
 * @param flags Array of FlagTypeV2 objects
 * @returns Array of FlagTypeV2 objects with properly parsed models
 */
export function processFlags(flags: FlagTypeV2[]): FlagTypeV2[] {
  return flags.map(flag => ensureModelParsed(flag));
}
