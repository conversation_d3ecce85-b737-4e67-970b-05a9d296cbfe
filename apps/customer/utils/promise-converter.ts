/**
 * Converts a PromiseTypeV2 (from xfer_promises) to the old PromiseType format (from xfer_gw_promises)
 * for backward compatibility with existing code.
 *
 * @param promiseV2 The promise from xfer_promises table
 * @returns A promise in the old format for compatibility
 */
export function convertPromiseV2ToPromiseV1(promiseV2: any): any {
  // Handle the case where the input might not match the PromiseTypeV2 interface exactly
  const rawPromiseV2 = promiseV2 as any;
  const model = rawPromiseV2.model;

  if (!model) {
    console.error('Promise model is missing:', rawPromiseV2);
    // Return a minimal valid PromiseType to prevent errors
    return {
      id: rawPromiseV2.id || 0,
      run_id: rawPromiseV2.run_id || 0,
      entity_xid: rawPromiseV2.entity_xid || '',
      statement_id: 0,
      promise_kept: rawPromiseV2.kept ?? false, // Use the kept field if available
      greenwashing: false,
      verdict: 'Missing data',
      summary: 'Missing data',
      conclusion: 'Missing data',
      confidence: 0,
      text: 'Missing data',
      context: null,
      promise_doc: 'Unknown',
      promise_doc_year: new Date().getFullYear(),
      evidence: [],
      citations: [],
      llm_greenwashing: false,
      company: 'Unknown',
      company_id: 0,
      esg_promise: false,
      created_at: null,
      company_xid: '',
      virtual_entity_id: null,
      virtual_entity_short_id: ''
    };
  }

  return {
    id: rawPromiseV2.id,
    run_id: rawPromiseV2.run_id,
    entity_xid: model.entity_xid || rawPromiseV2.entity_xid,
    statement_id: model.statement_id || 0,
    promise_kept: rawPromiseV2.kept ?? model.promise_kept ?? false, // Use the kept field if available
    greenwashing: model.greenwashing || false,
    verdict: model.verdict || 'Unknown',
    summary: model.summary || 'Missing data',
    conclusion: model.conclusion || 'Missing data',
    confidence: model.confidence || 0,
    text: model.text || 'Missing data',
    context: model.context || null,
    promise_doc: model.promise_doc || 'Unknown',
    promise_doc_year: model.promise_doc_year || new Date().getFullYear(),
    evidence: model.evidence || [],
    citations: model.citations || [],
    llm_greenwashing: model.llm_greenwashing || false,
    company: model.company || 'Unknown',
    company_id: model.company_id || 0,
    esg_promise: model.esg_promise || false,
    created_at: model.created_at || null,
    company_xid: model.entity_xid || rawPromiseV2.entity_xid,
    virtual_entity_id: null,
    virtual_entity_short_id: model.entity_xid || rawPromiseV2.entity_xid
  };
}

/**
 * Converts an array of PromiseTypeV2 objects to an array of PromiseType objects
 * for backward compatibility with existing code.
 *
 * @param promisesV2 Array of promises from xfer_promises table
 * @returns Array of promises in the old format for compatibility
 */
export function convertPromisesV2ToPromisesV1(promisesV2: any[]): any[] {
  return promisesV2.map(promiseV2 => convertPromiseV2ToPromiseV1(promiseV2));
}
