import { VagueType } from '@/types'

/**
 * Converts a VagueTypeV2 (from _deprecated_xfer_gw_vague_v2) to the old VagueType format (from xfer_gw_vague)
 * for backward compatibility with existing code.
 *
 * @param vagueV2 The vague term analysis from _deprecated_xfer_gw_vague_v2 table
 * @returns A vague term analysis in the old format for compatibility
 */
export function convertVagueV2ToVagueV1(vagueV2: any): VagueType {
  throw new Error("This function is deprecated");
}

/**
 * Gets the citations from a VagueTypeV2
 *
 * @param vagueV2 The vague term analysis from _deprecated_xfer_gw_vague_v2 table
 * @returns Array of citations
 */
export function getVagueCitations(vagueV2: any) {
  const rawVagueV2 = vagueV2 as any;
  const model = rawVagueV2.model;

  if (!model) {
    console.error('Vague model is missing in getVagueCitations:', rawVagueV2);
    return [];
  }

  return model.citations || [];
}
