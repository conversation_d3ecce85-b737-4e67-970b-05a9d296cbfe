import { NextRequest, NextResponse } from 'next/server'
import jwt from 'jsonwebtoken'
import { createClient } from '@/app/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()

    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get the request body
    const body = await request.json()
    const { documentId, permissions = 'write' } = body

    if (!documentId) {
      return NextResponse.json(
        { error: 'Document ID is required' },
        { status: 400 }
      )
    }

    // Get TipTap secret from environment
    const tiptapSecret = process.env.TIPTAP_SECRET
    if (!tiptapSecret) {
      console.error('TIPTAP_SECRET environment variable is not set')
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      )
    }

    // Check if user has access to the document in Supabase
    const { data: document, error: docError } = await supabase
      .from('doc_documents')
      .select('id, created_by')
      .eq('id', documentId)
      .single()

    // Determine user permissions
    let allowedDocumentNames: string[] = []
    let readonlyDocumentNames: string[] = []

    if (document) {
      // Document exists, check permissions
      const isOwner = document.created_by === user.id

      if (isOwner || permissions === 'write') {
        allowedDocumentNames = [documentId]
      } else if (permissions === 'read') {
        readonlyDocumentNames = [documentId]
      }
    } else {
      // Document doesn't exist, allow creation if user is authenticated
      allowedDocumentNames = [documentId]
    }

    // Create JWT payload according to TipTap documentation
    const payload = {
      sub: user.id, // User identifier
      allowedDocumentNames,
      readonlyDocumentNames,
      // Optional: Add user metadata for collaboration features
      user: {
        id: user.id,
        name: user.user_metadata?.full_name || user.email?.split('@')[0] || 'Anonymous',
        email: user.email || '',
        avatar: user.user_metadata?.avatar_url,
      },
      // Optional: Add expiration time (1 hour)
      exp: Math.floor(Date.now() / 1000) + (60 * 60),
    }

    // Generate JWT token
    const token = jwt.sign(payload, tiptapSecret)

    return NextResponse.json({
      token,
      user: payload.user,
      permissions: {
        allowedDocumentNames,
        readonlyDocumentNames,
      }
    })

  } catch (error) {
    console.error('Error generating TipTap JWT:', error)

    // Provide more specific error information
    const errorMessage = error instanceof Error ? error.message : 'Internal server error'

    return NextResponse.json(
      {
        error: 'Failed to generate authentication token',
        details: errorMessage,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}

// GET method for testing/debugging (optional)
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const documentId = searchParams.get('documentId')

  if (!documentId) {
    return NextResponse.json(
      { error: 'Document ID is required' },
      { status: 400 }
    )
  }

  // Call the POST method with default permissions
  const mockRequest = new NextRequest(request.url, {
    method: 'POST',
    body: JSON.stringify({ documentId, permissions: 'write' }),
    headers: {
      'Content-Type': 'application/json',
      ...Object.fromEntries(request.headers.entries())
    }
  })

  return POST(mockRequest)
}
