'use client'

import React, { useEffect, useState } from 'react'
import { useParams } from 'next/navigation'
import { createClient } from '@/app/supabase/client'
import { EkoDocumentEditor } from '@/components/editor/EkoDocumentEditor'
import { DocumentProvider } from '@/components/editor/context/DocumentContext'
import { Skeleton } from '@ui/components/ui/skeleton'

interface Document {
  id: string
  title?: string | null
  content?: string | null
  initial_content?: string | null
  data?: any
  metadata?: any
  created_at?: string | null
  updated_at?: string | null
}

export default function DocumentPrintPage() {
  const params = useParams()
  const [document, setDocument] = useState<Document | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const documentId = params.id as string
  const supabase = createClient()

  // Load document data
  useEffect(() => {
    const loadDocument = async () => {
      if (!documentId) return

      try {
        setLoading(true)
        const { data, error } = await supabase
          .from('doc_documents')
          .select('id, title, content, initial_content, data, metadata, created_at, updated_at')
          .eq('id', documentId)
          .single()

        if (error) {
          throw error
        }

        setDocument(data)
      } catch (err) {
        console.error('Error loading document:', err)
        setError(err instanceof Error ? err.message : 'Failed to load document')
      } finally {
        setLoading(false)
      }
    }

    loadDocument()
  }, [documentId, supabase])

  // Note: Auto-print removed - users can now view the document before choosing to print

  // Handle print dialog events
  useEffect(() => {
    const handleAfterPrint = () => {
      // Close the window after printing
      window.close()
    }

    const handleBeforePrint = () => {
      // Optional: Any pre-print setup
      console.log('Preparing to print document:', documentId)
    }

    // Add event listeners
    window.addEventListener('afterprint', handleAfterPrint)
    window.addEventListener('beforeprint', handleBeforePrint)

    // Cleanup
    return () => {
      window.removeEventListener('afterprint', handleAfterPrint)
      window.removeEventListener('beforeprint', handleBeforePrint)
    }
  }, [documentId])

  if (loading) {
    return (
      <div className="min-h-screen bg-white p-8">
        <div className="max-w-4xl mx-auto space-y-4">
          <Skeleton className="h-8 w-3/4" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-5/6" />
          <Skeleton className="h-4 w-4/5" />
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-white p-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-2xl font-bold text-red-600 mb-2">Error</h1>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => window.close()}
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
          >
            Close Window
          </button>
        </div>
      </div>
    )
  }

  if (!document) {
    return (
      <div className="min-h-screen bg-white p-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-2xl font-bold mb-2">Document Not Found</h1>
          <p className="text-gray-600 mb-4">The requested document could not be found.</p>
          <button
            onClick={() => window.close()}
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
          >
            Close Window
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-white print:bg-white print:text-black print:m-0 print:p-0">
      {/* Print-specific styles using Tailwind */}
      <style jsx global>{`
        @media print {
          * {
            background: transparent !important;
            background-color: transparent !important;
            background-image: none !important;
            box-shadow: none !important;
            text-shadow: none !important;
          }

          body, html {
            margin: 0;
            padding: 0;
            font-family: Helvetica, -apple-system, BlinkMacSystemFont, Roboto, Arial, sans-serif !important;
            color: black !important;
            background: white !important;
            background-color: white !important;
            -webkit-print-color-adjust: exact;
          }

            /* Hide report component decorations with high specificity */
            .report-summary, .report-group, .report-section {
                border: none !important;
                background: transparent !important;
                box-shadow: none !important;
                backdrop-filter: none !important;
                padding: 0 !important;
                margin: 0 !important;
                border-radius: 0 !important;
            }

            /* Hide all child elements with borders and backgrounds */
            .report-summary *, .report-group *, .report-section * {
                border-color: transparent !important;
                background: transparent !important;
                backdrop-filter: none !important;
            }

            /* Hide control headers and buttons */
            .report-summary .absolute, .report-group .absolute, .report-section .absolute,
            .report-summary button, .report-group button, .report-section button,
            .report-summary .controls, .report-group .controls, .report-section .controls {
                display: none !important;
            }

            /* Hide "Summarizes:" text specifically */
            .report-summary .text-purple-500,
            .report-summary div[class*="text-purple-500"],
            .report-summary .text-xs.text-purple-500 {
                display: none !important;
            }

            /* Ensure print:hidden utility works with high specificity */
            .print\\:hidden {
                display: none !important;
            }

            /* Hide specific colored elements */
            .bg-purple-50, .bg-purple-100, .bg-blue-50, .bg-blue-100, .bg-green-50, .bg-green-100,
            .border-purple-200, .border-blue-200, .border-green-200 {
                background: transparent !important;
                border-color: transparent !important;
            }

            /* Ensure all containers have transparent backgrounds */
            div, section, article, main, header, footer, nav, aside {
                background: transparent !important;
                background-color: transparent !important;
            }

            /* Override any glass effects or gradients */
            .glass-effect-brand-lit, .glass-effect-brand-strong-lit, .glass-effect-subtle-lit,
            .glass-effect-brand-compliment-lit, .glass-effect-brand-alt-strong-lit,
            .bg-gradient-to-br, .bg-gradient-to-r, .bg-gradient-to-l, .bg-gradient-to-t, .bg-gradient-to-b {
                background: transparent !important;
                background-image: none !important;
                backdrop-filter: none !important;
            }

          /* Ensure proper page breaks */
          h1, h2, h3, h4, h5, h6 {
            page-break-after: avoid;
              page-break-inside: avoid;
            color: black !important;
          }

          p, div {
            page-break-inside: avoid;
            color: black !important;
          }

          /* Style links for print */
          a {
            color: black !important;
            text-decoration: none !important;
          }

          /* Ensure images fit properly */
          img {
            max-width: 100% !important;
            height: auto !important;
          }

          /* Table styling */
          table {
            border-collapse: collapse !important;
            width: 100% !important;
          }

          th, td {
            border: 1px solid #000 !important;
            padding: 8px !important;
            color: black !important;
          }

            /* Grid layout adjustments */
            .grid {
                display: block !important;
            }

            .grid > div {
                width: 100% !important;
                margin-bottom: 1.5rem !important;
            }

            /* Report sections */
            .report-section {
                font-size: 16pt !important;
                page-break-before: always;
                page-break-after: auto;
            }

            .report-first-page {
                page-break-before: avoid;
                page-break-after: always;
            }

            .report-subsection {
                page-break-inside: avoid;
                margin-bottom: 2rem;
            }

            /* Prose styling */
            .prose, .prose-slate {
                max-width: 100% !important;
                width: 100% !important;
                color: inherit;
            }

            .prose h1, .prose h2, .prose h3, .prose h4,
            .prose p, .prose ul, .prose ol, .prose li,
            .prose blockquote, .prose figure, .prose table {
                width: 100%;
                max-width: none;
                color: inherit;
            }
        }

        @page {
          size: A4 portrait;
          margin: 25mm 20mm 20mm 20mm;
        }
      `}</style>

      <div className="w-full h-full">
        {/* Document header - only show in non-print view */}
        <div className="print:hidden p-8 border-b">
          <h1 className="text-3xl font-bold mb-2 text-black print:text-black">
            {document.title || 'Untitled Document'}
          </h1>
          {document.updated_at && (
            <p className="text-sm text-gray-600 mb-4 print:text-black">
              Last updated: {new Date(document.updated_at).toLocaleDateString()}
            </p>
          )}

          {/* Print button for non-print view */}
          <div className="mt-4 print:hidden">
            <button
              onClick={() => window.print()}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 mr-2 print:hidden"
              data-testid="print-document-button"
            >
              Print Document
            </button>
            <button
              onClick={() => window.close()}
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 print:hidden"
            >
              Close Window
            </button>
          </div>
        </div>

        {/* Document content using EkoDocumentEditor in view mode */}
        <div className="w-full h-full print:w-full print:h-auto print:max-w-none">
          <DocumentProvider
            documentId={document.id}
            initialEntity={null}
            initialRun="latest"
            onSave={() => {}} // No-op for print mode
          >
            <EkoDocumentEditor
              documentId={document.id}
              citations={document.metadata?.citations || []}
              initialContent={document.initial_content || document.content || ''}
              initialData={document.data}
              viewMode={true}
              editable={false}
              showToolbar={false}
              showCollaboration={false}
              className="w-full h-full print:p-0 print:m-0 print:w-full print:max-w-none print:text-black"
            />
          </DocumentProvider>
        </div>
      </div>
    </div>
  )
}
