import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { PublicDocumentViewer } from '@/components/editor/PublicDocumentViewer'
import { createClient } from '@/app/supabase/server'

interface PublicDocumentPageProps {
  params: Promise<{
    id: string
  }>
}

// This function fetches the document data using Supabase client
async function getPublicDocument(id: string) {
  try {
    console.log(`[PUBLIC DOCUMENT] Fetching document with ID: ${id}`)
    const supabase = await createClient()

    // First, check if the document exists at all (for debugging)
    const { data: documentExists, error: existsError } = await supabase
      .from('doc_documents')
      .select('id, is_public, metadata')
      .eq('id', id)
      .single()

    if (existsError) {
      console.error(`[PUBLIC DOCUMENT] Document ${id} not found:`, existsError)
      return null
    }

    console.log(`[PUBLIC DOCUMENT] Document ${id} exists. is_public: ${documentExists.is_public}, metadata:`, documentExists.metadata)

    // Check both is_public column and metadata.isPublic for backwards compatibility
    const isDocumentPublic = documentExists.is_public === true || 
                            (documentExists.metadata as any)?.isPublic === true

    if (!isDocumentPublic) {
      console.error(`[PUBLIC DOCUMENT] Document ${id} is not public. is_public: ${documentExists.is_public}, metadata.isPublic: ${(documentExists.metadata as any)?.isPublic}`)
      return null
    }

    // Query the document with public access check
    const { data: document, error } = await supabase
      .from('doc_documents')
      .select('id, title, content, initial_content, data, metadata, created_at, updated_at, is_public')
      .eq('id', id)
      .eq('is_public', true) // Only fetch if document is public
      .single()

    if (error) {
      console.error(`[PUBLIC DOCUMENT] Error fetching public document ${id}:`, error)
      return null
    }

    console.log(`[PUBLIC DOCUMENT] Successfully fetched public document ${id}`)
    return document
  } catch (error) {
    console.error(`[PUBLIC DOCUMENT] Unexpected error fetching document ${id}:`, error)
    return null
  }
}

// Generate metadata for the page
export async function generateMetadata({ params }: PublicDocumentPageProps): Promise<Metadata> {
  const { id } = await params
  const document = await getPublicDocument(id)

  if (!document) {
    return {
      title: 'Document Not Found',
      description: 'The requested document could not be found or is not publicly accessible.',
    }
  }

  return {
    title: document.title || 'Shared Document',
    description: 'A publicly shared document',
    robots: {
      index: false, // Don't index shared documents
      follow: false,
    },
  }
}

export default async function PublicDocumentPage({ params }: PublicDocumentPageProps) {
  const { id } = await params
  console.log(`[PUBLIC DOCUMENT PAGE] Rendering page for document ID: ${id}`)
  
  const document = await getPublicDocument(id)

  if (!document) {
    console.log(`[PUBLIC DOCUMENT PAGE] Document not found, returning 404`)
    notFound()
  }

  // Extract citations from metadata if available
  const citations = (document.metadata as any)?.citations || []
  
  console.log(`[PUBLIC DOCUMENT PAGE] Rendering PublicDocumentViewer for document: ${document.title}`)

  return (
    <PublicDocumentViewer
      documentId={id}
      title={document.title || undefined}
      content={(document.initial_content || document.content) || undefined}
      data={document.data || undefined}
      citations={citations}
    />
  )
}
