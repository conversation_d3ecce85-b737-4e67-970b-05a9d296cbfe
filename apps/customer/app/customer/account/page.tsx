"use client";
import {useEffect, useRef, useState} from 'react';
import {Camera, Loader2, RotateCw, ZoomIn, ZoomOut} from 'lucide-react';
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from '@/components/ui/card';
import {Input} from '@ui/components/ui/input';
import {Button} from '@/components/ui/button';
import {Label} from '@/components/ui/label';
import {Avatar, AvatarFallback, AvatarImage} from '@/components/ui/avatar';
import {Dialog, DialogContent, DialogHeader, DialogTitle} from '@ui/components/ui/dialog';
import {Slider} from '@ui/components/ui/slider';
import {useToast} from '@ui/hooks/use-toast';
import AvatarEditor from "react-avatar-editor";

import {createClient} from "@/app/supabase/client";
import {useRouter} from "next/navigation";
import {useIsMobile} from '@ui/hooks/use-mobile';
import {ContactForm} from "@/components/contact-form";

const ProfilePage = () => {
    const supabase =createClient();
    const { toast } = useToast();
    const router = useRouter();

    const [loading, setLoading] = useState(false);
    const [profile, setProfile] = useState<any>({
        id: '',
        full_name: '',
        username: '',
        website: '',
        avatar_url: null,
        organisation: ''
    });

    // Avatar editor state
    const [avatarDialogOpen, setAvatarDialogOpen] = useState(false);
    const [avatarFile, setAvatarFile] = useState(null);
    const [zoom, setZoom] = useState(1.2);
    const [rotation, setRotation] = useState(0);
    const editorRef = useRef(null);

    const isMobile = useIsMobile();
    // Fetch profile data on mount
    useEffect(() => {
        getProfile();
    }, []);

    const getProfile = async () => {
        try {
            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) throw new Error('No user found');

            let { data, error } = await supabase
                .from('profiles')
                .select('*')
                .eq('id', user.id)
                .single();

            if(data === null && user.id !== null) {
                await supabase.from('profiles').insert({
                    id: user.id,
                    avatar_url: null,
                    full_name: user.email,
                    username: user.email,
                    website: null
                });
                data= (await supabase
                    .from('profiles')
                    .select('*')
                    .eq('id', user.id)
                    .single()).data;
            }

            if (error) throw error;

            if(!data) throw new Error('No profile found');



            let profileData = {
                id: user.id,
                full_name: data.full_name || '',
                username: data.username || '',
                website: data.website || '',
                avatar_url: data.avatar_url,
                organisation: ""
            };

            const orgData= (await supabase
                .from('acc_organisations')
                .select('*')
                .eq('id',data.organisation!)
                .single()).data;

            profileData.organisation= orgData?.name || "";

            if(data.avatar_url) {
                // Get public URL
                profileData.avatar_url= (await supabase.storage
                    .from('avatars')
                    .createSignedUrl(data.avatar_url, 3600)).data?.signedUrl!;
            }

            setProfile(profileData);
        } catch (error:any) {
            toast({
                variant: "destructive",
                title: "Error loading profile",
                description: error.message
            });
        } finally {
            setLoading(false);
        }
    };

    const handleSubmit = async (e:any) => {
        e.preventDefault();
        try {
            setLoading(true);

            const { error } = await supabase
                .from('profiles')
                .upsert({
                    id: profile.id,
                    full_name: profile.full_name,
                    username: profile.username,
                    website: profile.website,
                    updated_at: new Date().toISOString(),
                });

            if (error) throw error;

            toast({
                title: "Profile updated",
                description: "Your profile has been successfully updated."
            });
        } catch (error: any) {
            toast({
                variant: "destructive",
                title: "Error updating profile",
                description: error.message
            });
        } finally {
            setLoading(false);
        }
    };

    const handleAvatarChange = (e:any) => {
        const file = e.target.files?.[0];
        if (!file) return;
        setAvatarFile(file);
        setAvatarDialogOpen(true);
    };

    const handleSaveAvatar = async () => {
        if (!editorRef.current) return;

        try {
            setLoading(true);

            // Convert canvas to blob
            const canvas = (editorRef.current as any).getImageScaledToCanvas();
            const blob:Blob = await new Promise((resolve) =>
                canvas.toBlob((b:Blob) => resolve(b), 'image/jpeg', 0.95)
            );

            // Create file name
            const fileName = `avatar-${profile.id}-${Date.now()}.jpg`;

            // Upload to Supabase Storage
            const { error: uploadError } = await supabase.storage
                .from('avatars')
                .upload(fileName, blob, {
                    contentType: 'image/jpeg',
                    upsert: true
                })

            if (uploadError) throw uploadError;


            // Update profile with new avatar URL
            const { error: updateError } = await supabase
                .from('profiles')
                .update({
                    avatar_url: fileName,
                    updated_at: new Date().toISOString()
                })
                .eq('id', profile.id);

            if (updateError) throw updateError;

            profile.avatar_url= (await supabase.storage
                .from('avatars')
                .createSignedUrl(fileName, 3600)).data?.signedUrl!;
            setProfile({ ...profile});
            setAvatarDialogOpen(false);

        } catch (error:any) {
            toast({
                variant: "destructive",
                title: "Error updating avatar",
                description: error.message
            });
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="container max-w-2xl p-4" data-testid="account-page">
            <Card>
                <CardHeader>
                    <CardTitle>Account Settings</CardTitle>
                    <CardDescription>
                        Update your profile information and manage your account
                    </CardDescription>
                </CardHeader>
                <CardContent className="mx-4">
                    <form onSubmit={handleSubmit} className="space-y-8" data-testid="profile-form">
                        {/* Avatar Section */}
                        <div className="flex flex-col items-center space-y-4" data-testid="avatar-section">
                            <Avatar className="h-24 w-24" data-testid="current-avatar">
                                <AvatarImage src={profile.avatar_url || ''} />
                                <AvatarFallback className="text-lg">
                                    {profile.full_name?.charAt(0)?.toUpperCase() || 'U'}
                                </AvatarFallback>
                            </Avatar>

                            <div className="flex items-center space-x-2">
                                <Label
                                    htmlFor="avatar-upload"
                                    className="cursor-pointer inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
                                    data-testid="upload-avatar-button"
                                >
                                    <Camera className="mr-2 h-4 w-4" />
                                    Change Avatar
                                </Label>
                                <Input
                                    id="avatar-upload"
                                    type="file"
                                    accept="image/*"
                                    className="hidden"
                                    onChange={handleAvatarChange}
                                    disabled={loading}
                                    data-testid="avatar-file-input"
                                />
                            </div>
                        </div>

                        {/* Avatar Editor Dialog */}
                        <Dialog open={avatarDialogOpen} onOpenChange={setAvatarDialogOpen}>
                            <DialogContent className="sm:max-w-md">
                                <DialogHeader>
                                    <DialogTitle>Edit Avatar</DialogTitle>
                                </DialogHeader>
                                <div className="flex flex-col items-center space-y-6">
                                    {avatarFile && (
                                        <div className="relative">
                                            <AvatarEditor
                                                ref={editorRef}
                                                image={avatarFile}
                                                width={250}
                                                height={250}
                                                border={25}
                                                borderRadius={125}
                                                color={[0, 0, 0, 0.6]}
                                                scale={zoom}
                                                rotate={rotation}
                                            />
                                        </div>
                                    )}

                                    {/* Controls */}
                                    <div className="w-full space-y-4">
                                        {/* Zoom Control */}
                                        <div className="space-y-2">
                                            <div className="flex items-center justify-between">
                                                <Label>Zoom</Label>
                                                <div className="flex items-center space-x-2">
                                                    <Button
                                                        variant="outline"
                                                        size="icon"
                                                        onClick={() => setZoom(Math.max(1, zoom - 0.1))}
                                                    >
                                                        <ZoomOut className="h-4 w-4" />
                                                    </Button>
                                                    <Button
                                                        variant="outline"
                                                        size="icon"
                                                        onClick={() => setZoom(Math.min(3, zoom + 0.1))}
                                                    >
                                                        <ZoomIn className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            </div>
                                            <Slider
                                                value={[zoom]}
                                                min={1}
                                                max={3}
                                                step={0.1}
                                                onValueChange={([value]) => setZoom(value)}
                                            />
                                        </div>

                                        {/* Rotation Control */}
                                        <div className="space-y-2">
                                            <div className="flex items-center justify-between">
                                                <Label>Rotation</Label>
                                                <Button
                                                    variant="outline"
                                                    size="icon"
                                                    onClick={() => setRotation((rotation + 90) % 360)}
                                                >
                                                    <RotateCw className="h-4 w-4" />
                                                </Button>
                                            </div>
                                        </div>

                                        <Button
                                            className="w-full"
                                            onClick={handleSaveAvatar}
                                            disabled={loading}
                                        >
                                            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                                            Save Avatar
                                        </Button>
                                    </div>
                                </div>
                            </DialogContent>
                        </Dialog>

                        {/* Profile Details */}
                        <div className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="full-name">Full Name</Label>
                                <Input
                                    id="full-name"
                                    value={profile.full_name}
                                    onChange={e => setProfile({...profile, full_name: e.target.value})}
                                    disabled={loading}
                                    data-testid="full-name-input"
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="username">Username</Label>
                                <Input
                                    id="username"
                                    value={profile.username}
                                    onChange={e => setProfile({...profile, username: e.target.value})}
                                    disabled={loading}
                                    data-testid="username-input"
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="website">Website</Label>
                                <Input
                                    id="website"
                                    type="url"
                                    value={profile.website}
                                    onChange={e => setProfile({...profile, website: e.target.value})}
                                    disabled={loading}
                                    data-testid="website-input"
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="organisation">Organisation</Label>
                                <Input
                                    id="organisation"
                                    value={profile.organisation}
                                    disabled={true}
                                    data-testid="organization-input"
                                />
                            </div>
                        </div>

                        <Button type="submit" disabled={loading} className="w-full" data-testid="save-profile-button">
                            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin"/>}
                            Save Changes
                        </Button>
                    </form>
                </CardContent>
            </Card>
            {isMobile && <ContactForm/>}
        </div>
    );
};

export default ProfilePage;
