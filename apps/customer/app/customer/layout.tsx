import '../globals.css'
import { Inter } from 'next/font/google'
import Link from 'next/link'
import { createClient } from '@/app/supabase/server'
import { SidebarWithSelectors } from '@/app/customer/sidebar'
import { Suspense } from 'react'
import { checkAdmin } from '@/app/auth-utils'
import { Skeleton } from '@ui/components/ui/skeleton'
import { Toaster } from '@ui/components/ui/toaster'
import { redirect } from 'next/navigation'
import { AuthProvider } from '@/components/context/auth/auth-context'
import { EntityProvider } from '@/components/context/entity/entity-context'

const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
})
const defaultUrl = process.env.VERCEL_URL
  ? `https://${process.env.VERCEL_URL}`
  : 'http://localhost:3000'


export default async function Layout({
                                       children,
                                     }: {
  children: React.ReactNode;
}) {
  const supabase = await createClient()

  const user = (await supabase.auth.getUser()).data.user
  if (!user) {
    return redirect('/login?next=/customer')
  }
  const admin = await checkAdmin()
  return (
    <AuthProvider>
      <EntityProvider>
        <Suspense>
          <div className="flex min-h-screen flex-col dark:bg-background relative bg-background text-foreground">
            {/* Subtle background pattern */}
            <div className="background-pattern fixed inset-0 pointer-events-none z-0">
              {/* Gradient background */}
              <div
                className="absolute inset-0 bg-gradient-to-br from-brand/5 via-transparent to-brand-contrast/5 dark:from-brand/10 dark:to-brand-contrast/10"></div>

              {/* Subtle dot pattern - light mode */}
              <div className="absolute inset-0 opacity-100 dark:hidden"
                   style={{
                     backgroundImage: 'radial-gradient(circle at 13% 47%, rgba(140, 140, 140,0.03) 0%, rgba(140, 140, 140,0.03) 25%,transparent 25%, transparent 100%),radial-gradient(circle at 28% 63%, rgba(143, 143, 143,0.03) 0%, rgba(143, 143, 143,0.03) 16%,transparent 16%, transparent 100%),radial-gradient(circle at 81% 56%, rgba(65, 65, 65,0.03) 0%, rgba(65, 65, 65,0.03) 12%,transparent 12%, transparent 100%),radial-gradient(circle at 26% 48%, rgba(60, 60, 60,0.03) 0%, rgba(60, 60, 60,0.03) 6%,transparent 6%, transparent 100%),radial-gradient(circle at 97% 17%, rgba(150, 150, 150,0.03) 0%, rgba(150, 150, 150,0.03) 56%,transparent 56%, transparent 100%),radial-gradient(circle at 50% 100%, rgba(25, 25, 25,0.03) 0%, rgba(25, 25, 25,0.03) 36%,transparent 36%, transparent 100%),radial-gradient(circle at 55% 52%, rgba(69, 69, 69,0.03) 0%, rgba(69, 69, 69,0.03) 6%,transparent 6%, transparent 100%),linear-gradient(90deg, rgb(255,255,255),rgb(255,255,255))',
                   }}>
              </div>

              {/* Subtle dot pattern - dark mode */}
              <div className="absolute inset-0 hidden dark:block animate-subtle-shift"
                   style={{
                     backgroundImage: 'radial-gradient(circle, rgba(255,255,255,0.03) 1px, transparent 1px), radial-gradient(circle, rgba(255,255,255,0.02) 1px, transparent 1px)',
                     backgroundSize: '40px 40px, 20px 20px',
                     backgroundPosition: '0 0, 20px 20px',
                     opacity: 0.4,
                   }}>
              </div>

            </div>

            <SidebarWithSelectors admin={admin}>
              <Toaster />
              <main
                className="min-h-screen flex flex-col items-center m-auto w-full text-foreground  relative z-10">
                <Suspense fallback={
                  <div>
                    <Skeleton className="h-4 w-[250px]" />
                    <div className="space-x-2">
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-full" />
                    </div>
                  </div>
                }>{children}</Suspense>
              </main>
              <footer className="bg-white dark:bg-muted border-t w-full mt-4 relative z-10">
                <div className=" mx-auto flex items-center justify-between h-16 px-4 md:px-6">
                  <p className="text-zinc-500 dark:text-zinc-400 text-sm">© 2024 Eko Intelligence Ltd.</p>
                  <div className="flex items-center gap-4">
                    <Link
                      className="text-zinc-500 hover:text-zinc-900 dark:text-zinc-400 dark:hover:text-zinc-50"
                      href="/policy/privacy">
                      Privacy Policy
                    </Link>
                    <Link
                      className="text-zinc-500 hover:text-zinc-900 dark:text-zinc-400 dark:hover:text-zinc-50"
                      href="/policy/tos">
                      Terms of Service
                    </Link>
                  </div>
                </div>
              </footer>
            </SidebarWithSelectors>


          </div>
        </Suspense>
      </EntityProvider>
    </AuthProvider>
  )
}
