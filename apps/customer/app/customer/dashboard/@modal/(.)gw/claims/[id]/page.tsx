"use client";

import { createClient } from '@/app/supabase/client'
import React, { use, useEffect, useState } from 'react'
import { ClaimTypeV2 } from '@/types/claim'
import { useAuth } from '@/components/context/auth/auth-context'
import ClaimDetailV2 from '@/app/customer/dashboard/gw/claims/claim-detail-v2'
import { SimpleModal } from '@/components/simple-modal'

export default function Page(props: { params: Promise<{ id: string }> }) {
    const params = use(props.params);
    const { id } = params;

    const [claimData, setClaimData] = useState<ClaimTypeV2 | null>(null);
    const auth = useAuth();

    useEffect(() => {
        const fetchData = async () => {
            const supabase = createClient();

            const { data, error } = await supabase
              .from('xfer_claims')
                .select("*")
                .eq("id", +id)
                .single();

            if (error) {
                console.error("Error fetching claim:", error);
            } else {
                // Cast to unknown first to avoid TypeScript errors with Json type
                setClaimData(data as unknown as ClaimTypeV2);
            }
        };

        fetchData();
    }, [id]);

    if (!claimData) return null;

    return (
        <SimpleModal>
            <ClaimDetailV2 claim={claimData} admin={auth.admin} />
        </SimpleModal>
    );
}
