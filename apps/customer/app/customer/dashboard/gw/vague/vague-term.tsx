'use client'
import { VagueType } from '@/types'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@ui/components/ui/card'
import { Badge } from '@ui/components/ui/badge'
import { EkoMarkdown } from '@/components/markdown/eko-markdown'
import { CitationType } from '@/components/citation'
import React from 'react'
import { AdminDeleteButton } from '@/components/admin'

export function VagueTerm(props: { item: VagueType, admin: boolean }) {
    return <Card className="w-full mt-4 relative group">
        <AdminDeleteButton
          tableName="_deprecated_xfer_gw_vague_v2"
            recordId={props.item.id}
            recordType="vague term"
        />
        <CardHeader>
            <div className="flex justify-between items-center">
                <CardTitle className="text-xl">{props.item.phrase}</CardTitle>
                <Badge
                    variant={props.item.model.score! >= 75 ? "destructive" : "default"}>
                    Rating {100 - props.item.model.score!}%
                </Badge>
            </div>
            <CardDescription>{props.item.model.summary}</CardDescription>
        </CardHeader>
        <CardContent>
            <EkoMarkdown
                citations={props.item.model.citations as CitationType[]}
                admin={props.admin}>{props.item.model.analysis}</EkoMarkdown>
        </CardContent>
    </Card>;
}
