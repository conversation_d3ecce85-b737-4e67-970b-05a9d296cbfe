import { ClaimTypeV2 } from '@/types/claim'
import { Card, CardContent } from '@ui/components/ui/card'
import { Badge } from '@ui/components/ui/badge'
import { AlertCircle, CheckCircle2 } from 'lucide-react'
import React from 'react'
import { motion } from 'framer-motion'
import Link from 'next/link'
import { useEntity } from '@/components/context/entity/entity-context'
import { cn } from '@utils/lib/utils'
import { EkoMarkdown } from '@/components/markdown/eko-markdown'
import Markdown from 'react-markdown'
import { AdminDeleteButton } from '@/components/admin'

const variants = {
    hidden: {opacity: 0, y: 20},
    visible: {opacity: 1, y: 0},
};

const greenwashingTypes: {[key:string]:string} = {
    "vague": "Vague",
    "misleading": "Misleading",
    "offsetting": "Includes Offsetting",
    "intentions": "Describes Intentions",
    "distracting": "Distracting",
    "org": "Possible Greenwashing",
    "fund": "Possible Greenwashing",
    "product": "Possible Greenwashing"
}

function accuracy(valid: boolean, greenwashing: boolean, greenwashingType?: string) {
    if (greenwashing) {
        if (valid) {
            return "Accurate but " + (greenwashingTypes[greenwashingType || ""] || "Greenwashing");
        } else {
            return "Inaccurate and " + (greenwashingTypes[greenwashingType || ""] || "Greenwashing");
        }
    } else {
        return valid ? "Accurate Claim" : "Inaccurate";
    }
}

export function ClaimsListV2({claimsData, admin, compact = false}: {
    claimsData: ClaimTypeV2[] | undefined | null,
    admin: boolean,
    compact?: boolean
}) {
    const {queryString} = useEntity();

    if (!claimsData || claimsData.length === 0) {
        return (
            <div className="text-center py-8" data-testid="empty-state">
                <p className="text-sm text-muted-foreground" data-testid="no-claims-message">No claims found</p>
            </div>
        );
    }

    return (
        <div className={cn("mx-auto container", compact ? "p-0" : "p-4 min-h-screen")} data-testid="claims-list">
            {claimsData.map((claim, j) => {
                const model = claim.model;

                // Determine if the claim is valid and if it's greenwashing
                const isValid = claim.verified !== null ? claim.verified : model.valid_claim;
                const isGreenwashing = model.greenwashing || false;

                // Get greenwashing type from the model if available
                let greenwashingType = '';
                if (typeof model.greenwashing === 'object' && model.greenwashing !== null) {
                    // Use type assertion to access the type property
                    const gwObj = model.greenwashing as any;
                    greenwashingType = gwObj.type || '';
                }

                // Determine the claim status icon, color, and text
                let statusIcon;
                let statusColor;
                let statusText;

                if (isValid) {
                    statusIcon = <CheckCircle2 className="w-5 h-5 inline-block"/>;
                    statusColor = "bg-green-50 border-green-200 text-green-700 dark:bg-green-950 dark:border-green-800 dark:text-green-400";
                    statusText = "Accurate";
                } else {
                    statusIcon = <AlertCircle className="w-5 h-5 inline-block"/>;
                    statusColor = "bg-red-50 border-red-200 text-red-700 dark:bg-red-950 dark:border-red-800 dark:text-red-400";
                    statusText = "Inaccurate";
                }

                // If it's greenwashing, add that to the status text
                if (isGreenwashing) {
                    statusText += greenwashingType ? ` (${greenwashingTypes[greenwashingType] || "Greenwashing"})` : " (Greenwashing)";
                }

                // Get document info from the model or first citation
                const firstCitation = model.citations && model.citations.length > 0 ? model.citations[0] : null;
                const docAuthors = firstCitation?.authors || [];
                const docYear = model.claim_doc_year || firstCitation?.year || new Date().getFullYear();
                const docTitle = model.claim_doc || firstCitation?.title || '';

                // Truncate text if too long
                const truncatedText = claim.summary!!.length > 120 ? claim.summary!!.substring(0, 90) + '...' : model.summary

                return (
                    <motion.div
                        initial="hidden"
                        key={j}
                        exit="hidden"
                        whileInView="visible"
                        variants={variants}
                        viewport={{once: true, amount: 0.2}} // Triggers once, 20% in view
                        transition={{duration: 0.3, ease: 'easeOut', delay: 0.2}}
                    >
                        <Link href={"/customer/dashboard/gw/claims/" + claim.id + "?" + queryString} passHref>
                            <Card className="mb-4 hover:shadow-md transition-shadow duration-200 overflow-hidden relative group" data-testid="claim-item">
                                <AdminDeleteButton
                                  tableName="xfer_claims"
                                    recordId={claim.id}
                                    recordType="claim"
                                />
                                {/* Confidence indicator bar */}
                                <div className="w-full h-1.5 bg-slate-100 dark:bg-slate-800">
                                    <div
                                        className={`h-full ${isValid ? 'bg-green-500' : 'bg-red-500'}`}
                                        style={{width: `${model.confidence}%`}}
                                    />
                                </div>

                                <CardContent className="pt-4 pb-4">
                                    <div className="flex justify-between items-start mb-3">
                                        <Badge className={`${statusColor} flex items-center gap-1`}>
                                            {statusIcon}
                                            {statusText}
                                        </Badge>
                                        <div className="flex items-center gap-2">
                                            <Badge variant="outline" data-testid="claim-date">{docYear}</Badge>
                                            {model.esg_claim && <Badge variant="secondary">ESG</Badge>}
                                        </div>
                                    </div>

                                    <div className="mb-3" data-testid="claim-title">
                                        <div className="font-medium text-lg">
                                            <Markdown
                                              children={'"' + (claim.statement_text || model.text || truncatedText) + '"'} />
                                        </div>
                                    </div>

                                    <div className="flex flex-col gap-2">
                                        <div className="text-sm text-muted-foreground line-clamp-6" data-testid="claim-content">
                                            <EkoMarkdown
                                              citations={[]}
                                              admin={admin}
                                              skipCitations={true}
                                            >
                                                {model.verdict}
                                            </EkoMarkdown>
                                        </div>

                                        <div className="flex justify-between items-center mt-2" data-testid="claim-metadata">
                                            <div className="text-xs text-muted-foreground">
                                                ID: {claim.id}
                                            </div>
                                            <div className="flex items-center gap-2">
                                                <Badge variant="secondary" data-testid="claim-confidence">
                                                    {model.confidence}%
                                                </Badge>
                                                <Badge variant="outline" data-testid="claim-importance">
                                                    {model.importance}
                                                </Badge>
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </Link>
                    </motion.div>
                );
            })}
        </div>
    );
}
