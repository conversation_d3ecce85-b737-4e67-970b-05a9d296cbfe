import React from 'react'
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@ui/components/ui/card'
import { Info } from 'lucide-react'
import { CitationType, CompactCitation, reduceCitations } from '@/components/citation'
import { EkoMarkdown } from '@/components/markdown/eko-markdown'
import { CherryTypeV2 } from '@/types'

export default function CherryPicking ({data, admin}: { data: CherryTypeV2, admin:boolean })  {

    const citations:any[]= [];

    // Get statements from the model field in v2 format
    const greenFlags = data.model.positive_statements || [];
    const redFlags = data.model.negative_statements || [];

    greenFlags.forEach((flag:any) => {
        if (flag.citations) {
            flag.citations.forEach((citation:any) => citations.push(citation));
        }
    });

    redFlags.forEach((flag:any) => {
        if (flag.citations) {
            flag.citations.forEach((citation:any) => citations.push(citation));
        }
    });

    for (const citation of citations) {
        if(!citation) {
            console.log("Citation is null");
        }
    }
    return (
        <div className="p-6 space-y-6">

            <div>
                <h2 >Cherry Picking</h2>
                <div>{data.reason}</div>
            </div>

            <div>
                <h2>Explanation</h2>
                <div>{data.explanation}</div>
            </div>

            <div>
                <h2>Detailed Analysis</h2>
                <div><EkoMarkdown citations={data.model.citations as unknown as CitationType[] || []}
                                  admin={admin}>{data.analysis}</EkoMarkdown></div>
            </div>


            {data.model.citations && (<Card className="mb-6">
                <CardHeader>
                    <CardTitle>References</CardTitle>
                </CardHeader>
                <CardContent>
                    {reduceCitations(data.model.citations as unknown as CitationType[] || []).map((citation) => (
                        <CompactCitation key={citation.doc_id + ":" + citation.pages.join(",")} data={citation}
                                         admin={admin}/>))}
                </CardContent>
            </Card>)}

            <div className="mt-6 text-sm text-zinc-500 flex items-center">
                <Info className="mr-2 h-4 w-4"/>
                <span>Run ID: {data.run_id} | Entity ID: {data.entity_xid} | Model: {data.model.model} | Label: {data.label}</span>
            </div>
        </div>
    );
};
