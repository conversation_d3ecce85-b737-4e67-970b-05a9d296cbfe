import React from 'react'
import { Badge } from '@ui/components/ui/badge'
import { <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle, Cherry, Info, TrendingUp, Waves } from 'lucide-react'
import { CitationType, CompactCitation, reduceCitations } from '@/components/citation'
import { EkoMarkdown } from '@/components/markdown/eko-markdown'
import { CherryTypeV2 } from '@/types'
import { StatementAndMetadata } from '@/types/types'
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@ui/components/ui/accordion'
import { GlassCard } from '@/components/ui/glass-card'
import { AdminDeleteButton } from '@/components/admin'

interface SelectiveHighlightingCardProps {
  data: CherryTypeV2;
  admin: boolean;
}

export default function SelectiveHighlightingCard({ data, admin }: SelectiveHighlightingCardProps) {
  console.log("SelectiveHighlightingCard received data:", data);

  // Safely access model properties with fallbacks
  const model = data.model || {};
  const modelType = model.model || 'unknown';
  const isFlooding = modelType === 'flooding';
  const isCherryPicking = modelType === 'cherry_picking';
  const severity = model.severity || 0;
  const score = model.score || 0;
  const confidence = model.confidence || 0;

  // Determine the badge color based on the model type and severity
  const getBadgeVariant = () => {
    if (severity >= 70) return "destructive";
    if (severity >= 40) return "secondary"; // Use secondary instead of warning as warning is not a valid variant
    return "secondary";
  };

  // Format the score as a percentage
  const formatScore = (score: number) => {
    return `${score}%`;
  };

  return (
    <GlassCard
      variant="default"
      className={`overflow-hidden border-l-4 relative group ${
        isFlooding
          ? 'border-l-amber-500 hover:border-l-amber-400'
          : 'border-l-rose-500 hover:border-l-rose-400'
      } transition-all duration-300`}
    >
      <AdminDeleteButton
        tableName="xfer_selective"
        recordId={data.id}
        recordType={isFlooding ? "flooding" : "cherry picking"}
      />
      {/* Header */}
      <div className={`p-6 pb-4 rounded-t-2xl ${
        isFlooding
          ? 'bg-gradient-to-r from-amber-50/50 to-transparent dark:from-amber-950/20 dark:to-transparent'
          : 'bg-gradient-to-r from-rose-50/50 to-transparent dark:from-rose-950/20 dark:to-transparent'
      }`}>
        <div className="flex justify-between items-start">
          <div className="space-y-2">
            <div className="flex items-center gap-3">
              {isFlooding ? (
                <>
                  <div className="p-2 rounded-xl bg-amber-100 dark:bg-amber-900/30">
                    <Waves className="h-5 w-5 text-amber-600 dark:text-amber-400" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold">Flooding</h3>
                    <p className="text-sm text-muted-foreground">Information overwhelming</p>
                  </div>
                </>
              ) : (
                <>
                  <div className="p-2 rounded-xl bg-rose-100 dark:bg-rose-900/30">
                    <Cherry className="h-5 w-5 text-rose-600 dark:text-rose-400" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold">Cherry Picking</h3>
                    <p className="text-sm text-muted-foreground">Selective data presentation</p>
                  </div>
                </>
              )}
              <Badge variant={getBadgeVariant()} className="ml-2">
                Severity: {severity}
              </Badge>
            </div>
            <h4 className="text-base font-medium text-foreground/90">
              {model.label || 'No label available'}
            </h4>
          </div>
          <div className="text-right space-y-1">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
              <span className="text-2xl font-bold">{formatScore(score)}</span>
            </div>
            <div className="text-xs text-muted-foreground space-y-1">
              <div>Confidence: {confidence}%</div>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="px-6 space-y-6">
        <div className="space-y-3">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Info className="h-4 w-4 text-brand" />
            Summary
          </h3>
          <div className="p-4 rounded-xl bg-muted/30 border border-border/50">
            <p className="text-sm leading-relaxed">{model.reason || 'No summary available'}</p>
          </div>
        </div>

        <div className="space-y-3">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <AlertTriangle className="h-4 w-4 text-amber-500" />
            Explanation
          </h3>
          <div className="p-4 rounded-xl bg-muted/30 border border-border/50">
            <p className="text-sm leading-relaxed">{model.explanation || 'No explanation available'}</p>
          </div>
        </div>

        <div className="space-y-3">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <TrendingUp className="h-4 w-4 text-brand" />
            Detailed Analysis
          </h3>
          <div className="p-4 rounded-xl bg-muted/30 border border-border/50 prose dark:prose-invert max-w-none prose-sm">
            <EkoMarkdown
              citations={model.citations as unknown as CitationType[] || []}
              admin={admin}
            >
              {model.analysis || 'No detailed analysis available'}
            </EkoMarkdown>
          </div>
        </div>

        <Accordion type="single" collapsible className="w-full">
          <AccordionItem value="statements" className="border border-border/50 rounded-xl px-4">
            <AccordionTrigger className="hover:no-underline">
              <div className="flex items-center gap-3">
                <div className="p-1.5 rounded-lg bg-brand/10">
                  <CheckCircle className="h-4 w-4 text-brand" />
                </div>
                <span className="font-medium">Statements Analysis</span>
                <Badge variant="outline" className="ml-auto">
                  {model.positive_statements?.length || 0} positive, {model.negative_statements?.length || 0} negative
                </Badge>
              </div>
            </AccordionTrigger>
            <AccordionContent className="pt-4">
              <div className="space-y-6">
                {model.positive_statements && model.positive_statements.length > 0 && (
                  <div className="space-y-3">
                    <h4 className="text-sm font-semibold flex items-center gap-2">
                      <div className="p-1 rounded bg-green-100 dark:bg-green-900/30">
                        <CheckCircle className="h-3 w-3 text-green-600 dark:text-green-400" />
                      </div>
                      Positive Statements
                    </h4>
                    <div className="space-y-2">
                      {model.positive_statements.map((statement: StatementAndMetadata, index: number) => (
                        <div key={`positive-${index}`} className="p-3 rounded-lg bg-green-50/50 dark:bg-green-950/20 border border-green-200/50 dark:border-green-800/50">
                          <p className="text-sm leading-relaxed">{statement.statement_text}</p>
                          {statement.metadata?.impact_value && (
                            <Badge variant="outline" className="mt-2 text-xs">
                              Impact: {statement.metadata.impact_value}
                            </Badge>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {model.negative_statements && model.negative_statements.length > 0 && (
                  <div className="space-y-3">
                    <h4 className="text-sm font-semibold flex items-center gap-2">
                      <div className="p-1 rounded bg-red-100 dark:bg-red-900/30">
                        <AlertTriangle className="h-3 w-3 text-red-600 dark:text-red-400" />
                      </div>
                      Negative Statements
                    </h4>
                    <div className="space-y-2">
                      {model.negative_statements.map((statement: StatementAndMetadata, index: number) => (
                        <div key={`negative-${index}`} className="p-3 rounded-lg bg-red-50/50 dark:bg-red-950/20 border border-red-200/50 dark:border-red-800/50">
                          <p className="text-sm leading-relaxed">{statement.statement_text}</p>
                          {statement.metadata?.impact_value && (
                            <Badge variant="outline" className="mt-2 text-xs">
                              Impact: {statement.metadata.impact_value}
                            </Badge>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>

        {model.citations && model.citations.length > 0 && (
          <div className="space-y-3">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Info className="h-4 w-4 text-brand" />
              References
            </h3>
            <div className="p-4 rounded-xl bg-muted/30 border border-border/50 space-y-3">
              {reduceCitations(model.citations as unknown as CitationType[]).map((citation) => (
                <CompactCitation
                  key={citation.doc_id + ":" + citation.pages.join(",")}
                  data={citation}
                  admin={admin}
                />
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="px-6 py-4 mt-6 border-t border-border/20 bg-muted/10 rounded-b-2xl">
        <div className="text-xs text-muted-foreground flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Info className="h-3 w-3" />
            <span>ID: {data.id} | Run: {data.run_id} | Entity: {data.entity_xid}</span>
          </div>
          {model.created_at && (
            <div className="flex items-center gap-1">
              <span>Created: {new Date(model.created_at).toLocaleDateString()}</span>
            </div>
          )}
        </div>
      </div>
    </GlassCard>
  );
}
