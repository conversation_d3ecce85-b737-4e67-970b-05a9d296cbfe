// Interface to match the Python XferClaimModel class
import { CitationType } from '@/components/citation'

export interface XferClaimModel {
  id: number;
  statement_id: number;
  entity_xid: string;
  valid_claim: boolean;
  greenwashing: boolean;
  verdict: string;
  summary: string;
  conclusion: string;
  confidence: number;
  text: string;
  statement_text?: string; // The text of the statement the claim is about
  context?: string;
  claim_doc: string;
  claim_doc_year: number;
  counters: any;
  citations: CitationType[];
  llm_greenwashing: boolean;
  importance: number; // Importance of the claim (0-100)
  company: string;
  company_id: number;
  esg_claim: boolean;
  created_at?: string; // ISO date string
}

// Interface for the xfer_claims table row
export interface ClaimTypeV2 {
  id: number;
  entity_xid: string;
  run_id: number;
  statement_id: number | null;
  verified: boolean | null;
  // Direct columns (moved from model for performance)
  conclusion: string | null;
  context: string | null;
  summary: string | null;
  statement_text: string | null;
  importance: number | null;
  // JSON model field (contains remaining data)
  model: XferClaimModel;
}
