import { CitationType } from '@/components/citation'

// Interface to match the Python XferVagueModel class
export interface XferVagueModel {
  id: number;
  entity_xid: string;
  phrase: string;
  score: number;
  explanation: string;
  analysis: string;
  summary: string;
  citations: CitationType[];
  rank?: number;
  created_at?: string; // ISO date string
}

// Interface for the _deprecated_xfer_gw_vague_v2 table row
export interface VagueTypeV2 {
  id: number;
  entity_xid: string;
  run_id: number;
  phrase: string;
  model: XferVagueModel;
}
