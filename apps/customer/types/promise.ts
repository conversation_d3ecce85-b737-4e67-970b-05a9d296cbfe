// Interface to match the Python XferPromiseModel class
import { CitationType } from '@/components/citation'

export interface XferPromiseModel {
  id: number;
  statement_id: number;
  entity_xid: string;
  promise_kept: boolean;
  greenwashing: boolean;
  verdict: string;
  summary: string;
  conclusion: string;
  confidence: number;
  text: string;
  statement_text?: string; // The text of the statement the promise is about
  context?: string;
  promise_doc: string;
  promise_doc_year: number;
  evidence: any;
  citations: CitationType[];
  llm_greenwashing: boolean;
  company: string;
  company_id: number;
  esg_promise: boolean;
  created_at?: string; // ISO date string
}

// Interface for the xfer_promises table row
export interface PromiseTypeV2 {
  id: number;
  entity_xid: string;
  run_id: number;
  statement_id: number | null;
  kept: boolean | null;
  // Direct columns (moved from model for performance)
  conclusion: string | null;
  summary: string | null;
  statement_text: string | null;
  // JSON model field (contains remaining data including evidence)
  model: XferPromiseModel;
}
