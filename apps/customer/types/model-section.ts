// Interface to match the model section structure
export interface ModelSectionModel {
  model: string;
  section: string;
  title: string | null;
  description: string | null;
  level: string | null;
  icon: string | null;
  status: string;
  created_at?: string; // ISO date string
  updated_at?: string; // ISO date string
}

// Interface for the xfer_model_sections table row
export interface ModelSectionTypeV2 {
  id?: number;
  model: string;
  section: string;
  title?: string | null;
  description?: string | null;
  level?: string | null;
  icon?: string | null;
  status?: string;
  created_at?: string;
  updated_at?: string;
  data?: any;
}
