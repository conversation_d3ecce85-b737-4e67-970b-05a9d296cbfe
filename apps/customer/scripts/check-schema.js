const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase URL or Service Key')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function checkSchema() {
  try {
    console.log('Checking database schema...')
    
    // Check if profiles table exists
    console.log('\n1. Checking profiles table...')
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('*')
      .limit(1)
    
    if (profilesError) {
      console.log('Profiles table error:', profilesError.message)
    } else {
      console.log('Profiles table exists and is accessible')
      if (profiles.length > 0) {
        console.log('Sample profile structure:', Object.keys(profiles[0]))
      }
    }

    // Check if doc_documents table exists
    console.log('\n2. Checking doc_documents table...')
    const { data: docs, error: docsError } = await supabase
      .from('doc_documents')
      .select('*')
      .limit(1)
    
    if (docsError) {
      console.log('doc_documents table error:', docsError.message)
    } else {
      console.log('doc_documents table exists and is accessible')
      if (docs.length > 0) {
        console.log('Sample document structure:', Object.keys(docs[0]))
      }
    }
    
    // Check if document_templates table exists
    console.log('\n3. Checking document_templates table...')
    const { data: templates, error: templatesError } = await supabase
      .from('document_templates')
      .select('*')
      .limit(1)
    
    if (templatesError) {
      console.log('Document_templates table error:', templatesError.message)
    } else {
      console.log('Document_templates table exists and is accessible')
      if (templates.length > 0) {
        console.log('Sample template structure:', Object.keys(templates[0]))
      }
    }
    
    // Try to get current user to see what auth looks like
    console.log('\n4. Checking auth...')
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError) {
      console.log('Auth error:', authError.message)
    } else if (user) {
      console.log('Current user ID:', user.id)
    } else {
      console.log('No authenticated user (expected for service key)')
    }
    
  } catch (error) {
    console.error('Schema check failed:', error)
  }
}

checkSchema()
