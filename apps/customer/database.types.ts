export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      _deprecated_xfer_gw_vague_v2: {
        Row: {
          entity_xid: string
          id: number
          model: Json
          phrase: string | null
          run_id: number
        }
        Insert: {
          entity_xid: string
          id: number
          model: Json
          phrase?: string | null
          run_id: number
        }
        Update: {
          entity_xid?: string
          id?: number
          model?: Json
          phrase?: string | null
          run_id?: number
        }
        Relationships: []
      }
      acc_messages: {
        Row: {
          created_at: string
          deleted_at: string | null
          id: number
          message: string | null
          read_at: string | null
          recipient: string
          sender_name: string | null
          title: string | null
          type: Database["public"]["Enums"]["acc_message_type"] | null
        }
        Insert: {
          created_at?: string
          deleted_at?: string | null
          id?: number
          message?: string | null
          read_at?: string | null
          recipient: string
          sender_name?: string | null
          title?: string | null
          type?: Database["public"]["Enums"]["acc_message_type"] | null
        }
        Update: {
          created_at?: string
          deleted_at?: string | null
          id?: number
          message?: string | null
          read_at?: string | null
          recipient?: string
          sender_name?: string | null
          title?: string | null
          type?: Database["public"]["Enums"]["acc_message_type"] | null
        }
        Relationships: [
          {
            foreignKeyName: "acc_message_recipient_fkey"
            columns: ["recipient"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "acc_message_recipient_fkey"
            columns: ["recipient"]
            isOneToOne: false
            referencedRelation: "view_quota_for_entity_analysis"
            referencedColumns: ["profile_id"]
          },
          {
            foreignKeyName: "acc_message_recipient_fkey"
            columns: ["recipient"]
            isOneToOne: false
            referencedRelation: "view_quota_for_single_doc"
            referencedColumns: ["profile_id"]
          },
        ]
      }
      acc_organisations: {
        Row: {
          created_at: string
          email_domain: string | null
          entity_xid: string | null
          feature_flags: string[] | null
          id: number
          name: string | null
          uuid: string | null
        }
        Insert: {
          created_at?: string
          email_domain?: string | null
          entity_xid?: string | null
          feature_flags?: string[] | null
          id?: number
          name?: string | null
          uuid?: string | null
        }
        Update: {
          created_at?: string
          email_domain?: string | null
          entity_xid?: string | null
          feature_flags?: string[] | null
          id?: number
          name?: string | null
          uuid?: string | null
        }
        Relationships: []
      }
      acc_quota: {
        Row: {
          created_at: string
          customer: string | null
          id: number
          item_type: Database["public"]["Enums"]["quota_item_type"]
          organisation: number
          period: string | null
          quantity: number
          scope: Database["public"]["Enums"]["quota_scope"]
        }
        Insert: {
          created_at?: string
          customer?: string | null
          id?: number
          item_type: Database["public"]["Enums"]["quota_item_type"]
          organisation: number
          period?: string | null
          quantity: number
          scope?: Database["public"]["Enums"]["quota_scope"]
        }
        Update: {
          created_at?: string
          customer?: string | null
          id?: number
          item_type?: Database["public"]["Enums"]["quota_item_type"]
          organisation?: number
          period?: string | null
          quantity?: number
          scope?: Database["public"]["Enums"]["quota_scope"]
        }
        Relationships: [
          {
            foreignKeyName: "acc_quota_customer_fkey"
            columns: ["customer"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "acc_quota_customer_fkey"
            columns: ["customer"]
            isOneToOne: false
            referencedRelation: "view_quota_for_entity_analysis"
            referencedColumns: ["profile_id"]
          },
          {
            foreignKeyName: "acc_quota_customer_fkey"
            columns: ["customer"]
            isOneToOne: false
            referencedRelation: "view_quota_for_single_doc"
            referencedColumns: ["profile_id"]
          },
          {
            foreignKeyName: "acc_quota_organisation_fkey"
            columns: ["organisation"]
            isOneToOne: false
            referencedRelation: "acc_organisations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "acc_quota_organisation_fkey"
            columns: ["organisation"]
            isOneToOne: false
            referencedRelation: "view_quota_for_entity_analysis"
            referencedColumns: ["org_id"]
          },
          {
            foreignKeyName: "acc_quota_organisation_fkey"
            columns: ["organisation"]
            isOneToOne: false
            referencedRelation: "view_quota_for_single_doc"
            referencedColumns: ["org_id"]
          },
        ]
      }
      acc_usage: {
        Row: {
          created_at: string
          customer: string | null
          id: number
          organisation: number
          usage_item: string | null
          usage_quantity: number | null
        }
        Insert: {
          created_at?: string
          customer?: string | null
          id?: number
          organisation: number
          usage_item?: string | null
          usage_quantity?: number | null
        }
        Update: {
          created_at?: string
          customer?: string | null
          id?: number
          organisation?: number
          usage_item?: string | null
          usage_quantity?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "acc_usage_customer_fkey"
            columns: ["customer"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "acc_usage_customer_fkey"
            columns: ["customer"]
            isOneToOne: false
            referencedRelation: "view_quota_for_entity_analysis"
            referencedColumns: ["profile_id"]
          },
          {
            foreignKeyName: "acc_usage_customer_fkey"
            columns: ["customer"]
            isOneToOne: false
            referencedRelation: "view_quota_for_single_doc"
            referencedColumns: ["profile_id"]
          },
          {
            foreignKeyName: "acc_usage_organisation_fkey"
            columns: ["organisation"]
            isOneToOne: false
            referencedRelation: "acc_organisations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "acc_usage_organisation_fkey"
            columns: ["organisation"]
            isOneToOne: false
            referencedRelation: "view_quota_for_entity_analysis"
            referencedColumns: ["org_id"]
          },
          {
            foreignKeyName: "acc_usage_organisation_fkey"
            columns: ["organisation"]
            isOneToOne: false
            referencedRelation: "view_quota_for_single_doc"
            referencedColumns: ["org_id"]
          },
        ]
      }
      api_queue: {
        Row: {
          created_at: string | null
          id: number
          input_cost: number | null
          message: string | null
          output_cost: number | null
          request_action: string
          request_data: Json | null
          requester: string | null
          response_data: Json | null
          status: Database["public"]["Enums"]["api_request_status"] | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          id?: never
          input_cost?: number | null
          message?: string | null
          output_cost?: number | null
          request_action: string
          request_data?: Json | null
          requester?: string | null
          response_data?: Json | null
          status?: Database["public"]["Enums"]["api_request_status"] | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          id?: never
          input_cost?: number | null
          message?: string | null
          output_cost?: number | null
          request_action?: string
          request_data?: Json | null
          requester?: string | null
          response_data?: Json | null
          status?: Database["public"]["Enums"]["api_request_status"] | null
          updated_at?: string | null
        }
        Relationships: []
      }
      cus_ana_companies: {
        Row: {
          created_at: string
          created_by: string | null
          entity_xid: string
          id: number
          name: string
          quota_id: number
        }
        Insert: {
          created_at?: string
          created_by?: string | null
          entity_xid: string
          id?: number
          name: string
          quota_id: number
        }
        Update: {
          created_at?: string
          created_by?: string | null
          entity_xid?: string
          id?: number
          name?: string
          quota_id?: number
        }
        Relationships: [
          {
            foreignKeyName: "cus_ana_companies_acc_quota_id_fk"
            columns: ["quota_id"]
            isOneToOne: false
            referencedRelation: "acc_quota"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "cus_ana_companies_acc_quota_id_fk"
            columns: ["quota_id"]
            isOneToOne: false
            referencedRelation: "view_quota_for_entity_analysis"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "cus_ana_companies_acc_quota_id_fk"
            columns: ["quota_id"]
            isOneToOne: false
            referencedRelation: "view_quota_for_single_doc"
            referencedColumns: ["id"]
          },
        ]
      }
      cus_ana_hist_entity_runs: {
        Row: {
          created_at: string
          entity_xid: string
          id: number
          quota_id: number
          run_by: string | null
          run_id: number
        }
        Insert: {
          created_at?: string
          entity_xid: string
          id?: number
          quota_id: number
          run_by?: string | null
          run_id: number
        }
        Update: {
          created_at?: string
          entity_xid?: string
          id?: number
          quota_id?: number
          run_by?: string | null
          run_id?: number
        }
        Relationships: [
          {
            foreignKeyName: "cus_ana_hist_entity_runs_acc_quota_id_fk"
            columns: ["quota_id"]
            isOneToOne: false
            referencedRelation: "acc_quota"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "cus_ana_hist_entity_runs_acc_quota_id_fk"
            columns: ["quota_id"]
            isOneToOne: false
            referencedRelation: "view_quota_for_entity_analysis"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "cus_ana_hist_entity_runs_acc_quota_id_fk"
            columns: ["quota_id"]
            isOneToOne: false
            referencedRelation: "view_quota_for_single_doc"
            referencedColumns: ["id"]
          },
        ]
      }
      cus_ana_hist_gw_single_doc_runs: {
        Row: {
          created_at: string
          doc_ana_id: number | null
          doc_id: number
          entity_xid: string | null
          id: number
          quota_id: number | null
          run_by: string | null
        }
        Insert: {
          created_at?: string
          doc_ana_id?: number | null
          doc_id: number
          entity_xid?: string | null
          id?: number
          quota_id?: number | null
          run_by?: string | null
        }
        Update: {
          created_at?: string
          doc_ana_id?: number | null
          doc_id?: number
          entity_xid?: string | null
          id?: number
          quota_id?: number | null
          run_by?: string | null
        }
        Relationships: []
      }
      doc_comments: {
        Row: {
          content: string
          created_at: string | null
          document_id: string | null
          id: string
          parent_comment_id: string | null
          position_from: number | null
          position_to: number | null
          resolved: boolean | null
          resolved_at: string | null
          resolved_by: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          content: string
          created_at?: string | null
          document_id?: string | null
          id?: string
          parent_comment_id?: string | null
          position_from?: number | null
          position_to?: number | null
          resolved?: boolean | null
          resolved_at?: string | null
          resolved_by?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          content?: string
          created_at?: string | null
          document_id?: string | null
          id?: string
          parent_comment_id?: string | null
          position_from?: number | null
          position_to?: number | null
          resolved?: boolean | null
          resolved_at?: string | null
          resolved_by?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "document_comments_parent_comment_id_fkey"
            columns: ["parent_comment_id"]
            isOneToOne: false
            referencedRelation: "doc_comments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_document_comments_document_id"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "doc_documents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_document_comments_document_id"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "user_document_access"
            referencedColumns: ["document_id"]
          },
          {
            foreignKeyName: "fk_document_comments_user_id"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_document_comments_user_id"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "view_quota_for_entity_analysis"
            referencedColumns: ["profile_id"]
          },
          {
            foreignKeyName: "fk_document_comments_user_id"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "view_quota_for_single_doc"
            referencedColumns: ["profile_id"]
          },
        ]
      }
      doc_documents: {
        Row: {
          content: string | null
          created_at: string | null
          created_by: string | null
          data: Json | null
          id: string
          initial_content: string | null
          is_public: boolean | null
          metadata: Json | null
          title: string | null
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          content?: string | null
          created_at?: string | null
          created_by?: string | null
          data?: Json | null
          id: string
          initial_content?: string | null
          is_public?: boolean | null
          metadata?: Json | null
          title?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          content?: string | null
          created_at?: string | null
          created_by?: string | null
          data?: Json | null
          id?: string
          initial_content?: string | null
          is_public?: boolean | null
          metadata?: Json | null
          title?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_collaborative_documents_created_by"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_collaborative_documents_created_by"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "view_quota_for_entity_analysis"
            referencedColumns: ["profile_id"]
          },
          {
            foreignKeyName: "fk_collaborative_documents_created_by"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "view_quota_for_single_doc"
            referencedColumns: ["profile_id"]
          },
          {
            foreignKeyName: "fk_collaborative_documents_updated_by"
            columns: ["updated_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_collaborative_documents_updated_by"
            columns: ["updated_by"]
            isOneToOne: false
            referencedRelation: "view_quota_for_entity_analysis"
            referencedColumns: ["profile_id"]
          },
          {
            foreignKeyName: "fk_collaborative_documents_updated_by"
            columns: ["updated_by"]
            isOneToOne: false
            referencedRelation: "view_quota_for_single_doc"
            referencedColumns: ["profile_id"]
          },
        ]
      }
      doc_operations: {
        Row: {
          clock: number
          created_at: string | null
          document_id: string | null
          id: number
          operation: string
          user_id: string | null
        }
        Insert: {
          clock: number
          created_at?: string | null
          document_id?: string | null
          id?: number
          operation: string
          user_id?: string | null
        }
        Update: {
          clock?: number
          created_at?: string | null
          document_id?: string | null
          id?: number
          operation?: string
          user_id?: string | null
        }
        Relationships: []
      }
      doc_permissions: {
        Row: {
          document_id: string | null
          granted_at: string | null
          granted_by: string | null
          id: string
          permission_type: string | null
          user_id: string | null
        }
        Insert: {
          document_id?: string | null
          granted_at?: string | null
          granted_by?: string | null
          id?: string
          permission_type?: string | null
          user_id?: string | null
        }
        Update: {
          document_id?: string | null
          granted_at?: string | null
          granted_by?: string | null
          id?: string
          permission_type?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_document_permissions_document_id"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "doc_documents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_document_permissions_document_id"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "user_document_access"
            referencedColumns: ["document_id"]
          },
          {
            foreignKeyName: "fk_document_permissions_granted_by"
            columns: ["granted_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_document_permissions_granted_by"
            columns: ["granted_by"]
            isOneToOne: false
            referencedRelation: "view_quota_for_entity_analysis"
            referencedColumns: ["profile_id"]
          },
          {
            foreignKeyName: "fk_document_permissions_granted_by"
            columns: ["granted_by"]
            isOneToOne: false
            referencedRelation: "view_quota_for_single_doc"
            referencedColumns: ["profile_id"]
          },
          {
            foreignKeyName: "fk_document_permissions_user_id"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_document_permissions_user_id"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "view_quota_for_entity_analysis"
            referencedColumns: ["profile_id"]
          },
          {
            foreignKeyName: "fk_document_permissions_user_id"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "view_quota_for_single_doc"
            referencedColumns: ["profile_id"]
          },
        ]
      }
      doc_presence: {
        Row: {
          cursor_position: Json | null
          document_id: string | null
          id: string
          is_active: boolean | null
          last_seen: string | null
          selection: Json | null
          user_avatar: string | null
          user_color: string | null
          user_id: string | null
          user_name: string | null
        }
        Insert: {
          cursor_position?: Json | null
          document_id?: string | null
          id?: string
          is_active?: boolean | null
          last_seen?: string | null
          selection?: Json | null
          user_avatar?: string | null
          user_color?: string | null
          user_id?: string | null
          user_name?: string | null
        }
        Update: {
          cursor_position?: Json | null
          document_id?: string | null
          id?: string
          is_active?: boolean | null
          last_seen?: string | null
          selection?: Json | null
          user_avatar?: string | null
          user_color?: string | null
          user_id?: string | null
          user_name?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_document_presence_document_id"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "doc_documents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_document_presence_document_id"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "user_document_access"
            referencedColumns: ["document_id"]
          },
        ]
      }
      doc_versions: {
        Row: {
          change_summary: string | null
          content: string | null
          created_at: string | null
          created_by: string | null
          data: Json | null
          document_id: string | null
          id: string
          is_auto_save: boolean | null
          title: string | null
          version_number: number
        }
        Insert: {
          change_summary?: string | null
          content?: string | null
          created_at?: string | null
          created_by?: string | null
          data?: Json | null
          document_id?: string | null
          id?: string
          is_auto_save?: boolean | null
          title?: string | null
          version_number: number
        }
        Update: {
          change_summary?: string | null
          content?: string | null
          created_at?: string | null
          created_by?: string | null
          data?: Json | null
          document_id?: string | null
          id?: string
          is_auto_save?: boolean | null
          title?: string | null
          version_number?: number
        }
        Relationships: [
          {
            foreignKeyName: "fk_document_versions_document_id"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "doc_documents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_document_versions_document_id"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "user_document_access"
            referencedColumns: ["document_id"]
          },
        ]
      }
      profiles: {
        Row: {
          avatar_url: string | null
          email: string | null
          feature_flags: string[] | null
          full_name: string | null
          id: string
          is_admin: boolean | null
          name: string | null
          organisation: number | null
          updated_at: string | null
          username: string | null
          website: string | null
        }
        Insert: {
          avatar_url?: string | null
          email?: string | null
          feature_flags?: string[] | null
          full_name?: string | null
          id: string
          is_admin?: boolean | null
          name?: string | null
          organisation?: number | null
          updated_at?: string | null
          username?: string | null
          website?: string | null
        }
        Update: {
          avatar_url?: string | null
          email?: string | null
          feature_flags?: string[] | null
          full_name?: string | null
          id?: string
          is_admin?: boolean | null
          name?: string | null
          organisation?: number | null
          updated_at?: string | null
          username?: string | null
          website?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "profiles_organisation_fkey"
            columns: ["organisation"]
            isOneToOne: false
            referencedRelation: "acc_organisations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "profiles_organisation_fkey"
            columns: ["organisation"]
            isOneToOne: false
            referencedRelation: "view_quota_for_entity_analysis"
            referencedColumns: ["org_id"]
          },
          {
            foreignKeyName: "profiles_organisation_fkey"
            columns: ["organisation"]
            isOneToOne: false
            referencedRelation: "view_quota_for_single_doc"
            referencedColumns: ["org_id"]
          },
        ]
      }
      xfer_claims: {
        Row: {
          conclusion: string | null
          context: string | null
          entity_xid: string
          id: number
          importance: number | null
          model: Json
          run_id: number
          statement_id: number | null
          statement_text: string | null
          summary: string | null
          verified: boolean | null
        }
        Insert: {
          conclusion?: string | null
          context?: string | null
          entity_xid: string
          id: number
          importance?: number | null
          model: Json
          run_id: number
          statement_id?: number | null
          statement_text?: string | null
          summary?: string | null
          verified?: boolean | null
        }
        Update: {
          conclusion?: string | null
          context?: string | null
          entity_xid?: string
          id?: number
          importance?: number | null
          model?: Json
          run_id?: number
          statement_id?: number | null
          statement_text?: string | null
          summary?: string | null
          verified?: boolean | null
        }
        Relationships: []
      }
      xfer_entities: {
        Row: {
          entity_base_entities_json: string | null
          entity_description: string | null
          entity_xid: string
          model: Json
          name: string
          run_id: number
          type: string | null
        }
        Insert: {
          entity_base_entities_json?: string | null
          entity_description?: string | null
          entity_xid: string
          model: Json
          name: string
          run_id: number
          type?: string | null
        }
        Update: {
          entity_base_entities_json?: string | null
          entity_description?: string | null
          entity_xid?: string
          model?: Json
          name?: string
          run_id?: number
          type?: string | null
        }
        Relationships: []
      }
      xfer_entities_v1: {
        Row: {
          description: string | null
          entity_xid: string
          lei: string | null
          name: string | null
          searchable_text: unknown | null
          synonyms: string[] | null
          ticker: string | null
          type: string | null
          url: string | null
        }
        Insert: {
          description?: string | null
          entity_xid: string
          lei?: string | null
          name?: string | null
          searchable_text?: unknown | null
          synonyms?: string[] | null
          ticker?: string | null
          type?: string | null
          url?: string | null
        }
        Update: {
          description?: string | null
          entity_xid?: string
          lei?: string | null
          name?: string | null
          searchable_text?: unknown | null
          synonyms?: string[] | null
          ticker?: string | null
          type?: string | null
          url?: string | null
        }
        Relationships: []
      }
      xfer_flags: {
        Row: {
          entity_xid: string
          flag_analysis: string | null
          flag_statements: Json | null
          flag_summary: string | null
          flag_type: string | null
          id: number
          model: Json
          run_id: number
          trace_json: Json | null
        }
        Insert: {
          entity_xid: string
          flag_analysis?: string | null
          flag_statements?: Json | null
          flag_summary?: string | null
          flag_type?: string | null
          id: number
          model: Json
          run_id: number
          trace_json?: Json | null
        }
        Update: {
          entity_xid?: string
          flag_analysis?: string | null
          flag_statements?: Json | null
          flag_summary?: string | null
          flag_type?: string | null
          id?: number
          model?: Json
          run_id?: number
          trace_json?: Json | null
        }
        Relationships: []
      }
      xfer_gw_single_doc: {
        Row: {
          analysis_conclusion: string | null
          analysis_json: Json
          analysis_recommendations: string | null
          analysis_summary: string | null
          doc_id: number
          entity_xid: string
          id: number
          public_url: string
        }
        Insert: {
          analysis_conclusion?: string | null
          analysis_json: Json
          analysis_recommendations?: string | null
          analysis_summary?: string | null
          doc_id: number
          entity_xid: string
          id?: never
          public_url: string
        }
        Update: {
          analysis_conclusion?: string | null
          analysis_json?: Json
          analysis_recommendations?: string | null
          analysis_summary?: string | null
          doc_id?: number
          entity_xid?: string
          id?: never
          public_url?: string
        }
        Relationships: []
      }
      xfer_model_sections: {
        Row: {
          description: string | null
          icon: string | null
          id: number
          level: string | null
          model: string | null
          section: string | null
          status: string | null
          title: string | null
          updated_at: string
        }
        Insert: {
          description?: string | null
          icon?: string | null
          id: number
          level?: string | null
          model?: string | null
          section?: string | null
          status?: string | null
          title?: string | null
          updated_at?: string
        }
        Update: {
          description?: string | null
          icon?: string | null
          id?: number
          level?: string | null
          model?: string | null
          section?: string | null
          status?: string | null
          title?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      xfer_predict_cluster: {
        Row: {
          cluster_id: number
          created_at: string | null
          entity_xid: string
          historical_vectors_by_year: Json | null
          id: number
          model: Json
          run_id: number
          year: number
        }
        Insert: {
          cluster_id: number
          created_at?: string | null
          entity_xid: string
          historical_vectors_by_year?: Json | null
          id?: number
          model: Json
          run_id: number
          year: number
        }
        Update: {
          cluster_id?: number
          created_at?: string | null
          entity_xid?: string
          historical_vectors_by_year?: Json | null
          id?: number
          model?: Json
          run_id?: number
          year?: number
        }
        Relationships: []
      }
      xfer_predict_component: {
        Row: {
          cluster_id: number
          component_type: string
          created_at: string | null
          entity_xid: string
          id: number
          model: Json
          run_id: number
          year: number
        }
        Insert: {
          cluster_id: number
          component_type: string
          created_at?: string | null
          entity_xid: string
          id?: number
          model: Json
          run_id: number
          year: number
        }
        Update: {
          cluster_id?: number
          component_type?: string
          created_at?: string | null
          entity_xid?: string
          id?: number
          model?: Json
          run_id?: number
          year?: number
        }
        Relationships: []
      }
      xfer_predict_entity_year: {
        Row: {
          created_at: string | null
          entity_xid: string
          id: number
          model: Json
          run_id: number
          year: number
        }
        Insert: {
          created_at?: string | null
          entity_xid: string
          id?: number
          model: Json
          run_id: number
          year: number
        }
        Update: {
          created_at?: string | null
          entity_xid?: string
          id?: number
          model?: Json
          run_id?: number
          year?: number
        }
        Relationships: []
      }
      xfer_promises: {
        Row: {
          conclusion: string | null
          entity_xid: string
          id: number
          kept: boolean | null
          model: Json
          run_id: number
          statement_id: number | null
          statement_text: string | null
          summary: string | null
        }
        Insert: {
          conclusion?: string | null
          entity_xid: string
          id: number
          kept?: boolean | null
          model: Json
          run_id: number
          statement_id?: number | null
          statement_text?: string | null
          summary?: string | null
        }
        Update: {
          conclusion?: string | null
          entity_xid?: string
          id?: number
          kept?: boolean | null
          model?: Json
          run_id?: number
          statement_id?: number | null
          statement_text?: string | null
          summary?: string | null
        }
        Relationships: []
      }
      xfer_runs: {
        Row: {
          completed_at: string | null
          end_year: number | null
          id: number
          model: Json
          run_date: string | null
          run_id: number | null
          run_type: string
          scope: string
          start_year: number | null
          target: string
        }
        Insert: {
          completed_at?: string | null
          end_year?: number | null
          id: number
          model: Json
          run_date?: string | null
          run_id?: number | null
          run_type: string
          scope: string
          start_year?: number | null
          target: string
        }
        Update: {
          completed_at?: string | null
          end_year?: number | null
          id?: number
          model?: Json
          run_date?: string | null
          run_id?: number | null
          run_type?: string
          scope?: string
          start_year?: number | null
          target?: string
        }
        Relationships: []
      }
      xfer_score: {
        Row: {
          entity_xid: string
          model: Json
          run_id: number
          score: number
        }
        Insert: {
          entity_xid: string
          model: Json
          run_id: number
          score: number
        }
        Update: {
          entity_xid?: string
          model?: Json
          run_id?: number
          score?: number
        }
        Relationships: []
      }
      xfer_selective: {
        Row: {
          analysis: string | null
          entity_xid: string
          explanation: string | null
          id: number
          label: string | null
          model: Json
          reason: string | null
          run_id: number
        }
        Insert: {
          analysis?: string | null
          entity_xid: string
          explanation?: string | null
          id: number
          label?: string | null
          model: Json
          reason?: string | null
          run_id: number
        }
        Update: {
          analysis?: string | null
          entity_xid?: string
          explanation?: string | null
          id?: number
          label?: string | null
          model?: Json
          reason?: string | null
          run_id?: number
        }
        Relationships: []
      }
    }
    Views: {
      user_document_access: {
        Row: {
          content: string | null
          created_at: string | null
          created_by: string | null
          document_id: string | null
          metadata: Json | null
          title: string | null
          updated_at: string | null
          updated_by: string | null
          user_permission: string | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_collaborative_documents_created_by"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_collaborative_documents_created_by"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "view_quota_for_entity_analysis"
            referencedColumns: ["profile_id"]
          },
          {
            foreignKeyName: "fk_collaborative_documents_created_by"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "view_quota_for_single_doc"
            referencedColumns: ["profile_id"]
          },
          {
            foreignKeyName: "fk_collaborative_documents_updated_by"
            columns: ["updated_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_collaborative_documents_updated_by"
            columns: ["updated_by"]
            isOneToOne: false
            referencedRelation: "view_quota_for_entity_analysis"
            referencedColumns: ["profile_id"]
          },
          {
            foreignKeyName: "fk_collaborative_documents_updated_by"
            columns: ["updated_by"]
            isOneToOne: false
            referencedRelation: "view_quota_for_single_doc"
            referencedColumns: ["profile_id"]
          },
        ]
      }
      view_entity_analysis_runs: {
        Row: {
          created_at: string | null
          entity_xid: string | null
          id: number | null
          name: string | null
          quota_id: number | null
          run_by: string | null
          run_id: number | null
          type: string | null
        }
        Relationships: [
          {
            foreignKeyName: "cus_ana_hist_entity_runs_acc_quota_id_fk"
            columns: ["quota_id"]
            isOneToOne: false
            referencedRelation: "acc_quota"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "cus_ana_hist_entity_runs_acc_quota_id_fk"
            columns: ["quota_id"]
            isOneToOne: false
            referencedRelation: "view_quota_for_entity_analysis"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "cus_ana_hist_entity_runs_acc_quota_id_fk"
            columns: ["quota_id"]
            isOneToOne: false
            referencedRelation: "view_quota_for_single_doc"
            referencedColumns: ["id"]
          },
        ]
      }
      view_my_companies: {
        Row: {
          entity_xid: string | null
          id: number | null
          name: string | null
          org_id: number | null
          profile_id: string | null
          quota: number | null
        }
        Relationships: []
      }
      view_quota_for_customer: {
        Row: {
          created_at: string | null
          customer: string | null
          id: number | null
          item_type: Database["public"]["Enums"]["quota_item_type"] | null
          org_id: number | null
          organisation: number | null
          period: string | null
          profile_id: string | null
          quantity: number | null
          scope: Database["public"]["Enums"]["quota_scope"] | null
        }
        Relationships: []
      }
      view_quota_for_entity_analysis: {
        Row: {
          id: number | null
          org_id: number | null
          profile_id: string | null
          quota: number | null
        }
        Relationships: []
      }
      view_quota_for_single_doc: {
        Row: {
          id: number | null
          org_id: number | null
          profile_id: string | null
          quota: number | null
        }
        Relationships: []
      }
      view_quota_used: {
        Row: {
          period: string | null
          profile_id: string | null
          quota: number | null
          scope: Database["public"]["Enums"]["quota_scope"] | null
          type: Database["public"]["Enums"]["quota_item_type"] | null
          used: number | null
        }
        Relationships: []
      }
      view_single_doc_runs: {
        Row: {
          analysis_id: number | null
          analysis_json: Json | null
          created_at: string | null
          entity_xid: string | null
          id: number | null
          name: string | null
          public_url: string | null
          quota_id: number | null
          run_by: string | null
          type: string | null
        }
        Relationships: []
      }
      view_unique_companies: {
        Row: {
          entity_xid: string | null
          id: number | null
          max_quota: number | null
          name: string | null
        }
        Relationships: []
      }
    }
    Functions: {
      binary_quantize: {
        Args: { "": string } | { "": unknown }
        Returns: unknown
      }
      cleanup_inactive_presence: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      column_exists: {
        Args: { tbl: string; col: string }
        Returns: boolean
      }
      get_latest_run_id: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      get_quota_for_current_user: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: number
          created_at: string
          item_type: Database["public"]["Enums"]["quota_item_type"]
          quantity: number
          period: string
          customer: string
          organisation: number
          scope: Database["public"]["Enums"]["quota_scope"]
          profile_id: string
          org_id: number
        }[]
      }
      gtrgm_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gtrgm_decompress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gtrgm_in: {
        Args: { "": unknown }
        Returns: unknown
      }
      gtrgm_options: {
        Args: { "": unknown }
        Returns: undefined
      }
      gtrgm_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      halfvec_avg: {
        Args: { "": number[] }
        Returns: unknown
      }
      halfvec_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      halfvec_send: {
        Args: { "": unknown }
        Returns: string
      }
      halfvec_typmod_in: {
        Args: { "": unknown[] }
        Returns: number
      }
      hnsw_bit_support: {
        Args: { "": unknown }
        Returns: unknown
      }
      hnsw_halfvec_support: {
        Args: { "": unknown }
        Returns: unknown
      }
      hnsw_sparsevec_support: {
        Args: { "": unknown }
        Returns: unknown
      }
      hnswhandler: {
        Args: { "": unknown }
        Returns: unknown
      }
      is_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      ivfflat_bit_support: {
        Args: { "": unknown }
        Returns: unknown
      }
      ivfflat_halfvec_support: {
        Args: { "": unknown }
        Returns: unknown
      }
      ivfflathandler: {
        Args: { "": unknown }
        Returns: unknown
      }
      l2_norm: {
        Args: { "": unknown } | { "": unknown }
        Returns: number
      }
      l2_normalize: {
        Args: { "": string } | { "": unknown } | { "": unknown }
        Returns: string
      }
      limit_array_size: {
        Args: { arr: unknown; max_size: number }
        Returns: unknown
      }
      name_to_ekoid: {
        Args: { input_str: string }
        Returns: string
      }
      remove_suffix: {
        Args: { company_name: string }
        Returns: string
      }
      set_limit: {
        Args: { "": number }
        Returns: number
      }
      show_limit: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      show_trgm: {
        Args: { "": string }
        Returns: string[]
      }
      sparsevec_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      sparsevec_send: {
        Args: { "": unknown }
        Returns: string
      }
      sparsevec_typmod_in: {
        Args: { "": unknown[] }
        Returns: number
      }
      to_snake_case: {
        Args: { input_text: string }
        Returns: string
      }
      vector_avg: {
        Args: { "": number[] }
        Returns: string
      }
      vector_dims: {
        Args: { "": string } | { "": unknown }
        Returns: number
      }
      vector_norm: {
        Args: { "": string }
        Returns: number
      }
      vector_out: {
        Args: { "": string }
        Returns: unknown
      }
      vector_send: {
        Args: { "": string }
        Returns: string
      }
      vector_typmod_in: {
        Args: { "": unknown[] }
        Returns: number
      }
    }
    Enums: {
      acc_message_type:
        | "info"
        | "success"
        | "billing"
        | "quota"
        | "feature"
        | "error"
      analysis_flag: "red" | "green"
      analysis_type: "security" | "company" | "fund"
      api_request_status: "pending" | "processing" | "completed" | "failed"
      audit_operation:
        | "insert"
        | "update"
        | "delete"
        | "authorize"
        | "revoke"
        | "logical_delete"
        | "pending"
      authorize_status: "approved" | "rejected" | "pending" | "deleted"
      chunk_diff: "created" | "unchanged" | "deleted" | "error" | "updated"
      doughnut_level: "social" | "ecological"
      doughnut_segment:
        | "nitrogen_phosphorus"
        | "biodiversity"
        | "chemical_pollution"
        | "air_pollution"
        | "freshwater_withdrawals"
        | "climate_change"
        | "ocean_acid"
        | "ozone"
        | "land_conversion"
        | "water"
        | "food"
        | "health"
        | "education"
        | "work"
        | "justice"
        | "voice"
        | "social_equity"
        | "gender_equality"
        | "housing"
        | "networks"
        | "energy"
        | "all"
      entity_type: "company" | "security" | "person" | "country"
      ethical_model:
        | "oecd_compact"
        | "doughnut"
        | "drawdown"
        | "sfdr"
        | "sdg"
        | "eu_taxonomy"
        | "csrd"
      flag_status: "active" | "deleted" | "removed"
      keyword_type: "google_search" | "keyword_search"
      kg_status: "active" | "deleted"
      quota_item_type:
        | "company"
        | "entity"
        | "document-analysis"
        | "entity-analysis"
      quota_scope: "org" | "individual"
      recipient_scope: "all" | "organisation" | "individual"
      research_category:
        | "impact"
        | "company_background"
        | "scientific"
        | "journalism"
        | "disclosure"
        | "regulatory"
        | "media"
      research_scope:
        | "company"
        | "city"
        | "industry"
        | "global"
        | "region"
        | "global-region"
        | "other"
        | "country-region"
        | "country"
      research_type: "fund" | "company" | "nested_fund"
      run_status:
        | "started"
        | "completed"
        | "aborted"
        | "failed"
        | "pending"
        | "deleted"
      run_type: "hist" | "comp" | "inc" | "full" | "hist-inc"
      severity: "low" | "medium" | "high"
      text_type:
        | "prose"
        | "contents_or_index"
        | "table"
        | "list"
        | "other"
        | "binary"
        | "references"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      acc_message_type: [
        "info",
        "success",
        "billing",
        "quota",
        "feature",
        "error",
      ],
      analysis_flag: ["red", "green"],
      analysis_type: ["security", "company", "fund"],
      api_request_status: ["pending", "processing", "completed", "failed"],
      audit_operation: [
        "insert",
        "update",
        "delete",
        "authorize",
        "revoke",
        "logical_delete",
        "pending",
      ],
      authorize_status: ["approved", "rejected", "pending", "deleted"],
      chunk_diff: ["created", "unchanged", "deleted", "error", "updated"],
      doughnut_level: ["social", "ecological"],
      doughnut_segment: [
        "nitrogen_phosphorus",
        "biodiversity",
        "chemical_pollution",
        "air_pollution",
        "freshwater_withdrawals",
        "climate_change",
        "ocean_acid",
        "ozone",
        "land_conversion",
        "water",
        "food",
        "health",
        "education",
        "work",
        "justice",
        "voice",
        "social_equity",
        "gender_equality",
        "housing",
        "networks",
        "energy",
        "all",
      ],
      entity_type: ["company", "security", "person", "country"],
      ethical_model: [
        "oecd_compact",
        "doughnut",
        "drawdown",
        "sfdr",
        "sdg",
        "eu_taxonomy",
        "csrd",
      ],
      flag_status: ["active", "deleted", "removed"],
      keyword_type: ["google_search", "keyword_search"],
      kg_status: ["active", "deleted"],
      quota_item_type: [
        "company",
        "entity",
        "document-analysis",
        "entity-analysis",
      ],
      quota_scope: ["org", "individual"],
      recipient_scope: ["all", "organisation", "individual"],
      research_category: [
        "impact",
        "company_background",
        "scientific",
        "journalism",
        "disclosure",
        "regulatory",
        "media",
      ],
      research_scope: [
        "company",
        "city",
        "industry",
        "global",
        "region",
        "global-region",
        "other",
        "country-region",
        "country",
      ],
      research_type: ["fund", "company", "nested_fund"],
      run_status: [
        "started",
        "completed",
        "aborted",
        "failed",
        "pending",
        "deleted",
      ],
      run_type: ["hist", "comp", "inc", "full", "hist-inc"],
      severity: ["low", "medium", "high"],
      text_type: [
        "prose",
        "contents_or_index",
        "table",
        "list",
        "other",
        "binary",
        "references",
      ],
    },
  },
} as const
