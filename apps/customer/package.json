{"name": "customer", "private": true, "scripts": {"dev": "next dev --port 3000", "build": "next build", "start": "next start", "lint": "next lint", "test": "playwright test", "test:ui": "playwright test --ui", "test:headed": "playwright test --headed", "test:unit": "jest", "test:unit:watch": "jest --watch", "test:unit:coverage": "jest --coverage"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.11", "@ai-sdk/google": "^1.2.18", "@anthropic-ai/sdk": "^0.51.0", "@aws-sdk/client-s3": "^3.812.0", "@aws-sdk/lib-storage": "^3.812.0", "@aws-sdk/s3-request-presigner": "^3.812.0", "@formkit/auto-animate": "^0.8.2", "@google/generative-ai": "^0.24.1", "@hocuspocus/provider": "^2.15.2", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-tooltip": "^1.2.6", "@radix-ui/react-visually-hidden": "^1.2.3", "@react-hookz/web": "^24.0.4", "@repo/ui": "workspace:*", "@repo/utils": "workspace:*", "@sentry/nextjs": "^8.55.0", "@supabase/ssr": "latest", "@supabase/supabase-js": "latest", "@tailwindcss/typography": "^0.5.16", "@tiptap-extend/columns": "^2.1.6", "@tiptap/core": "^2.12.0", "@tiptap/extension-collaboration": "^2.12.0", "@tiptap/extension-collaboration-cursor": "^2.12.0", "@tiptap/extension-color": "^2.12.0", "@tiptap/extension-dropcursor": "^2.12.0", "@tiptap/extension-focus": "^2.12.0", "@tiptap/extension-font-family": "^2.12.0", "@tiptap/extension-gapcursor": "^2.12.0", "@tiptap/extension-highlight": "^2.12.0", "@tiptap/extension-history": "^2.12.0", "@tiptap/extension-image": "^2.12.0", "@tiptap/extension-link": "^2.12.0", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/extension-subscript": "^2.12.0", "@tiptap/extension-superscript": "^2.12.0", "@tiptap/extension-table": "^2.12.0", "@tiptap/extension-table-cell": "^2.12.0", "@tiptap/extension-table-header": "^2.12.0", "@tiptap/extension-table-row": "^2.12.0", "@tiptap/extension-task-item": "^2.12.0", "@tiptap/extension-task-list": "^2.12.0", "@tiptap/extension-text-align": "^2.12.0", "@tiptap/extension-text-style": "^2.12.0", "@tiptap/extension-typography": "^2.12.0", "@tiptap/extension-underline": "^2.12.0", "@tiptap/html": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "@tiptap/suggestion": "^2.12.0", "@types/d3": "^7.4.3", "@types/jsonwebtoken": "^9.0.9", "@types/react": "npm:types-react@19.0.1", "@types/react-dom": "npm:types-react-dom@19.0.2", "@types/uuid": "^10.0.0", "@vercel/blob": "^1.1.1", "@vercel/kv": "^2.0.0", "@xstate/react": "^6.0.0", "ai": "^4.3.16", "autoprefixer": "10.4.17", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "d3": "^7.9.0", "d3plus-react": "^1.3.3", "d3plus-text": "^1.2.5", "date-fns": "^4.1.0", "debounce": "^2.2.0", "docx": "^8.5.0", "dotenv": "^16.5.0", "echarts-for-react": "^3.0.2", "emoji-picker-react": "^4.12.0", "eslint-config-next": "15.3.2", "fast-json-patch": "^3.1.1", "file-saver": "^2.0.5", "framer-motion": "^12.12.1", "geist": "^1.4.2", "import-in-the-middle": "^1.13.2", "js-base64": "^3.7.7", "jsonrepair": "^3.12.0", "jsonwebtoken": "^9.0.2", "katex": "^0.16.11", "lru-cache": "^11.1.0", "lucide-react": "^0.460.0", "mammoth": "^1.8.0", "mdast-util-gfm-table": "^2.0.0", "mdast-util-to-string": "^4.0.0", "micromark-extension-gfm-table": "^2.1.1", "next": "^15.3.2", "nuqs": "^2.4.3", "openai": "^4.100.0", "papaparse": "^5.5.2", "pizzip": "^3.1.7", "postcss": "8.4.33", "postcss-import": "^16.1.1", "prop-types": "^15.8.1", "prosemirror-docx": "^0.4.1", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.39.3", "react": "19.0.0", "react-avatar-editor": "^13.0.2", "react-dom": "19.0.0", "react-dropzone": "^14.3.8", "react-force-graph-3d": "^1.26.1", "react-hook-form": "^7.56.4", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "react-to-print": "^3.1.0", "recharts": "^2.15.3", "rehype-raw": "^7.0.0", "rehype-stringify": "^10.0.1", "remark-gfm": "^4.0.1", "remark-parse": "^11.0.0", "remark-rehype": "^11.1.2", "require-in-the-middle": "^7.5.2", "sdk:latest": "link:@anthropic-ai/sdk:latest", "sonner": "^1.7.4", "swr": "^2.3.3", "tailwind-merge": "^2.6.0", "tailwindcss": "3.4.1", "tailwindcss-animate": "^1.0.7", "tailwindcss-textshadow": "^2.1.3", "tippy.js": "^6.3.7", "tiptap-extension-auto-joiner": "^0.1.3", "tiptap-extension-global-drag-handle": "^0.1.18", "tiptap-markdown": "^0.8.10", "typescript": "^5.8.3", "unified": "^11.0.5", "unist-util-visit": "^5.0.0", "usehooks-ts": "^3.1.1", "uuid": "^10.0.0", "xstate": "^5.20.0", "y-prosemirror": "^1.3.5", "y-protocols": "^1.0.6", "y-websocket": "^3.0.0", "yjs": "^13.6.27"}, "devDependencies": {"@playwright/test": "^1.52.0", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/file-saver": "^2.0.7", "@types/jest": "^30.0.0", "@types/node": "20.11.5", "@types/react-avatar-editor": "^13.0.4", "@types/react-syntax-highlighter": "^15.5.13", "encoding": "^0.1.13", "jest": "^30.0.2", "jest-environment-jsdom": "^30.0.2", "next": "15.1.0", "postcss-nesting": "^13.0.1", "ts-jest": "^29.4.0", "turbo": "latest"}, "overrides": {"react": "19.0.0", "react-dom": "19.0.0", "@types/react": "npm:types-react@19.0.1", "@types/react-dom": "npm:types-react-dom@19.0.2"}, "pnpm": {"overrides": {"@types/react": "19.0.1", "@types/react-dom": "19.0.2"}}}