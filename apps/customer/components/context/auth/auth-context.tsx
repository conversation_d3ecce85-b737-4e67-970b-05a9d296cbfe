'use client'

import { createContext, ReactNode, useContext, useEffect, useState } from 'react'
import { User } from '@supabase/supabase-js'
import { createClient } from '@/app/supabase/client'
import { ProfileType } from '@/types'
import { usePathname, useRouter } from 'next/navigation'
import { DEFAULT_FEATURE_FLAGS, FeatureFlagConfig, hasFeature, normalizeFlags } from '@/utils/feature-flags'

interface AuthContextType {
  user: User | null;
  admin: boolean;
  profile: ProfileType | null;
  hasFeature: (flagName: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

interface UserProviderProps {
  children: ReactNode;
}

export const AuthProvider = ({ children }: UserProviderProps) => {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<ProfileType | null>(null)
  const [isAdmin, setIsAdmin] = useState<boolean>(false)
  const [orgFeatureFlags, setOrgFeatureFlags] = useState<string[]>([])
  const supabase = createClient()
  const router = useRouter()
  const pathname = usePathname()

  const fetchProfile = async (user: User | null) => {
    console.log('fetching profile for', user)
    if (user) {
      const profileResponse = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single()

      if (profileResponse.data) {
        // Set admin status from database
        setIsAdmin(!!profileResponse.data.is_admin)
        
        if (profileResponse.data.avatar_url) {
          const avatarData = await supabase.storage
            .from('avatars')
            .createSignedUrl(profileResponse.data.avatar_url, 3600)
          setProfile({
            ...profileResponse.data,
            avatar_url: avatarData.data?.signedUrl ?? '',
          })
        } else {
          setProfile(profileResponse.data)
        }

        // Fetch organisation feature flags if user has an organisation
        if (profileResponse.data.organisation) {
          const orgResponse = await supabase
            .from('acc_organisations')
            .select('feature_flags')
            .eq('id', profileResponse.data.organisation)
            .single()

          if (orgResponse.data) {
            setOrgFeatureFlags(normalizeFlags(orgResponse.data.feature_flags))
            console.log('org_flags', orgResponse.data.feature_flags)
          } else {
            console.log('no org_flags', orgResponse)
          }
        } else {
          console.log('no org')
        }
      } else {
        setProfile(null)
        setIsAdmin(false)
        setOrgFeatureFlags([])
        console.log('no profile')
      }
    } else {
      setProfile(null)
      setIsAdmin(false)
      setOrgFeatureFlags([])
      console.log('no user')
    }
  }

  const getUserProfile = async () => {
    const { data, error } = await supabase.auth.getUser()
    const currentUser = data?.user
    console.log('current user', currentUser)
    if (error || !currentUser) {
      console.error('error getting user', error)
      setUser(null)
      setProfile(null)
      setIsAdmin(false)
      if (!window.location.pathname.startsWith('/login')) {
        router.push(`/login?next=${encodeURIComponent(window.location.href)}`)
      }
      return
    }

    setUser(currentUser)
    fetchProfile(currentUser)
  }

  const checkFeature = (flagName: string): boolean => {
    const config: FeatureFlagConfig = {
      userFlags: normalizeFlags(profile?.feature_flags),
      orgFlags: orgFeatureFlags,
      defaultFlags: DEFAULT_FEATURE_FLAGS,
    }
    return hasFeature(flagName, config)
  }

  useEffect(() => {
    // Listen for auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      console.info('Auth state changed')
      const currentUser = session?.user || null
      setUser(currentUser)
      fetchProfile(currentUser) // Fetch only if the user changes
    })

    // Initial user check
    getUserProfile()

    return () => {
      subscription.unsubscribe()
    }
  }, []) // Remove the pathname dependency to prevent infinite loops

  return (
    <AuthContext.Provider
      value={{
        user,
        profile,
        admin: isAdmin,
        hasFeature: checkFeature,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within a UserProvider')
  }
  return context
}
