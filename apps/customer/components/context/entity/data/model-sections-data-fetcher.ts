import { ModelSectionType } from '@/types'
import { BaseDataFetcher, DataFetcherDependencies } from './base-data-fetcher'

export class ModelSectionsDataFetcher extends BaseDataFetcher<ModelSectionType[]> {
    constructor(
        currentRequestRefs: Map<string, string>,
        abortControllersRef: Map<string, AbortController>
    ) {
        super('modelSections', currentRequestRefs, abortControllersRef);
    }

    validateDependencies(dependencies: DataFetcherDependencies): boolean {
        const { model } = dependencies;
        return !!model;
    }

    async fetch(
        dependencies: DataFetcherDependencies,
        setData: (data: ModelSectionType[] | null) => void,
        setLoading: (loading: boolean) => void
    ): Promise<void> {
        const { model } = dependencies;

        if (!this.validateDependencies(dependencies)) {
            setData(null);
            return;
        }

        const { requestId, abortController, isStale } = this.setupRequestCancellation(dependencies);
        setLoading(true);

        try {
            const { data: modelSectionsData, error: modelSectionsError } = await this.supabase
              .from('xfer_model_sections')
                .select('*')
                .eq('model', model!)
              .order('id', { ascending: false })
                .abortSignal(abortController.signal);

            // Check if this request is still current before processing results
            if (isStale()) {
                this.logCancellation();
                return;
            }

            if (modelSectionsError) {
                this.logError(modelSectionsError, 'fetchModelSectionsData');
                if (!isStale()) {
                    setData([]);
                }
                return;
            }

            if (!isStale()) {
                setData((modelSectionsData as unknown as ModelSectionType[]) || []);
                this.logSuccess("Loaded model sections data", modelSectionsData?.length || 0);
            }
        } catch (error) {
            if (this.handleAbortError(error)) {
                return;
            }
            this.logError(error, 'fetchModelSectionsData');
            if (!isStale()) {
                setData([]);
            }
        } finally {
            if (!isStale()) {
                setLoading(false);
            }
        }
    }
}
