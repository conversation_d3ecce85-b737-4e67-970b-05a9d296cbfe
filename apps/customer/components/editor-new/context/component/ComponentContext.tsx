import React, { createContext, useContext, useReducer, useRef, useCallback, useMemo } from 'react'
import { ReportComponent, ComponentUpdate, ComponentOperations } from '../../types'
import { componentStateMachine, logger, COMPONENT_STATUS, TimeoutRegistry, TIMEOUT_DELAYS } from '../../services'
import type { ComponentStatus } from '../../services'

interface ComponentState {
  components: Map<string, ReportComponent>
  orphanedComponents: Map<string, ReportComponent>
  componentHierarchy: Map<string, string[]> // parentId -> childIds
}

type ComponentAction =
  | { type: 'REGISTER_COMPONENT'; payload: ReportComponent }
  | { type: 'UPDATE_COMPONENT'; payload: ComponentUpdate }
  | { type: 'REMOVE_COMPONENT'; payload: { id: string } }
  | { type: 'CLEAR_ORPHANED'; payload: { id: string } }
  | { type: 'RESET_COMPONENTS' }

const initialState: ComponentState = {
  components: new Map(),
  orphanedComponents: new Map(),
  componentHierarchy: new Map()
}

function componentReducer(state: ComponentState, action: ComponentAction): ComponentState {
  switch (action.type) {
    case 'REGISTER_COMPONENT': {
      const component = action.payload
      const newComponents = new Map(state.components)
      const newHierarchy = new Map(state.componentHierarchy)
      const newOrphaned = new Map(state.orphanedComponents)
      
      // Add component
      newComponents.set(component.id, component)
      
      // Remove from orphaned if it was there
      newOrphaned.delete(component.id)
      
      // Update hierarchy
      if (component.parentId) {
        const siblings = newHierarchy.get(component.parentId) || []
        if (!siblings.includes(component.id)) {
          newHierarchy.set(component.parentId, [...siblings, component.id])
        }
      }
      
      return {
        ...state,
        components: newComponents,
        orphanedComponents: newOrphaned,
        componentHierarchy: newHierarchy
      }
    }
    
    case 'UPDATE_COMPONENT': {
      const { id, ...updates } = action.payload
      const existing = state.components.get(id)
      
      if (!existing) {
        logger.warn('ComponentContext', 'UPDATE_COMPONENT', `Component ${id} not found`)
        return state
      }
      
      const updated = { ...existing, ...updates, lastUpdated: new Date() }
      const newComponents = new Map(state.components)
      newComponents.set(id, updated)
      
      return {
        ...state,
        components: newComponents
      }
    }
    
    case 'REMOVE_COMPONENT': {
      const { id } = action.payload
      const newComponents = new Map(state.components)
      const newHierarchy = new Map(state.componentHierarchy)
      
      // Remove component
      newComponents.delete(id)
      
      // Remove from hierarchy
      const component = state.components.get(id)
      if (component?.parentId) {
        const siblings = newHierarchy.get(component.parentId) || []
        newHierarchy.set(component.parentId, siblings.filter(sid => sid !== id))
      }
      
      // Remove as parent
      newHierarchy.delete(id)
      
      return {
        ...state,
        components: newComponents,
        componentHierarchy: newHierarchy
      }
    }
    
    case 'CLEAR_ORPHANED': {
      const newOrphaned = new Map(state.orphanedComponents)
      newOrphaned.delete(action.payload.id)
      return {
        ...state,
        orphanedComponents: newOrphaned
      }
    }
    
    case 'RESET_COMPONENTS': {
      return initialState
    }
    
    default:
      return state
  }
}

interface ComponentContextValue extends ComponentOperations {
  components: Map<string, ReportComponent>
  orphanedComponents: Map<string, ReportComponent>
  componentHierarchy: Map<string, string[]>
  getComponentsByStatus: (status: ComponentStatus) => ReportComponent[]
  getComponentTree: (rootId?: string) => ReportComponent[]
  areAllComponentsInFinalState: () => boolean
}

const ComponentContext = createContext<ComponentContextValue | undefined>(undefined)

interface ComponentProviderProps {
  children: React.ReactNode
  onComponentStatusChange?: (componentId: string, status: ComponentStatus) => void
}

export function ComponentProvider({ children, onComponentStatusChange }: ComponentProviderProps) {
  const [state, dispatch] = useReducer(componentReducer, initialState)
  const timeoutRegistry = useRef(new TimeoutRegistry())
  
  // Cleanup timeouts on unmount
  React.useEffect(() => {
    return () => {
      timeoutRegistry.current.clearAll()
    }
  }, [])

  const registerComponent = useCallback((component: ReportComponent) => {
    logger.info('ComponentProvider', 'registerComponent', `Registering component ${component.id} of type ${component.type}`)
    
    // Validate component before registration
    if (!component.id || !component.type) {
      logger.error('ComponentProvider', 'registerComponent', 'Component missing required fields')
      return
    }
    
    // Check if parent exists (if parentId is specified)
    if (component.parentId && !state.components.has(component.parentId)) {
      logger.warn('ComponentProvider', 'registerComponent', `Parent ${component.parentId} not found for component ${component.id}`)
      // Could add to orphaned components here if needed
    }
    
    // Use state machine to determine initial status
    const initialStatus = componentStateMachine.determineNextStatus(
      COMPONENT_STATUS.UNREGISTERED,
      {
        componentId: component.id,
        componentType: component.type,
        dependencies: component.dependencies
      }
    )
    
    const componentWithStatus = {
      ...component,
      status: initialStatus,
      lastUpdated: new Date()
    }
    
    dispatch({ type: 'REGISTER_COMPONENT', payload: componentWithStatus })
    
    // Notify of status change
    if (onComponentStatusChange) {
      onComponentStatusChange(component.id, initialStatus)
    }
  }, [state.components, onComponentStatusChange])

  const updateComponent = useCallback((update: ComponentUpdate) => {
    const existing = state.components.get(update.id)
    if (!existing) {
      logger.warn('ComponentProvider', 'updateComponent', `Component ${update.id} not found`)
      return
    }
    
    // Validate status transition if status is being updated
    if (update.status && update.status !== existing.status) {
      const canTransition = componentStateMachine.canTransition(
        existing.status,
        update.status,
        {
          componentId: existing.id,
          componentType: existing.type,
          dependencies: existing.dependencies,
          hasError: Boolean(update.error)
        }
      )
      
      if (!canTransition) {
        logger.error('ComponentProvider', 'updateComponent', 
          `Invalid status transition from ${existing.status} to ${update.status} for component ${update.id}`)
        return
      }
    }
    
    logger.debug('ComponentProvider', 'updateComponent', `Updating component ${update.id}`)
    dispatch({ type: 'UPDATE_COMPONENT', payload: update })
    
    // Notify of status change
    if (update.status && update.status !== existing.status && onComponentStatusChange) {
      onComponentStatusChange(update.id, update.status)
    }
  }, [state.components, onComponentStatusChange])

  const removeComponent = useCallback((id: string) => {
    logger.info('ComponentProvider', 'removeComponent', `Removing component ${id}`)
    dispatch({ type: 'REMOVE_COMPONENT', payload: { id } })
  }, [])

  const getComponent = useCallback((id: string) => {
    return state.components.get(id)
  }, [state.components])

  const getComponentsByType = useCallback((type: string) => {
    return Array.from(state.components.values()).filter(c => c.type === type)
  }, [state.components])

  const getComponentsByParent = useCallback((parentId: string) => {
    const childIds = state.componentHierarchy.get(parentId) || []
    return childIds.map(id => state.components.get(id)).filter(Boolean) as ReportComponent[]
  }, [state.components, state.componentHierarchy])

  const getComponentsByStatus = useCallback((status: ComponentStatus) => {
    return Array.from(state.components.values()).filter(c => c.status === status)
  }, [state.components])

  const getComponentTree = useCallback((rootId?: string) => {
    const buildTree = (parentId?: string): ReportComponent[] => {
      const children = getComponentsByParent(parentId || '')
      return children.map(child => ({
        ...child,
        children: state.componentHierarchy.has(child.id) 
          ? buildTree(child.id).map(c => c.id)
          : undefined
      }))
    }
    
    return buildTree(rootId)
  }, [state.componentHierarchy, getComponentsByParent])

  const areAllComponentsInFinalState = useCallback(() => {
    return Array.from(state.components.values()).every(component => 
      componentStateMachine.isFinalState(component.status)
    )
  }, [state.components])

  const contextValue = useMemo((): ComponentContextValue => ({
    components: state.components,
    orphanedComponents: state.orphanedComponents,
    componentHierarchy: state.componentHierarchy,
    registerComponent,
    updateComponent,
    removeComponent,
    getComponent,
    getComponentsByType,
    getComponentsByParent,
    getComponentsByStatus,
    getComponentTree,
    areAllComponentsInFinalState
  }), [
    state.components,
    state.orphanedComponents,
    state.componentHierarchy,
    registerComponent,
    updateComponent,
    removeComponent,
    getComponent,
    getComponentsByType,
    getComponentsByParent,
    getComponentsByStatus,
    getComponentTree,
    areAllComponentsInFinalState
  ])

  return (
    <ComponentContext.Provider value={contextValue}>
      {children}
    </ComponentContext.Provider>
  )
}

export function useComponents(): ComponentContextValue {
  const context = useContext(ComponentContext)
  if (!context) {
    throw new Error('useComponents must be used within a ComponentProvider')
  }
  return context
}