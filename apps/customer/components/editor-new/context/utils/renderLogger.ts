/**
 * Utility for conditional React render logging based on feature flags
 */
import { DEFAULT_FEATURE_FLAGS, hasFeature, type FeatureFlagConfig } from '@/utils/feature-flags'

// Function to check if the trace.react feature flag is enabled
const checkTraceReactFlag = (): boolean => {
  // Check if we're in browser environment
  if (typeof window === 'undefined') {
    return false;
  }

  // Check localStorage for development override first
  const devOverride = localStorage.getItem('trace.react');
  if (devOverride === 'true') {
    return true;
  }
  if (devOverride === 'false') {
    return false;
  }
  
  // Check URL parameters for quick enable/disable
  const urlParams = new URLSearchParams(window.location.search);
  if (urlParams.get('trace') === 'react') {
    localStorage.setItem('trace.react', 'true');
    return true;
  }

  // Fallback to default feature flags (since we can't access auth context from here)
  const config: FeatureFlagConfig = {
    userFlags: [],
    orgFlags: [],
    defaultFlags: DEFAULT_FEATURE_FLAGS,
  };
  
  return hasFeature('trace.react', config);
};

// Allow runtime toggling from console
if (typeof window !== 'undefined') {
  (window as any).enableRenderTracing = () => {
    localStorage.setItem('trace.react', 'true');
    console.log('🔍 React render tracing enabled');
    console.log('💡 To disable: disableRenderTracing() or localStorage.setItem("trace.react", "false")');
  };
  
  (window as any).disableRenderTracing = () => {
    localStorage.setItem('trace.react', 'false');
    console.log('🔇 React render tracing disabled');
  };
  
  // Add helpful console info on load
  console.log('🛠️  Render tracing controls available:');
  console.log('  • enableRenderTracing() - Enable detailed render logs');
  console.log('  • disableRenderTracing() - Disable render logs');
  console.log('  • Add ?trace=react to URL for quick enable');
}

export const renderLog = (message: string, ...args: any[]) => {
  if (checkTraceReactFlag()) {
    console.log(message, ...args);
  }
};