import { useRef, useEffect, useCallback } from 'react'
import { Editor } from '@tiptap/react'
import { ReportComponent, DocumentAction } from '../DocumentContext'

interface DocumentInitializationProps {
  editor: Editor | null
  hasTriggeredInitialLoad: boolean
  isInitialized: boolean
  dispatch: React.Dispatch<DocumentAction>
  stateRef: React.MutableRefObject<{ components: Map<string, ReportComponent> }>
}

export const useDocumentInitialization = ({
  editor,
  hasTriggeredInitialLoad,
  isInitialized,
  dispatch,
  stateRef
}: DocumentInitializationProps) => {
  const initializationTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const INITIALIZATION_DELAY = 1000 // 1 second delay

  // Document initialization detection and auto-trigger
  useEffect(() => {
    // Only proceed if we have an editor and haven't triggered initial load yet
    if (!editor || hasTriggeredInitialLoad) {
      return
    }

    // Clear any existing timeout
    if (initializationTimeoutRef.current) {
      clearTimeout(initializationTimeoutRef.current)
    }

    // Set a timeout to detect when component registration is complete
    initializationTimeoutRef.current = setTimeout(() => {
      console.log('DocumentInitialization: Initialization timeout reached, checking for idle components')

      // Mark document as initialized
      if (!isInitialized) {
        console.log('DocumentInitialization: Marking document as initialized')
        dispatch({ type: 'DOCUMENT_INITIALIZED' })
      }

      // Find all idle components that should be auto-triggered
      const idleComponents = Array.from(stateRef.current.components.values()).filter(
        (component) =>
          (component.status === 'idle' || component.status === 'error') &&
          component.type === 'report-section' && // Only auto-trigger sections
          !component.preserved &&
          !component.locked
      )

      if (idleComponents.length > 0) {
        console.log(
          `DocumentInitialization: Found ${idleComponents.length} idle report sections to auto-trigger:`,
          idleComponents.map(c => c.id)
        )

        // Mark that we've triggered initial load
        dispatch({ type: 'INITIAL_LOAD_TRIGGERED' })

        // Reset dependent groups and summaries from error to loading
        setTimeout(() => {
          idleComponents.forEach((section) => {
            // Find groups that contain this section and reset them
            const parentGroups = Array.from(stateRef.current.components.values()).filter(
              (comp) => comp.type === 'report-group' && (
                comp.children?.includes(section.id) || 
                section.parentId === comp.id ||
                section.id.startsWith(comp.id + '-')
              )
            )

            parentGroups.forEach((group) => {
              if (group.status === 'error') {
                console.log(`DocumentInitialization: Resetting parent group ${group.id} from error to loading`)
                dispatch({
                  type: 'COMPONENT_UPDATED',
                  id: group.id,
                  updates: {
                    status: 'loading',
                    error: undefined
                  }
                })
              }
            })

            // Find summaries that depend on this section and reset them
            const dependentSummaries = Array.from(stateRef.current.components.values()).filter(
              (comp) => comp.type === 'report-summary' && (
                comp.dependencies?.includes(section.id) ||
                comp.dependencies?.includes(section.parentId || '') ||
                (section.parentId && comp.dependencies?.includes(section.parentId))
              )
            )

            dependentSummaries.forEach((summary) => {
              if (summary.status === 'error') {
                console.log(`DocumentInitialization: Resetting dependent summary ${summary.id} from error to loading`)
                dispatch({
                  type: 'COMPONENT_UPDATED',
                  id: summary.id,
                  updates: {
                    status: 'loading',
                    error: undefined
                  }
                })
              }
            })
          })
        }, 50)

        // Trigger loading for idle sections with delay between each
        idleComponents.forEach((component, index) => {
          setTimeout(() => {
            console.log(`DocumentInitialization: Auto-triggering load for idle component ${component.id}`)
            dispatch({ type: 'COMPONENT_UPDATED', id: component.id, updates: {} })
          }, index * 100)
        })
      } else {
        console.log('DocumentInitialization: No idle report sections found to auto-trigger')
        dispatch({ type: 'INITIAL_LOAD_TRIGGERED' })
      }

      // IMPORTANT: Also check for error groups that have all children loaded
      // This handles the case where a group is stuck in error state but all its children are loaded
      setTimeout(() => {
        const errorGroups = Array.from(stateRef.current.components.values()).filter(
          (comp) => comp.type === 'report-group' && comp.status === 'error'
        )

        errorGroups.forEach((group) => {
          // Get all descendants of this group
          const descendants = getAllDescendants(group.id)
          
          // Check if all descendants are in a final successful state
          const allDescendantsLoaded = descendants.every(
            (desc) => 
              desc.status === 'loaded' || 
              desc.status === 'preserved' || 
              desc.status === 'locked'
          )

          if (allDescendantsLoaded && descendants.length > 0) {
            console.log(
              `DocumentInitialization: Group ${group.id} is in error state but all ${descendants.length} descendants are loaded. Triggering status update.`
            )
            
            // Trigger an immediate group status update
            dispatch({
              type: 'COMPONENT_UPDATED',
              id: group.id,
              updates: { lastRefreshed: new Date() } // Trigger a re-evaluation
            })
          }
        })
      }, INITIALIZATION_DELAY + 500) // Run after initial load trigger

      // Helper function to get all descendants
      const getAllDescendants = (groupId: string): ReportComponent[] => {
        const directChildren = Array.from(stateRef.current.components.values()).filter(
          (component) => component.parentId === groupId
        )
        const allDescendants: ReportComponent[] = [...directChildren]

        directChildren.forEach((child) => {
          if (child.type === 'report-group') {
            allDescendants.push(...getAllDescendants(child.id))
          }
        })

        return allDescendants
      }
    }, INITIALIZATION_DELAY)

    return () => {
      if (initializationTimeoutRef.current) {
        clearTimeout(initializationTimeoutRef.current)
        initializationTimeoutRef.current = null
      }
    }
  }, [editor, hasTriggeredInitialLoad, isInitialized, dispatch, stateRef])

  // Manual trigger for initial load
  const triggerInitialLoad = useCallback(() => {
    console.log('DocumentInitialization.triggerInitialLoad: Manually triggering initial load')

    const idleComponents = Array.from(stateRef.current.components.values()).filter(
      (component) =>
        component.status === 'idle' &&
        component.type === 'report-section'
    )

    if (idleComponents.length > 0) {
      console.log(
        `DocumentInitialization.triggerInitialLoad: Found ${idleComponents.length} idle report sections to trigger:`,
        idleComponents.map(c => c.id)
      )

      dispatch({ type: 'INITIAL_LOAD_TRIGGERED' })

      idleComponents.forEach((component, index) => {
        setTimeout(() => {
          console.log(`DocumentInitialization.triggerInitialLoad: Triggering load for component ${component.id}`)
          dispatch({ type: 'COMPONENT_UPDATED', id: component.id, updates: {} })
        }, index * 100)
      })
    } else {
      console.log('DocumentInitialization.triggerInitialLoad: No idle report sections found')
    }
  }, [stateRef, dispatch])

  // Cleanup function
  const cleanup = useCallback(() => {
    if (initializationTimeoutRef.current) {
      clearTimeout(initializationTimeoutRef.current)
      initializationTimeoutRef.current = null
    }
  }, [])

  return {
    triggerInitialLoad,
    cleanup
  }
}
