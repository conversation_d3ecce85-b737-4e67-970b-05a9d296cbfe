import React, { useCallback, useRef } from 'react'
import { ComponentProvider, useComponents } from '../component/ComponentContext'
import { DocumentProvider } from '../DocumentContext'
import { VersioningProvider } from '../versioning/VersioningContext'
import { DependencyProvider, useDependencies } from '../dependency/DependencyContext'
import { logger, GroupStatusManager, memoryManager } from '../../services'
import type { ComponentStatus } from '../../services'

interface EditorProviderProps {
  children: React.ReactNode
  reportId?: string
  onSave?: (content: string, data?: any) => Promise<void> | void
  onComponentStatusChange?: (componentId: string, status: ComponentStatus) => void
  onGroupStatusChange?: (groupId: string, status: string) => void
}

export function EditorProvider({ 
  children, 
  reportId, 
  onSave,
  onComponentStatusChange,
  onGroupStatusChange
}: EditorProviderProps) {
  const groupManagerRef = useRef<GroupStatusManager | null>(null)
  const scopedMemoryRef = useRef<ReturnType<typeof memoryManager.createScope> | null>(null)

  // Initialize group manager
  if (!groupManagerRef.current) {
    groupManagerRef.current = new GroupStatusManager({
      onGroupStatusChange: (groupId, status) => {
        logger.debug('EditorProvider', 'onGroupStatusChange', 
          `Group ${groupId} status changed to ${status}`)
        onGroupStatusChange?.(groupId, status)
      }
    })
  }

  // Initialize scoped memory
  if (!scopedMemoryRef.current) {
    scopedMemoryRef.current = memoryManager.createScope(`editor-${reportId || 'new'}`)
  }

  // Cleanup on unmount
  React.useEffect(() => {
    const scopedMemory = scopedMemoryRef.current
    const groupManager = groupManagerRef.current

    return () => {
      logger.info('EditorProvider', 'cleanup', `Cleaning up editor for report ${reportId || 'new'}`)
      
      // Clean up scoped memory
      scopedMemory?.cleanupAll()
      
      // Clear group manager cache
      groupManager?.clearCache()
    }
  }, [reportId])

  logger.info('EditorProvider', 'render', `Initializing editor for report ${reportId || 'new'}`)

  // Create a component status getter for the dependency provider
  const getComponentStatus = useCallback((componentId: string): string | undefined => {
    // This will be properly implemented when we integrate with ComponentContext
    return undefined
  }, [])

  return (
    <DocumentProvider documentId={reportId || ''} onSave={onSave}>
      <ComponentProvider onComponentStatusChange={onComponentStatusChange}>
        <DependencyProvider getComponentStatus={getComponentStatus}>
          <VersioningProvider 
            documentId={reportId || null} 
            editor={null} // Will be set by DocumentProvider
            onContentRestore={(content, data) => {
              // This will be handled by the DocumentProvider
              logger.info('EditorProvider', 'onContentRestore', 'Content restored from version')
            }}
          >
            <EditorProviderInner groupManager={groupManagerRef.current} scopedMemory={scopedMemoryRef.current}>
              {children}
            </EditorProviderInner>
          </VersioningProvider>
        </DependencyProvider>
      </ComponentProvider>
    </DocumentProvider>
  )
}

// Inner provider to access all contexts
interface EditorProviderInnerProps {
  children: React.ReactNode
  groupManager: GroupStatusManager
  scopedMemory: ReturnType<typeof memoryManager.createScope>
}

function EditorProviderInner({ children, groupManager, scopedMemory }: EditorProviderInnerProps) {
  const { components, componentHierarchy } = useComponents()
  const { notifyDependencyResolved } = useDependencies()
  
  // Connect component updates to dependency resolution
  React.useEffect(() => {
    const handleComponentUpdate = (componentId: string, status: ComponentStatus) => {
      // Notify dependency system when component becomes ready
      if (['loaded', 'preserved', 'locked'].includes(status)) {
        notifyDependencyResolved(componentId)
      }
      
      // Queue group status updates for affected groups
      const affectedGroups = groupManager.getAffectedGroups(
        componentId, 
        components, 
        componentHierarchy
      )
      
      if (affectedGroups.length > 0) {
        groupManager.queueGroupUpdates(affectedGroups)
      }
    }
    
    // This would need to be properly connected to component status changes
    // For now, it's a placeholder for the integration
    
    return () => {
      // Cleanup if needed
    }
  }, [components, componentHierarchy, groupManager, notifyDependencyResolved])
  
  // Provide enhanced context to children
  return (
    <EditorEnhancedContext.Provider value={{ groupManager, scopedMemory }}>
      {children}
    </EditorEnhancedContext.Provider>
  )
}

// Enhanced context for additional utilities
interface EditorEnhancedContextValue {
  groupManager: GroupStatusManager
  scopedMemory: ReturnType<typeof memoryManager.createScope>
}

const EditorEnhancedContext = React.createContext<EditorEnhancedContextValue | undefined>(undefined)

export function useEditorEnhanced() {
  const context = React.useContext(EditorEnhancedContext)
  if (!context) {
    throw new Error('useEditorEnhanced must be used within EditorProvider')
  }
  return context
}

// Re-export hooks for convenience
export { useComponents } from '../component/ComponentContext'
export { useDocumentContext as useDocument } from '../DocumentContext'
export { useVersioning } from '../versioning/VersioningContext'
export { useDependencies } from '../dependency/DependencyContext'