import React, { create<PERSON>ontext, useContext, useReducer, use<PERSON><PERSON>back, useMemo, useRef } from 'react'
import { DependencyOperations } from '../../types'
import { logger, TIMEOUT_DELAYS } from '../../services'

interface DependencyWaiter {
  componentId: string
  dependencies: string[]
  resolve: () => void
  reject: (error: Error) => void
  timeoutId: NodeJS.Timeout
}

interface DependencyState {
  // Dependency graph: componentId -> dependencies
  dependencyGraph: Map<string, Set<string>>
  // Reverse dependency graph: componentId -> dependents
  dependentsGraph: Map<string, Set<string>>
  // Active waiters
  waiters: Map<string, DependencyWaiter[]>
  // Circular dependency detection
  circularDependencies: Set<string>
}

type DependencyAction =
  | { type: 'REGISTER_DEPENDENCIES'; payload: { componentId: string; dependencies: string[] } }
  | { type: 'REMOVE_DEPENDENCIES'; payload: { componentId: string } }
  | { type: 'ADD_WAITER'; payload: DependencyWaiter }
  | { type: 'REMOVE_WAITER'; payload: { waiterId: string } }
  | { type: 'RESOLVE_WAITERS'; payload: { componentId: string } }
  | { type: 'ADD_CIRCULAR_DEPENDENCY'; payload: string }
  | { type: 'RESET_DEPENDENCIES' }

const initialState: DependencyState = {
  dependencyGraph: new Map(),
  dependentsGraph: new Map(),
  waiters: new Map(),
  circularDependencies: new Set()
}

function dependencyReducer(state: DependencyState, action: DependencyAction): DependencyState {
  switch (action.type) {
    case 'REGISTER_DEPENDENCIES': {
      const { componentId, dependencies } = action.payload
      const newDepGraph = new Map(state.dependencyGraph)
      const newDependentsGraph = new Map(state.dependentsGraph)
      
      // Add to dependency graph
      newDepGraph.set(componentId, new Set(dependencies))
      
      // Update reverse dependency graph
      dependencies.forEach(depId => {
        const dependents = newDependentsGraph.get(depId) || new Set()
        dependents.add(componentId)
        newDependentsGraph.set(depId, dependents)
      })
      
      return {
        ...state,
        dependencyGraph: newDepGraph,
        dependentsGraph: newDependentsGraph
      }
    }
    
    case 'REMOVE_DEPENDENCIES': {
      const { componentId } = action.payload
      const newDepGraph = new Map(state.dependencyGraph)
      const newDependentsGraph = new Map(state.dependentsGraph)
      
      // Get current dependencies
      const dependencies = state.dependencyGraph.get(componentId) || new Set()
      
      // Remove from dependency graph
      newDepGraph.delete(componentId)
      
      // Update reverse dependency graph
      dependencies.forEach(depId => {
        const dependents = newDependentsGraph.get(depId)
        if (dependents) {
          dependents.delete(componentId)
          if (dependents.size === 0) {
            newDependentsGraph.delete(depId)
          } else {
            newDependentsGraph.set(depId, dependents)
          }
        }
      })
      
      return {
        ...state,
        dependencyGraph: newDepGraph,
        dependentsGraph: newDependentsGraph
      }
    }
    
    case 'ADD_WAITER': {
      const waiter = action.payload
      const newWaiters = new Map(state.waiters)
      
      waiter.dependencies.forEach(depId => {
        const waitersForDep = newWaiters.get(depId) || []
        waitersForDep.push(waiter)
        newWaiters.set(depId, waitersForDep)
      })
      
      return {
        ...state,
        waiters: newWaiters
      }
    }
    
    case 'REMOVE_WAITER': {
      const { waiterId } = action.payload
      const newWaiters = new Map(state.waiters)
      
      // Remove waiter from all dependency lists
      newWaiters.forEach((waiters, depId) => {
        const filtered = waiters.filter(w => w.componentId !== waiterId)
        if (filtered.length === 0) {
          newWaiters.delete(depId)
        } else {
          newWaiters.set(depId, filtered)
        }
      })
      
      return {
        ...state,
        waiters: newWaiters
      }
    }
    
    case 'RESOLVE_WAITERS': {
      const { componentId } = action.payload
      const newWaiters = new Map(state.waiters)
      const waitersToResolve = newWaiters.get(componentId) || []
      
      // Remove resolved waiters
      newWaiters.delete(componentId)
      
      // Resolve all waiters for this component
      waitersToResolve.forEach(waiter => {
        clearTimeout(waiter.timeoutId)
        waiter.resolve()
      })
      
      return {
        ...state,
        waiters: newWaiters
      }
    }
    
    case 'ADD_CIRCULAR_DEPENDENCY': {
      const newCircular = new Set(state.circularDependencies)
      newCircular.add(action.payload)
      return {
        ...state,
        circularDependencies: newCircular
      }
    }
    
    case 'RESET_DEPENDENCIES': {
      // Clear all timeouts before resetting
      state.waiters.forEach(waiters => {
        waiters.forEach(waiter => clearTimeout(waiter.timeoutId))
      })
      
      return initialState
    }
    
    default:
      return state
  }
}

interface DependencyContextValue extends DependencyOperations {
  dependencyGraph: Map<string, Set<string>>
  dependentsGraph: Map<string, Set<string>>
  circularDependencies: Set<string>
  registerDependencies: (componentId: string, dependencies: string[]) => void
  removeDependencies: (componentId: string) => void
  notifyDependencyResolved: (componentId: string) => void
  hasCircularDependency: (componentId: string) => boolean
}

const DependencyContext = createContext<DependencyContextValue | undefined>(undefined)

interface DependencyProviderProps {
  children: React.ReactNode
  getComponentStatus?: (componentId: string) => string | undefined
  dependencyTimeout?: number
}

export function DependencyProvider({ 
  children, 
  getComponentStatus,
  dependencyTimeout = TIMEOUT_DELAYS.INITIALIZATION * 10 // 10 seconds default
}: DependencyProviderProps) {
  const [state, dispatch] = useReducer(dependencyReducer, initialState)
  const activeWaiters = useRef<Map<string, DependencyWaiter>>(new Map())

  // Cleanup on unmount
  React.useEffect(() => {
    return () => {
      // Clear all timeouts
      activeWaiters.current.forEach(waiter => {
        clearTimeout(waiter.timeoutId)
      })
      activeWaiters.current.clear()
    }
  }, [])

  const detectCircularDependencies = useCallback((
    componentId: string, 
    visited: Set<string> = new Set(), 
    path: Set<string> = new Set()
  ): boolean => {
    if (path.has(componentId)) {
      // Circular dependency detected
      logger.error('DependencyProvider', 'detectCircularDependencies', 
        `Circular dependency detected: ${Array.from(path).join(' -> ')} -> ${componentId}`)
      dispatch({ type: 'ADD_CIRCULAR_DEPENDENCY', payload: componentId })
      return true
    }
    
    if (visited.has(componentId)) {
      return false
    }
    
    visited.add(componentId)
    path.add(componentId)
    
    const dependencies = state.dependencyGraph.get(componentId) || new Set()
    for (const depId of dependencies) {
      if (detectCircularDependencies(depId, visited, new Set(path))) {
        return true
      }
    }
    
    path.delete(componentId)
    return false
  }, [state.dependencyGraph])

  const registerDependencies = useCallback((componentId: string, dependencies: string[]) => {
    logger.info('DependencyProvider', 'registerDependencies', 
      `Registering dependencies for ${componentId}: ${dependencies.join(', ')}`)
    
    dispatch({ type: 'REGISTER_DEPENDENCIES', payload: { componentId, dependencies } })
    
    // Check for circular dependencies
    detectCircularDependencies(componentId)
  }, [detectCircularDependencies])

  const removeDependencies = useCallback((componentId: string) => {
    logger.info('DependencyProvider', 'removeDependencies', 
      `Removing dependencies for ${componentId}`)
    
    dispatch({ type: 'REMOVE_DEPENDENCIES', payload: { componentId } })
  }, [])

  const checkDependencies = useCallback((componentId: string): boolean => {
    if (!getComponentStatus) {
      logger.warn('DependencyProvider', 'checkDependencies', 
        'No getComponentStatus function provided')
      return true
    }
    
    const dependencies = state.dependencyGraph.get(componentId) || new Set()
    
    for (const depId of dependencies) {
      const status = getComponentStatus(depId)
      if (!status || !['loaded', 'preserved', 'locked'].includes(status)) {
        return false
      }
    }
    
    return true
  }, [state.dependencyGraph, getComponentStatus])

  const waitForDependencies = useCallback(async (
    componentId: string, 
    dependencies: string[]
  ): Promise<void> => {
    logger.info('DependencyProvider', 'waitForDependencies', 
      `Component ${componentId} waiting for dependencies: ${dependencies.join(', ')}`)
    
    // Check if all dependencies are already resolved
    if (checkDependencies(componentId)) {
      logger.info('DependencyProvider', 'waitForDependencies', 
        `All dependencies for ${componentId} are already resolved`)
      return
    }
    
    // Check for circular dependencies
    if (detectCircularDependencies(componentId)) {
      throw new Error(`Circular dependency detected for component ${componentId}`)
    }
    
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        logger.error('DependencyProvider', 'waitForDependencies', 
          `Timeout waiting for dependencies of ${componentId}`)
        
        // Remove from active waiters
        activeWaiters.current.delete(componentId)
        dispatch({ type: 'REMOVE_WAITER', payload: { waiterId: componentId } })
        
        reject(new Error(`Timeout waiting for dependencies: ${dependencies.join(', ')}`))
      }, dependencyTimeout)
      
      const waiter: DependencyWaiter = {
        componentId,
        dependencies,
        resolve: () => {
          logger.info('DependencyProvider', 'waitForDependencies', 
            `Dependencies resolved for ${componentId}`)
          activeWaiters.current.delete(componentId)
          resolve()
        },
        reject,
        timeoutId
      }
      
      activeWaiters.current.set(componentId, waiter)
      dispatch({ type: 'ADD_WAITER', payload: waiter })
    })
  }, [checkDependencies, detectCircularDependencies, dependencyTimeout])

  const getDependencyChain = useCallback((componentId: string): string[] => {
    const chain: string[] = []
    const visited = new Set<string>()
    
    const traverse = (id: string) => {
      if (visited.has(id)) return
      visited.add(id)
      
      const dependencies = state.dependencyGraph.get(id) || new Set()
      dependencies.forEach(depId => {
        traverse(depId)
        if (!chain.includes(depId)) {
          chain.push(depId)
        }
      })
    }
    
    traverse(componentId)
    return chain
  }, [state.dependencyGraph])

  const notifyDependencyResolved = useCallback((componentId: string) => {
    logger.debug('DependencyProvider', 'notifyDependencyResolved', 
      `Dependency ${componentId} resolved`)
    
    // Check all waiters to see if their dependencies are now satisfied
    state.waiters.forEach((waiters, depId) => {
      if (depId === componentId) {
        waiters.forEach(waiter => {
          if (checkDependencies(waiter.componentId)) {
            dispatch({ type: 'RESOLVE_WAITERS', payload: { componentId: waiter.componentId } })
          }
        })
      }
    })
  }, [state.waiters, checkDependencies])

  const hasCircularDependency = useCallback((componentId: string): boolean => {
    return state.circularDependencies.has(componentId)
  }, [state.circularDependencies])

  const contextValue = useMemo((): DependencyContextValue => ({
    dependencyGraph: state.dependencyGraph,
    dependentsGraph: state.dependentsGraph,
    circularDependencies: state.circularDependencies,
    registerDependencies,
    removeDependencies,
    waitForDependencies,
    checkDependencies,
    getDependencyChain,
    notifyDependencyResolved,
    hasCircularDependency
  }), [
    state.dependencyGraph,
    state.dependentsGraph,
    state.circularDependencies,
    registerDependencies,
    removeDependencies,
    waitForDependencies,
    checkDependencies,
    getDependencyChain,
    notifyDependencyResolved,
    hasCircularDependency
  ])

  return (
    <DependencyContext.Provider value={contextValue}>
      {children}
    </DependencyContext.Provider>
  )
}

export function useDependencies(): DependencyContextValue {
  const context = useContext(DependencyContext)
  if (!context) {
    throw new Error('useDependencies must be used within a DependencyProvider')
  }
  return context
}