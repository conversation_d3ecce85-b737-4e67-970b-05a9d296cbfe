import { ComponentStatus, GroupStatus, DocumentStatus, EntityChangeStatus } from '../services/utils/constants'

// Re-export types for convenience
export type { ComponentStatus, GroupStatus, DocumentStatus, EntityChangeStatus }

// Core component interface
export interface ReportComponent {
  id: string
  type: string
  status: ComponentStatus
  parentId?: string
  children?: string[]
  dependencies?: string[]
  data?: Record<string, any>
  error?: string
  lastUpdated?: Date
}

// Component update payload
export interface ComponentUpdate {
  id: string
  status?: ComponentStatus
  data?: Record<string, any>
  error?: string
}

// Group information
export interface GroupInfo {
  id: string
  status: GroupStatus
  totalComponents: number
  loadedComponents: number
  children: string[]
  descendants: string[]
}

// Document state
export interface DocumentState {
  reportId: string | null
  status: DocumentStatus
  lastSaved?: Date
  hasUnsavedChanges: boolean
  error?: string
}

// Entity change tracking
export interface EntityChange {
  id: string
  entityId: string
  changeType: 'added' | 'removed' | 'updated'
  status: EntityChangeStatus
  timestamp: Date
}

// Context action types
export type DocumentAction = 
  | { type: 'REGISTER_COMPONENT'; payload: ReportComponent }
  | { type: 'UPDATE_COMPONENT'; payload: ComponentUpdate }
  | { type: 'REMOVE_COMPONENT'; payload: { id: string } }
  | { type: 'SET_DOCUMENT_STATUS'; payload: DocumentStatus }
  | { type: 'SET_REPORT_ID'; payload: string | null }
  | { type: 'SET_UNSAVED_CHANGES'; payload: boolean }
  | { type: 'ADD_ENTITY_CHANGE'; payload: EntityChange }
  | { type: 'CLEAR_ENTITY_CHANGES' }
  | { type: 'RESET_STATE' }

// Hook return types
export interface ComponentOperations {
  registerComponent: (component: ReportComponent) => void
  updateComponent: (update: ComponentUpdate) => void
  removeComponent: (id: string) => void
  getComponent: (id: string) => ReportComponent | undefined
  getComponentsByType: (type: string) => ReportComponent[]
  getComponentsByParent: (parentId: string) => ReportComponent[]
}

export interface DocumentOperations {
  loadDocument: (reportId: string) => Promise<void>
  saveDocument: () => Promise<void>
  resetDocument: () => void
  setUnsavedChanges: (hasChanges: boolean) => void
}

export interface DependencyOperations {
  waitForDependencies: (componentId: string, dependencies: string[]) => Promise<void>
  checkDependencies: (componentId: string) => boolean
  getDependencyChain: (componentId: string) => string[]
}

// Performance monitoring
export interface PerformanceMetrics {
  renderCount: number
  lastRenderTime: number
  averageRenderTime: number
  componentCount: number
  memoryUsage?: number
}