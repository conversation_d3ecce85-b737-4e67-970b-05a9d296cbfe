'use client'

import { Extension } from '@tiptap/core'
import { PluginKey } from 'prosemirror-state'
import Suggestion from '@tiptap/suggestion'
import { ReactRenderer } from '@tiptap/react'
import tippy from 'tippy.js'
import { AISlashCommandsList } from '../components/AISlashCommandsList'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    aiSlashCommand: {
      executeAISlashCommand: (command: string, range?: any) => ReturnType
    }
  }
}

export interface AISlashCommandOptions {
  suggestion: {
    char: string
    command: ({ editor, range, props }: any) => void
  }
  onAICommand?: (command: string, editor: any) => Promise<void>
}

export const AISlashCommandExtension = Extension.create<AISlashCommandOptions>({
  name: 'aiSlashCommand',

  addOptions() {
    return {
      suggestion: {
        char: '/ai',
        command: ({ editor, range, props }: any) => {
          props.command({ editor, range })
        },
      },
      onAICommand: undefined,
    }
  },

  addProseMirrorPlugins() {
    return [
      Suggestion({
        editor: this.editor,
        char: this.options.suggestion.char,
        pluginKey: new PluginKey('aiSlashCommand'),
        command: ({ editor, range, props }) => {
          // Execute the AI command and clean up the slash text
          if (props.command) {
            const { from, to } = range
            
            // Delete the "/ai" text first
            editor.chain().focus().deleteRange({ from, to }).run()
            
            // Execute the AI command
            if (this.options.onAICommand) {
              this.options.onAICommand(props.command, editor)
                .catch((error: any) => {
                  console.error('AI slash command failed:', error)
                })
            }
          }
        },
        items: ({ query }) => {
          return [
            {
              title: 'Improve Writing',
              description: 'Enhance clarity and flow of selected text',
              command: 'improve',
              icon: '✨',
            },
            {
              title: 'Fix Grammar',
              description: 'Correct grammar and spelling errors',
              command: 'grammar',
              icon: '✓',
            },
            {
              title: 'Make Shorter',
              description: 'Reduce length while keeping meaning',
              command: 'shorter',
              icon: '📝',
            },
            {
              title: 'Expand',
              description: 'Add more detail and examples',
              command: 'expand',
              icon: '📖',
            },
            {
              title: 'Change Tone',
              description: 'Adjust writing tone and style',
              command: 'tone',
              icon: '🎭',
            },
            {
              title: 'Summarize',
              description: 'Create a concise summary',
              command: 'summarize',
              icon: '📋',
            },
            {
              title: 'Continue Writing',
              description: 'Continue from current position',
              command: 'continue',
              icon: '➡️',
            },
            {
              title: 'Custom Prompt',
              description: 'Enter a custom AI instruction',
              command: 'custom',
              icon: '💭',
            },
          ].filter(item => 
            item.title.toLowerCase().includes(query.toLowerCase()) ||
            item.description.toLowerCase().includes(query.toLowerCase())
          )
        },
        render: () => {
          let component: ReactRenderer
          let popup: any

          return {
            onStart: (props: any) => {
              component = new ReactRenderer(AISlashCommandsList, {
                props,
                editor: props.editor,
              })

              if (!props.clientRect) {
                return
              }

              popup = tippy('body', {
                getReferenceClientRect: props.clientRect,
                appendTo: () => document.body,
                content: component.element,
                showOnCreate: true,
                interactive: true,
                trigger: 'manual',
                placement: 'bottom-start',
                hideOnClick: true,
                onClickOutside: () => {
                  popup[0].hide()
                },
              })
            },

            onUpdate(props: any) {
              component.updateProps(props)

              if (!props.clientRect) {
                return
              }

              popup[0].setProps({
                getReferenceClientRect: props.clientRect,
              })
            },

            onKeyDown(props: any) {
              if (props.event.key === 'Escape') {
                popup[0].hide()
                return true
              }

              return (component.ref as any)?.onKeyDown?.(props) || false
            },

            onExit() {
              popup[0].destroy()
              component.destroy()
            },
          }
        },
      }),
    ]
  },

  addCommands() {
    return {
      executeAISlashCommand:
        (command: string, range?: any) =>
        ({ editor, state, dispatch }: { editor: any; state: any; dispatch: any }) => {
          if (this.options.onAICommand) {
            // Remove the slash command text
            if (range) {
              const transaction = state.tr.deleteRange(range.from, range.to)
              if (dispatch) {
                dispatch(transaction)
              }
            }

            // Execute the AI command
            this.options.onAICommand(command, editor)
              .catch((error: any) => {
                console.error('AI slash command failed:', error)
              })
          }

          return true
        },
    }
  },
})
