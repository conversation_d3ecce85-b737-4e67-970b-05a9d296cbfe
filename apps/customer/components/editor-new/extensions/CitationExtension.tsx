import React from 'react'
import { Node } from '@tiptap/core'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ReactNodeViewRenderer } from '@tiptap/react'
import { citationLink, CitationType } from '@/components/citation'
import { useDocumentContext } from '../context/DocumentContext'

/* ------------------------------------------------------------------
 *  Citation React Component
 * ------------------------------------------------------------------*/
interface CitationComponentProps {
  node: any;
  citations: CitationType[];
  admin: boolean;
  editor?: any; // Add editor prop to access document state
  getPos?: () => number | undefined; // Add getPos prop to get position in document
}

const CitationComponent = React.memo<CitationComponentProps>(({ node, citations, admin, editor, getPos }) => {
  const pageId = parseInt(node.attrs.page_id || '0');
  const { state } = useDocumentContext()

  // Find the citation by page_id
  const citation = React.useMemo(() => {
    // First try to find citation in the static citations array
    let foundCitation = citations.find(c =>
      c.doc_page_id && c.doc_page_id === pageId
    );

    // If not found in static array, try to get from DocumentContext
    if (!foundCitation) {
      foundCitation = state.citations.find(c =>
        c.doc_page_id && c.doc_page_id === pageId
      );
    }

    return foundCitation || null
  }, [citations, pageId, state.citations]);

  // Simple citation numbering - find index in citations array
  const finalIndex = React.useMemo(() => {
    if (!citation) return 1

    // Try to find in static citations first
    const staticIndex = citations.findIndex(c => c.doc_page_id === pageId)
    if (staticIndex >= 0) {
      return staticIndex + 1
    }

    // Try to find in DocumentContext citations
    const contextIndex = state.citations.findIndex(c => c.doc_page_id === pageId)
    if (contextIndex >= 0) {
      return contextIndex + 1
    }

    return 1 // Default fallback
  }, [citation, citations, state.citations, pageId]);

  if (!citation) {
    // In view mode (including print), show a generic citation number instead of error
    return (
      <NodeViewWrapper className="citation-wrapper inline">
        <span className="citation-link-group">
          <span className="text-blue-600 font-medium print:text-black">[{finalIndex}]</span>
        </span>
      </NodeViewWrapper>
    )
  }

  const externalUrl = citationLink(citation, admin)
  const internalUrl = `#reference-${finalIndex}`

  return (
    <NodeViewWrapper className="citation-wrapper inline">
      <span className="citation-link-group">
        {/* Primary link to references section */}
        <a
          href={internalUrl}
          className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 no-underline font-medium print:text-black print:no-underline"
          title={`Go to reference ${finalIndex}: ${citation.title} (${citation.year || 'No date'})`}
        >
          [{finalIndex}]
        </a>
        {/* Secondary external link - appears on hover */}
        <a
          href={externalUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="ml-1 text-xs text-gray-400 hover:text-blue-600 opacity-0 hover:opacity-100 transition-opacity print:hidden"
          title={`Open external source: ${citation.title}`}
        >
          ↗
        </a>
      </span>
    </NodeViewWrapper>
  );
});

CitationComponent.displayName = 'CitationComponent';

/* ------------------------------------------------------------------
 *  TipTap Citation Extension
 * ------------------------------------------------------------------*/
export const CitationExtension = Node.create({
  name: 'citation',

  group: 'inline',

  inline: true,

  atom: true,

  addOptions() {
    return {
      citations: [] as CitationType[],
      admin: false,
    };
  },

  addAttributes() {
    return {
      page_id: {
        default: null,
        parseHTML: element => element.getAttribute('page_id'),
        renderHTML: attributes => {
          if (!attributes.page_id) {
            return {};
          }
          return {
            page_id: attributes.page_id,
          };
        },
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'citation',
        getAttrs: (element) => {
          if (typeof element === 'string') return false;
          return {
            page_id: element.getAttribute('page_id'),
          };
        },
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return ['citation', HTMLAttributes];
  },

  addNodeView() {
    return ReactNodeViewRenderer((props) => {
      // Create a unique key that includes position to avoid React duplicate key warnings
      // while still maintaining correct citation numbering based on first occurrence
      const position = props.getPos ? props.getPos() : Math.random()
      const pageId = props.node.attrs.page_id || 'unknown'
      const uniqueKey = `citation-${pageId}-${position}`

      return (
        <CitationComponent
          key={uniqueKey}
          {...props}
          citations={this.options.citations}
          admin={this.options.admin}
          editor={props.editor}
          getPos={props.getPos}
        />
      )
    });
  },
});
