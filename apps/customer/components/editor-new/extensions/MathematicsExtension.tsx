import { Node, mergeAttributes } from '@tiptap/core'
import { ReactNodeViewRenderer } from '@tiptap/react'
import React, { useState, useEffect } from 'react'
import { NodeViewWrapper } from '@tiptap/react'

// We'll use KaTeX for math rendering
let katex: any = null

// Dynamically import KaTeX to avoid SSR issues
const loadKaTeX = async () => {
  if (typeof window !== 'undefined' && !katex) {
    try {
      katex = await import('katex')
      // Also load KaTeX CSS
      const link = document.createElement('link')
      link.rel = 'stylesheet'
      link.href = 'https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.css'
      document.head.appendChild(link)
    } catch (error) {
      console.error('Failed to load KaTeX:', error)
    }
  }
  return katex
}

interface MathComponentProps {
  node: any
  updateAttributes: (attrs: any) => void
  deleteNode: () => void
  selected: boolean
}

const MathComponent: React.FC<MathComponentProps> = ({ 
  node, 
  updateAttributes, 
  deleteNode,
  selected 
}) => {
  const [isEditing, setIsEditing] = useState(!node.attrs.latex || node.attrs.latex.trim() === '')
  const [latex, setLatex] = useState(node.attrs.latex || '')
  const [renderedMath, setRenderedMath] = useState('')
  const [error, setError] = useState('')

  useEffect(() => {
    const renderMath = async () => {
      const katexLib = await loadKaTeX()
      if (!katexLib || !latex.trim()) {
        setRenderedMath('')
        return
      }

      try {
        const html = katexLib.renderToString(latex, {
          displayMode: node.attrs.display || false,
          throwOnError: false,
          errorColor: '#cc0000',
        })
        setRenderedMath(html)
        setError('')
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Rendering error')
        setRenderedMath('')
      }
    }

    renderMath()
  }, [latex, node.attrs.display])

  const handleSave = () => {
    updateAttributes({ latex })
    setIsEditing(false)
  }

  const handleCancel = () => {
    setLatex(node.attrs.latex || '')
    setIsEditing(false)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      handleSave()
    } else if (e.key === 'Escape') {
      handleCancel()
    }
  }

  if (isEditing) {
    return (
      <NodeViewWrapper className="math-editor">
        <div className={`border-2 rounded-lg p-4 ${selected ? 'border-blue-500' : 'border-gray-300'}`}>
          <div className="mb-2">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              LaTeX Expression:
            </label>
            <textarea
              value={latex}
              onChange={(e) => setLatex(e.target.value)}
              onKeyDown={handleKeyDown}
              className="w-full p-2 border border-gray-300 rounded font-mono text-sm"
              rows={3}
              placeholder="Enter LaTeX expression (e.g., x = \frac{-b \pm \sqrt{b^2-4ac}}{2a})"
              autoFocus
            />
          </div>
          
          {error && (
            <div className="mb-2 p-2 bg-red-50 border border-red-200 rounded text-red-700 text-sm">
              Error: {error}
            </div>
          )}
          
          {renderedMath && (
            <div className="mb-2 p-2 bg-gray-50 border border-gray-200 rounded">
              <div className="text-sm text-gray-600 mb-1">Preview:</div>
              <div dangerouslySetInnerHTML={{ __html: renderedMath }} />
            </div>
          )}
          
          <div className="flex gap-2">
            <button
              onClick={handleSave}
              className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
            >
              Save (Ctrl+Enter)
            </button>
            <button
              onClick={handleCancel}
              className="px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600"
            >
              Cancel (Esc)
            </button>
            <button
              onClick={deleteNode}
              className="px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600"
            >
              Delete
            </button>
          </div>
        </div>
      </NodeViewWrapper>
    )
  }

  return (
    <NodeViewWrapper className="math-wrapper">
      <div 
        className={`inline-block cursor-pointer p-2 rounded ${
          selected ? 'bg-blue-50 border border-blue-300' : 'hover:bg-gray-50'
        }`}
        onClick={() => setIsEditing(true)}
        title="Click to edit math expression"
      >
        {renderedMath ? (
          <div dangerouslySetInnerHTML={{ __html: renderedMath }} />
        ) : (
          <div className="text-gray-500 italic">
            {latex || 'Click to add math expression'}
          </div>
        )}
      </div>
    </NodeViewWrapper>
  )
}

export interface MathematicsOptions {
  HTMLAttributes: Record<string, any>
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    mathematics: {
      setMath: (options: { latex: string; display?: boolean }) => ReturnType
      setInlineMath: (latex: string) => ReturnType
      setDisplayMath: (latex: string) => ReturnType
    }
  }
}

export const MathematicsExtension = Node.create<MathematicsOptions>({
  name: 'mathematics',

  addOptions() {
    return {
      HTMLAttributes: {},
    }
  },

  group: 'inline',

  inline: true,

  atom: true,

  addAttributes() {
    return {
      latex: {
        default: '',
        parseHTML: element => element.getAttribute('data-latex') || '',
        renderHTML: attributes => {
          if (!attributes.latex) {
            return {}
          }
          return { 'data-latex': attributes.latex }
        },
      },
      display: {
        default: false,
        parseHTML: element => element.hasAttribute('data-display'),
        renderHTML: attributes => {
          if (!attributes.display) {
            return {}
          }
          return { 'data-display': '' }
        },
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'span[data-latex]',
      },
      {
        tag: 'div[data-latex]',
        getAttrs: () => ({ display: true }),
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    const tag = HTMLAttributes.display ? 'div' : 'span'
    return [tag, mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)]
  },

  addNodeView() {
    return ReactNodeViewRenderer(MathComponent)
  },

  addCommands() {
    return {
      setMath:
        (options) =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs: options,
          })
        },
      setInlineMath:
        (latex) =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs: { latex, display: false },
          })
        },
      setDisplayMath:
        (latex) =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs: { latex, display: true },
          })
        },
    }
  },

  addKeyboardShortcuts() {
    return {
      'Mod-Shift-m': () => this.editor.commands.setInlineMath(''),
      'Mod-Shift-M': () => this.editor.commands.setDisplayMath(''),
    }
  },
})

export default MathematicsExtension
