import React, { use<PERSON><PERSON>back, useEffect, useRef, useState } from 'react'
import { Node } from '@tiptap/core'
import { NodeViewContent, NodeViewWrapper, ReactNodeViewRenderer } from '@tiptap/react'
import { useReportManager } from '../hooks/useReportManager'
import { useDocumentContext } from '../context/DocumentContext'
import { processMarkdownForTipTap } from '../utils/markdown-processor'
import { transformHeadings, calculateReportGroupDepth, type HeadingTransform } from '../utils/markdown-heading-utils'
import { Button } from '@ui/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@ui/components/ui/dropdown-menu'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@ui/components/ui/tooltip'
import {
  Alert<PERSON>riangle,
  GripVertical,
  Loader2,
  Lock,
  MoreVertical,
  <PERSON>fresh<PERSON><PERSON>,
  <PERSON>ting<PERSON>,
  Shield,
  Trash2,
} from 'lucide-react'
import { cn } from '@utils/lib/utils'
import { ReportComponentConfig, ReportComponentDialog } from '../dialogs/ReportComponentDialog'

/**
 * Check if a TipTap node has meaningful content
 * @param node The TipTap node to check
 * @returns true if the node has content, false if empty
 */
function hasNodeContent(node: any): boolean {
  if (!node || !node.content) return false

  // Check if node has any content at all
  if (node.content.size === 0) return false

  // Extract text content and check if it's meaningful
  const textContent = node.textContent || ''
  const trimmedContent = textContent.trim()

  // Consider empty if only whitespace or very short content
  return trimmedContent.length > 0
}

// Utility function to find parent report-group
function findParentReportGroup(editor: any, pos: number): string | null {
  if (!editor || pos === undefined) return null

  try {
    const resolvedPos = editor.state.doc.resolve(pos)

    // Walk up the document tree to find a parent report-group
    for (let depth = resolvedPos.depth; depth >= 0; depth--) {
      const node = resolvedPos.node(depth)
      if (node.type.name === 'reportGroup' && node.attrs.id) {
        return node.attrs.id
      }
    }
  } catch (error) {
    console.warn('Error finding parent report group:', error)
  }

  return null
}

/**
 * Extract content from a TipTap node while preserving citations in markdown format
 * This traverses the document to find the node and converts it to markdown with citations
 */
function extractContentWithCitations(editor: any, nodeId: string): string {
  if (!editor || !editor.state) return ''

  let extractedContent = ''

  // Traverse the document to find the node with the matching ID
  editor.state.doc.descendants((node: any, pos: number) => {
    // Check if this is a report node with the matching ID
    if ((node.type.name === 'reportGroup' || node.type.name === 'reportSection') &&
      node.attrs.id === nodeId) {

      // Extract content from this node and its children, preserving citations
      extractedContent += extractNodeContentWithCitations(node)

      // For report groups, also extract content from child nodes
      if (node.type.name === 'reportGroup') {
        node.descendants((childNode: any) => {
          if (childNode.type.name !== 'reportGroup' && childNode.type.name !== 'reportSection') {
            const childContent = extractNodeContentWithCitations(childNode)
            if (childContent.trim()) {
              extractedContent += childContent + '\n\n'
            }
          }
        })
      }
    }
  })

  return extractedContent.trim()
}

/**
 * Recursively extract content from a TipTap node, converting citations to markdown format
 */
function extractNodeContentWithCitations(node: any): string {
  if (!node) return ''

  let content = ''

  // Handle different node types
  switch (node.type.name) {
    case 'text':
      content += node.text || ''
      break

    case 'citation':
      // Convert citation node to markdown format using page_id
      const pageId = node.attrs?.page_id
      if (pageId) {
        content += `[^${pageId}]`
      }
      break

    case 'paragraph':
      // Extract content from paragraph children
      if (node.content) {
        node.content.forEach((child: any) => {
          content += extractNodeContentWithCitations(child)
        })
      }
      content += '\n\n'
      break

    case 'heading':
      // Extract heading content with markdown formatting
      const level = node.attrs?.level || 1
      const headingPrefix = '#'.repeat(level) + ' '
      if (node.content) {
        let headingText = ''
        node.content.forEach((child: any) => {
          headingText += extractNodeContentWithCitations(child)
        })
        content += headingPrefix + headingText.trim() + '\n\n'
      }
      break

    case 'bulletList':
    case 'orderedList':
      // Extract list content
      if (node.content) {
        node.content.forEach((listItem: any) => {
          content += '- ' + extractNodeContentWithCitations(listItem).trim() + '\n'
        })
      }
      content += '\n'
      break

    case 'listItem':
      // Extract list item content
      if (node.content) {
        node.content.forEach((child: any) => {
          content += extractNodeContentWithCitations(child)
        })
      }
      break

    default:
      // For other node types, recursively extract from children
      if (node.content) {
        node.content.forEach((child: any) => {
          content += extractNodeContentWithCitations(child)
        })
      }
      break
  }

  return content
}

/**
 * Legacy function for backward compatibility
 * @deprecated Use extractContentWithCitations instead
 */
function extractContentFromNode(editor: any, nodeId: string): string {
  return extractContentWithCitations(editor, nodeId)
}

/* ------------------------------------------------------------------
 *  Report Summary React Component
 * ------------------------------------------------------------------*/
const ReportSummaryComponent = React.memo<{ node: any; updateAttributes: any; deleteNode: any; editor: any; getPos: () => number | undefined }>(({ node, updateAttributes, deleteNode, editor, getPos }) => {
  const id = React.useMemo(() => {
    // Ensure we always have a valid ID
    const nodeId = node.attrs.id;
    if (!nodeId || nodeId === 'null' || nodeId === 'undefined') {
      // Generate a default ID if none provided
      const defaultId = `summary-${Date.now()}`;
      console.warn(`Report summary missing ID, generating default: ${defaultId}`);
      // Update the node attributes with the generated ID
      setTimeout(() => updateAttributes({ id: defaultId }), 0);
      return defaultId;
    }
    return nodeId;
  }, [node.attrs.id, updateAttributes]);
  const title = React.useMemo(() => node.attrs.title, [node.attrs.title]);
  const prompt = React.useMemo(() => node.attrs.prompt, [node.attrs.prompt]);
  const summarize = React.useMemo(() => node.attrs.summarize, [node.attrs.summarize]); // Comma-separated list of component IDs
  const locked = React.useMemo(() => node.attrs.locked, [node.attrs.locked]);
  const preserved = React.useMemo(() => node.attrs.preserved, [node.attrs.preserved]);
  const [configDialogOpen, setConfigDialogOpen] = useState(false);
  
  const reportManager = useReportManager();
  const { state: documentState } = useDocumentContext();
  const [isLoading, setIsLoading] = useState(false);
  const [content, setContent] = useState('');
  const [isWaitingForDependencies, setIsWaitingForDependencies] = useState(false);
  const generationRef = useRef(false) // Prevent multiple simultaneous generations
  const initializationRef = useRef(false) // Prevent multiple initialization attempts

  // Check if this summary has content
  const hasContent = node.content && node.content.size > 0;

  // Parse the summarize attribute to get dependency IDs
  const dependencyIds = React.useMemo(() => {
    if (!summarize) return [];
    return summarize.split(',').map((id: string) => id.trim()).filter(Boolean);
  }, [summarize]);

  // Find parent report group
  const parentGroupId = React.useMemo(() => {
    const pos = getPos()
    return pos !== undefined ? findParentReportGroup(editor, pos) : null
  }, [editor, getPos])

  useEffect(() => {
    // Set initial status - respect status from HTML attributes
    let initialStatus: 'idle' | 'loaded' | 'preserved' | 'locked' | 'loading' | 'error' = node.attrs.status || 'idle'

    // Override with locked/preserved if those attributes are set
    if (preserved) {
      initialStatus = 'preserved'
    } else if (locked) {
      initialStatus = 'locked'
    }

    // Special case: If status is 'loaded' but there's no actual content, reset to 'idle'
    // This handles cases where the document was saved with 'loaded' status but content was never generated
    if (initialStatus === 'loaded' && (!node.content || node.content.size === 0)) {
      console.log(`Report summary ${id}: Status is 'loaded' but no content found, resetting to 'idle'`)
      initialStatus = 'idle'
    }

    console.log(`Report summary ${id}: Registering with status '${initialStatus}' (from HTML: '${node.attrs.status}', locked: ${locked}, preserved: ${preserved}, hasContent: ${node.content && node.content.size > 0})`)
    // Note: Content presence now affects initial status for loaded summaries

    // Register this component with the report manager
    reportManager.registerComponent({
      id,
      type: 'report-summary',
      status: initialStatus,
      title,
      prompt,
      content,
      dependencies: dependencyIds,
      parentId: parentGroupId || undefined,
    });

    return () => {
      reportManager.removeComponent(id);
    };
  }, [id, title, prompt, content, dependencyIds, parentGroupId, preserved, locked, node.attrs.status]); // Remove reportManager from deps

  // Debounced status synchronization to prevent infinite loops
  const statusSyncTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const lastSyncedStatusRef = useRef(node.attrs.status)
  const isSyncingRef = useRef(false)

  // Sync component status to node attributes with debouncing
  const syncComponentState = reportManager.components.get(id)
  const currentComponentStatus = syncComponentState?.status

  useEffect(() => {
    // Skip if we're currently in a sync operation to prevent loops
    if (isSyncingRef.current) return

    if (currentComponentStatus && currentComponentStatus !== lastSyncedStatusRef.current) {
      // Clear any existing timeout
      if (statusSyncTimeoutRef.current) {
        clearTimeout(statusSyncTimeoutRef.current)
      }

      // Debounce the status update
      statusSyncTimeoutRef.current = setTimeout(() => {
        if (currentComponentStatus !== node.attrs.status) {
          console.log(`Report summary ${id}: Syncing status from component '${currentComponentStatus}' to node (was '${node.attrs.status}')`)
          isSyncingRef.current = true
          lastSyncedStatusRef.current = currentComponentStatus
          updateAttributes({ status: currentComponentStatus })

          // Reset sync flag after a brief delay
          setTimeout(() => {
            isSyncingRef.current = false
          }, 50)
        }
      }, 100) // 100ms debounce
    }

    return () => {
      if (statusSyncTimeoutRef.current) {
        clearTimeout(statusSyncTimeoutRef.current)
      }
    }
  }, [currentComponentStatus, id, updateAttributes, node.attrs.status])

  // Update tracking when node attributes change from external sources
  useEffect(() => {
    if (!isSyncingRef.current) {
      lastSyncedStatusRef.current = node.attrs.status
    }
  }, [node.attrs.status])

  // EKO-117: Watch for status changes to 'idle' and trigger reload
  const previousStatusRef = useRef<string | undefined>(undefined)

  const generateSummary = useCallback(async () => {
    if (locked || preserved) return;

    // Check if component is already in a final state
    const componentState = reportManager.components.get(id)
    if (componentState?.status === 'loaded' || componentState?.status === 'error') {
      console.log(`Report summary ${id}: Already in final state (${componentState.status}), skipping generation`)
      return
    }

    // Prevent multiple simultaneous generations
    if (generationRef.current) {
      console.log(`Report summary ${id}: Already generating, skipping duplicate request`)
      return
    }

    generationRef.current = true
    setIsLoading(true);
    reportManager.updateComponent(id, { status: 'loading' });

    try {
      // Wait for dependencies to be ready
      setIsWaitingForDependencies(true);
      await reportManager.waitForDependencies(id);
      setIsWaitingForDependencies(false);

      // Get content from dependencies - extract from TipTap document
      const contentToSummarize = dependencyIds
        .map((depId: string) => {
          const dep = reportManager.components.get(depId)
          if (!dep) {
            console.log(`Report summary ${id}: Dependency ${depId} not found in components`)
            return ''
          }

          console.log(`Report summary ${id}: Processing dependency ${depId} (type: ${dep.type}, status: ${dep.status}, hasContent: ${!!dep.content})`)

          // For report sections, use the stored content
          if (dep.type === 'report-section') {
            if (dep.content) {
              console.log(`Report summary ${id}: Using stored content from section ${depId} (${dep.content.length} chars)`)
              return dep.content
            } else {
              console.warn(`Report summary ${id}: Section ${depId} has status '${dep.status}' but no content available`)
              // Try to extract content from TipTap document as fallback (with citations preserved)
              if (editor) {
                const extractedContent = extractContentWithCitations(editor, depId)
                if (extractedContent.trim()) {
                  console.log(`Report summary ${id}: Fallback extraction from section ${depId} successful (${extractedContent.length} chars)`)
                  return extractedContent
                }
              }
              return ''
            }
          }

          // For report groups, extract content from the TipTap document (with citations preserved)
          if (dep.type === 'report-group' && editor) {
            const extractedContent = extractContentWithCitations(editor, depId)
            console.log(`Report summary ${id}: Extracted content from group ${depId} (${extractedContent.length} chars): ${extractedContent.substring(0, 100)}...`)
            return extractedContent
          }

          console.log(`Report summary ${id}: No content available for dependency ${depId} (type: ${dep.type})`)
          return ''
        })
        .filter((content: string) => content.trim() !== '')
        .join('\n\n')

      console.log(`Report summary ${id}: Total content to summarize: ${contentToSummarize.length} chars`)

      if (!contentToSummarize || contentToSummarize.trim() === '') {
        console.log(`Report summary ${id}: No content available to summarize from dependencies`)
        setContent('No content available to summarize.')
        reportManager.updateComponent(id, {
          status: 'loaded',
          content: 'No content available to summarize.',
          lastRefreshed: new Date(),
        })
        return
      }

      // Call the summarize endpoint
      const response = await fetch('/api/report/summarize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: contentToSummarize,
          prompt: prompt || '',
          title: title || '',
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to generate summary: ${response.statusText}`);
      }

      const data = await response.json();

      if(!data.text) {
        throw new Error('Failed to generate summary: No text returned from API');
      }

      const newContent = data.text;

      setContent(newContent);
      console.log(`Report summary ${id}: Summary generation completed, setting status to 'loaded'`)
      reportManager.updateComponent(id, {
        status: 'loaded',
        content: newContent,
        lastRefreshed: new Date()
      });
      console.log(`Report summary ${id}: Status updated to 'loaded', component state:`, reportManager.components.get(id))

      // Update the node content with the generated summary
      if (newContent && editor && getPos) {
        // Process markdown content through the same pipeline as initial_content
        try {
          // Validate content before processing
          if (!newContent || typeof newContent !== 'string') {
            throw new Error('Invalid content: content must be a non-empty string')
          }

          // Apply heading transformation based on component configuration
          const headingsTransform = node.attrs.headings as HeadingTransform || 'remove'
          let transformedContent = newContent

          if (headingsTransform !== 'keep') {
            // Calculate depth if needed for reset transformation
            let targetDepth = node.attrs.depth || 1
            if (headingsTransform === 'reset' && !node.attrs.depth) {
              // Auto-calculate depth based on parent report groups
              const pos = getPos()
              if (pos !== undefined) {
                const calculatedDepth = calculateReportGroupDepth(editor, pos)
                targetDepth = calculatedDepth + 1 // Start one level deeper than parent groups
              }
            }

            transformedContent = transformHeadings(newContent, headingsTransform, targetDepth)
          }

          // Get citations from DocumentContext to properly convert [^page_id] back to citation elements
          const citations = documentState?.citations || [];
          console.log(`Report summary ${id}: Processing markdown with ${citations.length} citations available`);

          const processedResult = await processMarkdownForTipTap(transformedContent, citations, {
            admin: true,
            inlineCitations: true,
            badgeStyle: true,
            skipCitations: false
          });

          // Validate processed result
          if (!processedResult || !processedResult.html) {
            throw new Error('Markdown processing returned invalid result')
          }

          // Recalculate position after async processing to avoid "out of range" errors
          const currentPos = getPos();
          if (currentPos !== undefined) {
            // Insert content at the end of the node (after the title if present)
            const currentNodeSize = node.nodeSize;
            const insertPos = currentPos + currentNodeSize - 1; // Insert before the closing tag

            // Insert the processed HTML content
            const htmlContent = `<div class="report-summary-content">${processedResult.html}</div>`;
            editor.commands.insertContentAt(insertPos, htmlContent);
          }
        } catch (error) {
          console.error(`Report summary ${id}: Failed to process markdown:`, error);

          // Fallback to raw content with position recalculation
          const fallbackPos = getPos();
          if (fallbackPos !== undefined) {
            const fallbackNodeSize = node.nodeSize;
            const fallbackInsertPos = fallbackPos + fallbackNodeSize - 1;
            // Escape HTML in raw content to prevent injection
            const escapedContent = newContent.replace(/</g, '&lt;').replace(/>/g, '&gt;');
            const htmlContent = `<div class="report-summary-content">${escapedContent}</div>`;

            try {
              editor.commands.insertContentAt(fallbackInsertPos, htmlContent);
            } catch (posError) {
              console.error(`Report summary ${id}: Fallback insertion also failed:`, posError);
              // Last resort: append to the end of the document
              editor.commands.insertContent(htmlContent);
            }
          }
        }
      }
      
    } catch (error) {
      console.error('Error generating summary:', error);
      reportManager.updateComponent(id, {
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      generationRef.current = false
      setIsLoading(false);
      setIsWaitingForDependencies(false);
    }
  }, [id, locked, preserved, dependencyIds, prompt, title, reportManager, editor, getPos, node]);

  // EKO-117: Watch for status changes to 'idle' and trigger reload
  useEffect(() => {
    const currentStatus = syncComponentState?.status
    const previousStatus = previousStatusRef.current

    // Update the previous status ref
    previousStatusRef.current = currentStatus

    // If status changed to 'idle', trigger reload from database
    if (currentStatus === 'idle' && previousStatus && previousStatus !== 'idle') {
      console.log(`Report summary ${id}: Status changed to 'idle' from '${previousStatus}', triggering reload`)

      // Only reload if we aren't locked/preserved
      if (!locked && !preserved) {
        // Small delay to ensure status change is fully processed
        setTimeout(() => {
          generateSummary().catch(error => {
            console.error(`Report summary ${id}: Idle status reload failed:`, error)
          })
        }, 100)
      }
    }
  }, [syncComponentState?.status, id, locked, preserved, generateSummary])

  // Track dependency statuses to trigger effect when they change
  const dependencyStatuses = dependencyIds.map((depId: string) => {
    const dep = reportManager.components.get(depId)
    return dep?.status || 'unknown'
  }).join(',')

  // Check dependencies and update status - runs when dependencies change or when status is 'idle'
  useEffect(() => {
    const componentState = reportManager.components.get(id)

    // Don't proceed if locked/preserved or no dependencies
    if (!dependencyIds.length || locked || preserved) {
      return
    }

    // Only proceed if status is 'idle' or 'loading' (to handle dependency changes)
    const currentStatus = componentState?.status
    if (currentStatus !== 'idle' && currentStatus !== 'loading') {
      console.log(`Report summary ${id}: Skipping dependency check - status is '${currentStatus}'`)
      return
    }

    console.log(`Report summary ${id}: Status is '${componentState?.status}', checking dependencies`)

    // Check dependency statuses
    const dependencies = dependencyIds.map((depId: string) => reportManager.components.get(depId)).filter(Boolean)
    const missingDependencies = dependencyIds.filter((depId: string) => !reportManager.components.get(depId))

    if (missingDependencies.length > 0) {
      console.warn(`Report summary ${id}: Missing dependencies: ${missingDependencies.join(', ')}`)
    }

    const hasErrorDependency = dependencies.some((dep: any) => dep.status === 'error')
    const hasIdleOrLoadingDependency = dependencies.some((dep: any) => dep.status === 'idle' || dep.status === 'loading')
    const allDependenciesLoaded = dependencies.every((dep: any) => dep.status === 'loaded' || dep.status === 'preserved' || dep.status === 'locked')

    console.log(`Report summary ${id}: Dependencies status:`, dependencies.map((dep: any) => `${dep.id}:${dep.status}`))

    // Enhanced logging for debugging executive summary issue
    console.log(`SUMMARY DEBUG: ${id} dependency analysis:`)
    console.log(`  - Dependencies requested: ${dependencyIds.join(', ')}`)
    console.log(`  - Dependencies found: ${dependencies.length}`)
    console.log(`  - Missing dependencies: ${dependencyIds.filter((depId: string) => !reportManager.components.get(depId)).join(', ')}`)
    dependencies.forEach((dep: any) => {
      console.log(`  - ${dep.id} (${dep.type}): ${dep.status}`)
      if (dep.type === 'report-group') {
        const children = reportManager.getChildren(dep.id)
        console.log(`    - Group ${dep.id} has ${children.length} children:`, children.map(c => `${c.id}:${c.status}`))
      }
    })

    if (hasErrorDependency) {
      console.log(`Report summary ${id}: Dependencies have errors, setting status to 'error'`)
      reportManager.updateComponent(id, { status: 'error', error: 'One or more dependencies have errors' })
    } else if (hasIdleOrLoadingDependency) {
      console.log(`Report summary ${id}: Dependencies are idle/loading, setting status to 'loading'`)
      // Always update to loading if dependencies are idle/loading, even if already loading
      // This ensures proper error-to-loading transitions
      if (componentState?.status !== 'loading') {
        reportManager.updateComponent(id, { status: 'loading', error: undefined })
      }
    } else if (allDependenciesLoaded) {
      console.log(`Report summary ${id}: All dependencies loaded, calling endpoint`)

      // Only call generateSummary if we're not already generating and status is not already 'loaded'
      // Get fresh component state to avoid TypeScript narrowing issues
      const freshComponentState = reportManager.components.get(id)
      if (!generationRef.current && freshComponentState?.status !== 'loaded' && freshComponentState?.status !== 'error') {
        console.log(`Report summary ${id}: Starting summary generation, current status: ${freshComponentState?.status}`)
        reportManager.updateComponent(id, { status: 'loading' })
        generateSummary()
      } else {
        console.log(`Report summary ${id}: Skipping generation - generationRef: ${generationRef.current}, status: ${freshComponentState?.status}`)
      }
    } else {
      console.log(`Report summary ${id}: Dependencies not ready yet - waiting`)
    }
  }, [
    id,
    dependencyIds,
    locked,
    preserved,
    reportManager,
    generateSummary,
    dependencyStatuses, // This will trigger when dependency statuses change
    // Note: We use dependencyStatuses instead of reportManager.components to prevent infinite loops
  ]);

  const handleMenuAction = useCallback((action: string) => {
    switch (action) {
      case 'refresh':
        if (!locked && !preserved) {
          generateSummary();
        }
        break;
      case 'delete':
        deleteNode();
        break;
      case 'configure':
        setConfigDialogOpen(true);
        break;
      case 'lock':
        updateAttributes({ locked: true });
        break;
      case 'preserve':
        updateAttributes({ preserved: true });
        break;
    }
  }, [generateSummary, locked, preserved, updateAttributes, deleteNode]);

  const handleConfigConfirm = useCallback((config: ReportComponentConfig) => {
    // Check if headings or depth changed to trigger reload
    const headingsChanged = config.headings !== node.attrs.headings
    const depthChanged = config.depth !== node.attrs.depth

    updateAttributes({
      id: config.id,
      title: config.title,
      prompt: config.prompt,
      summarize: config.summarize?.join(',') || '',
      headings: config.headings,
      depth: config.depth,
    });

    // If headings configuration changed, reset status to trigger reload
    if (headingsChanged || depthChanged) {
      console.log(`Report summary ${id}: Headings/depth configuration changed, resetting status to 'idle'`)
      reportManager.updateComponent(id, { status: 'idle' })
      updateAttributes({ status: 'idle' })
    }
  }, [updateAttributes, node.attrs.headings, node.attrs.depth, id, reportManager]);

  // Get component state for error handling
  const componentState = reportManager.components.get(id);

  // Debug: Log when component state changes
  useEffect(() => {
    console.log(`Report summary ${id}: Component state changed:`, componentState)
  }, [id, componentState])

  const getStatusIcon = () => {
    // Check for error state first
    if (componentState?.status === 'error') {
      const errorMessage = componentState.error || 'Unknown error occurred';
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <AlertTriangle className="w-3 h-3 text-red-600 cursor-help" />
            </TooltipTrigger>
            <TooltipContent side="top" className="max-w-xs">
              <p className="text-sm">{errorMessage}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    }

    // Show loading state when status is 'loading' (waiting for dependencies or generating)
    if (componentState?.status === 'loading') {
      return <Loader2 className="w-3 h-3 text-purple-600 animate-spin" />
    }

    // Legacy loading states (keep for backward compatibility)
    if (isWaitingForDependencies) {
      return <Loader2 className="w-3 h-3 text-purple-600 animate-spin" />;
    }
    if (isLoading) {
      return <RefreshCw className="w-3 h-3 text-purple-600 animate-spin" />;
    }
    if (locked) {
      return <Lock className="w-3 h-3 text-purple-600" />;
    }
    if (preserved) {
      return <Shield className="w-3 h-3 text-purple-600" />;
    }
    return null;
  };

  const getStatusText = () => {
    // Use component status to determine text
    if (componentState?.status === 'loading') {
      // Check if we're waiting for dependencies or actually generating
      const dependencies = dependencyIds.map((depId: string) => reportManager.components.get(depId)).filter(Boolean)
      const hasIdleOrLoadingDependency = dependencies.some((dep: any) => dep.status === 'idle' || dep.status === 'loading')

      if (hasIdleOrLoadingDependency) {
        return 'Waiting for dependencies...'
      } else {
        return 'Generating summary...'
      }
    }

    // Legacy states (keep for backward compatibility)
    if (isWaitingForDependencies) {
      return 'Waiting for dependencies...';
    }
    if (isLoading) {
      return 'Generating summary...';
    }
    return '';
  };

  return (
    <NodeViewWrapper className={cn(
      "report-summary relative border-2 border-purple-200 rounded-lg p-4 my-4",
      "bg-purple-50/30 backdrop-blur-sm",
      // Print mode: hide all visual decorations
      "print:border-none print:bg-transparent print:backdrop-blur-none print:p-0 print:m-0 print:rounded-none",
      (componentState?.status === 'loading' || isLoading || isWaitingForDependencies) && 'opacity-60',
    )}
    data-type="reportSummary"
    data-id={id || ''}
    data-status={componentState?.status || 'idle'}>
      {/* Summary Header with Controls */}
      <div className="absolute -top-3 left-4 flex items-center gap-2 bg-purple-100 px-3 py-1 rounded-full border border-purple-200 print:hidden">
        <GripVertical className="w-4 h-4 text-purple-600 cursor-grab" />
        <span className="text-sm font-medium text-purple-700">{id}</span>
        {getStatusIcon()}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 text-purple-600 hover:text-purple-800"
              data-testid="report-summary-menu-trigger"
            >
              <MoreVertical className="w-3 h-3" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem
              onClick={() => handleMenuAction('refresh')}
              disabled={locked || preserved || componentState?.status === 'loading' || isLoading || isWaitingForDependencies}
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleMenuAction('configure')}>
              <Settings className="w-4 h-4 mr-2" />
              Configure
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={() => handleMenuAction('lock')}
              disabled={locked}
            >
              <Lock className="w-4 h-4 mr-2" />
              Lock
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={() => handleMenuAction('preserve')}
              disabled={preserved}
            >
              <Shield className="w-4 h-4 mr-2" />
              Preserve
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleMenuAction('delete')} className="text-red-600">
              <Trash2 className="w-4 h-4 mr-2" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Status text */}
      {getStatusText() && (
        <div className="text-sm text-purple-600 mt-4 mb-2 italic print:hidden">
          {getStatusText()}
        </div>
      )}

      {title && hasNodeContent(node) && (
        <a id={id}>
          <span className="report-section-title block heading-3 mt-4">{title}</span>
        </a>
      )}

      {/* Show dependencies info with status */}
      {dependencyIds.length > 0 && (
        <div className="text-xs text-purple-500 mt-2 mb-2 print:hidden">
          Summarizes: {dependencyIds.map((depId: string) => {
          const dep = reportManager.components.get(depId)
          const status = dep?.status || 'unknown'
          const statusIcon = status === 'loaded' ? '✓' :
            status === 'loading' ? '⏳' :
              status === 'error' ? '❌' :
                status === 'idle' ? '⏸️' : '?'
          return `${depId}${statusIcon}`
        }).join(', ')}
        </div>
      )}

      <NodeViewContent className="content mt-2" />

      <ReportComponentDialog
        open={configDialogOpen}
        onOpenChange={setConfigDialogOpen}
        onConfirm={handleConfigConfirm}
        type="report-summary"
        initialConfig={{
          id,
          title,
          prompt,
          summarize: dependencyIds,
          headings: node.attrs.headings || 'remove',
          depth: node.attrs.depth || 1,
          type: 'report-summary',
        }}
        availableComponents={Array.from(reportManager.components.values()).map((comp: any) => ({
          id: comp.id,
          title: comp.title || comp.id,
          type: comp.type
        }))}
      />
    </NodeViewWrapper>
  );
});

ReportSummaryComponent.displayName = 'ReportSummaryComponent';

/* ------------------------------------------------------------------
 *  TipTap Report Summary Extension
 * ------------------------------------------------------------------*/
export const ReportSummaryExtension = Node.create({
  name: 'reportSummary',

  group: 'block',

  content: 'block*',

  addAttributes() {
    return {
      id: {
        default: '',
      },
      title: {
        default: '',
      },
      prompt: {
        default: '',
      },
      summarize: {
        default: '', // Comma-separated list of component IDs to summarize
      },
      locked: {
        default: false,
      },
      preserved: {
        default: false,
      },
      status: {
        default: 'idle',
      },
      headings: {
        default: 'remove',
      },
      depth: {
        default: 1,
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'report-summary',
        getAttrs: (element) => {
          if (typeof element === 'string') return false;
          return {
            id: element.getAttribute('id'),
            title: element.getAttribute('title'),
            prompt: element.getAttribute('prompt'),
            summarize: element.getAttribute('summarize'),
            locked: element.getAttribute('locked') === 'true',
            preserved: element.getAttribute('preserved') === 'true',
            status: element.getAttribute('status') || 'idle',
          };
        },
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return ['report-summary', { ...HTMLAttributes, 'data-type': 'reportSummary' }, 0];
  },

  addNodeView() {
    return ReactNodeViewRenderer(ReportSummaryComponent);
  },
});
