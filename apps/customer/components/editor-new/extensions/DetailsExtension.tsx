import { Node, mergeAttributes } from '@tiptap/core'
import { ReactNodeViewRenderer } from '@tiptap/react'
import React, { useState } from 'react'
import { NodeViewWrapper, NodeViewContent } from '@tiptap/react'
import { ChevronRight, ChevronDown } from 'lucide-react'

interface DetailsComponentProps {
  node: any
  updateAttributes: (attrs: any) => void
  deleteNode: () => void
}

const DetailsComponent: React.FC<DetailsComponentProps> = ({ 
  node, 
  updateAttributes 
}) => {
  const [isOpen, setIsOpen] = useState(node.attrs.open || false)

  const toggleOpen = () => {
    const newOpen = !isOpen
    setIsOpen(newOpen)
    updateAttributes({ open: newOpen })
  }

  return (
    <NodeViewWrapper className="details-wrapper">
      <details 
        open={isOpen}
        className="border border-gray-200 rounded-lg overflow-hidden my-4"
      >
        <summary 
          className="bg-gray-50 px-4 py-3 cursor-pointer flex items-center gap-2 hover:bg-gray-100 transition-colors"
          onClick={(e) => {
            e.preventDefault()
            toggleOpen()
          }}
        >
          {isOpen ? (
            <ChevronDown className="w-4 h-4 text-gray-600" />
          ) : (
            <ChevronRight className="w-4 h-4 text-gray-600" />
          )}
          <span 
            className="font-medium text-gray-900 flex-1"
            contentEditable
            suppressContentEditableWarning
            onBlur={(e) => {
              updateAttributes({ summary: e.target.textContent })
            }}
          >
            {node.attrs.summary || 'Click to edit summary...'}
          </span>
        </summary>
        <div className="p-4">
          <NodeViewContent className="details-content" />
        </div>
      </details>
    </NodeViewWrapper>
  )
}

export interface DetailsOptions {
  HTMLAttributes: Record<string, any>
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    details: {
      setDetails: () => ReturnType
      toggleDetails: () => ReturnType
      unsetDetails: () => ReturnType
    }
  }
}

export const DetailsExtension = Node.create<DetailsOptions>({
  name: 'details',

  addOptions() {
    return {
      HTMLAttributes: {},
    }
  },

  group: 'block',

  content: 'block+',

  defining: true,

  addAttributes() {
    return {
      open: {
        default: false,
        parseHTML: element => element.hasAttribute('open'),
        renderHTML: attributes => {
          if (!attributes.open) {
            return {}
          }
          return { open: '' }
        },
      },
      summary: {
        default: 'Details',
        parseHTML: element => {
          const summary = element.querySelector('summary')
          return summary?.textContent || 'Details'
        },
        renderHTML: () => ({}),
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'details',
        contentElement: 'div',
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return [
      'details',
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes),
      ['summary', {}, HTMLAttributes.summary || 'Details'],
      ['div', {}, 0],
    ]
  },

  addNodeView() {
    return ReactNodeViewRenderer(DetailsComponent)
  },

  addCommands() {
    return {
      setDetails:
        () =>
        ({ commands }) => {
          return commands.wrapIn(this.name)
        },
      toggleDetails:
        () =>
        ({ commands }) => {
          return commands.toggleWrap(this.name)
        },
      unsetDetails:
        () =>
        ({ commands }) => {
          return commands.lift(this.name)
        },
    }
  },

  addKeyboardShortcuts() {
    return {
      'Mod-Shift-d': () => this.editor.commands.toggleDetails(),
    }
  },
})

export default DetailsExtension
