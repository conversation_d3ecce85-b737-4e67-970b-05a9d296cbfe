import React from 'react'
import { Node } from '@tiptap/core'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ReactNodeViewRenderer } from '@tiptap/react'
import { DomainBadgeCitation } from '@/components/citation/domain-badge-citation'
import { CitationType } from '@/components/citation'
import { useDocumentContext } from '../../editor/context/DocumentContext'

/* ------------------------------------------------------------------
 *  Citation React Component
 * ------------------------------------------------------------------*/
interface CitationComponentProps {
  node: any;
  citations: CitationType[];
  admin: boolean;
}

const CitationComponent = React.memo<CitationComponentProps>(({ node, citations, admin }) => {
  const { state } = useDocumentContext()
  const footnoteId = parseInt(node.attrs.footnote || '0');
  const display = node.attrs.display;

  // Find the citation by footnote ID - memoized to prevent re-renders
  const citation = React.useMemo(() => {
    // First try static citations
    let found = citations.find(c =>
      c.doc_page_id === footnoteId || c.doc_id === footnoteId
    );

    // If not found, try DocumentContext citations
    if (!found) {
      found = state.citations.find(c =>
        c.doc_page_id === footnoteId || c.doc_id === footnoteId,
      )
    }

    return found
  }, [citations, state.citations, footnoteId]);

  // Simple citation numbering
  const citationNumber = React.useMemo(() => {
    if (!citation) return null

    // Try to find index in citations array
    const index = citations.findIndex(c => c.doc_page_id === citation.doc_page_id)
    if (index >= 0) {
      return index + 1
    }

    // Try DocumentContext citations
    const contextIndex = state.citations.findIndex(c => c.doc_page_id === citation.doc_page_id)
    if (contextIndex >= 0) {
      return contextIndex + 1
    }

    return 1 // Default
  }, [citation, citations, state.citations])

  if (!citation) {
    return (
      <NodeViewWrapper className="citation-error inline">
        <span className="text-red-500 text-xs">[Citation not found]</span>
      </NodeViewWrapper>
    );
  }

  return (
    <NodeViewWrapper className="citation-wrapper inline">
      <DomainBadgeCitation
        citation={citation}
        admin={admin}
        display={citationNumber || (display ? parseInt(display) : undefined)}
        showYear
      />
    </NodeViewWrapper>
  );
});

CitationComponent.displayName = 'CitationComponent';

/* ------------------------------------------------------------------
 *  TipTap Citation Extension
 * ------------------------------------------------------------------*/
export const CitationExtension = Node.create({
  name: 'domainBadgeCitation',

  group: 'inline',

  inline: true,

  atom: true,

  addOptions() {
    return {
      citations: [] as CitationType[],
      admin: false,
    };
  },

  addAttributes() {
    return {
      id: {
        default: null,
      },
      display: {
        default: null,
      },
      footnote: {
        default: null,
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'domain-badge-citation',
        getAttrs: (element) => {
          if (typeof element === 'string') return false;
          return {
            id: element.getAttribute('id'),
            display: element.getAttribute('display'),
            footnote: element.getAttribute('footnote'),
          };
        },
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return ['domain-badge-citation', HTMLAttributes];
  },

  addNodeView() {
    return ReactNodeViewRenderer((props) => (
      <CitationComponent
        {...props}
        citations={this.options.citations}
        admin={this.options.admin}
      />
    ));
  },
});
