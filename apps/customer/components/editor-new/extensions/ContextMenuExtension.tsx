import { Extension } from '@tiptap/core'
import { Plugin, PluginKey } from 'prosemirror-state'
import { EditorView } from 'prosemirror-view'

export interface ContextMenuOptions {
  onContextMenu?: (event: MouseEvent, view: EditorView) => void
  element?: HTMLElement
}

export const ContextMenuExtension = Extension.create<ContextMenuOptions>({
  name: 'contextMenu',

  addOptions() {
    return {
      onContextMenu: undefined,
      element: undefined,
    }
  },

  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey('contextMenu'),
        props: {
          handleDOMEvents: {
            contextmenu: (view, event) => {
              // Prevent the default browser context menu
              event.preventDefault()
              
              // Call the custom context menu handler if provided
              if (this.options.onContextMenu) {
                this.options.onContextMenu(event, view)
              }
              
              return true
            },
          },
        },
      }),
    ]
  },
})

export default ContextMenuExtension
