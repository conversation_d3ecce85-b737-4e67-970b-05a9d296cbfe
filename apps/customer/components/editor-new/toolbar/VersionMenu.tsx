'use client'

import React, { useState } from 'react'
import { Editor } from '@tiptap/react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@ui/components/ui/dropdown-menu'
import { Button } from '@ui/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@ui/components/ui/dialog'
import { Input } from '@ui/components/ui/input'
import { Label } from '@ui/components/ui/label'
import { Textarea } from '@ui/components/ui/textarea'
import { ChevronDown, Clock, History, Plus } from 'lucide-react'
import { useToast } from '@ui/hooks/use-toast'
import { createClient } from '@/app/supabase/client'

interface VersionMenuProps {
  editor: Editor
  documentId: string
  onOpenHistory?: () => void
}

export function VersionMenu({ editor, documentId, onOpenHistory }: VersionMenuProps) {
  const [createDialogOpen, setCreateDialogOpen] = useState(false)
  const [versionTitle, setVersionTitle] = useState('')
  const [versionSummary, setVersionSummary] = useState('')
  const [isCreating, setIsCreating] = useState(false)
  
  const { toast } = useToast()
  const supabase = createClient()

  const handleCreateVersion = async () => {
    if (!versionTitle.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter a version title',
        variant: 'destructive'
      })
      return
    }

    setIsCreating(true)
    try {
      // Get the current editor content
      const content = editor.getHTML()
      const data = editor.getJSON()
      
      // Get the latest version number
      const { data: latestVersion } = await supabase
        .from('doc_versions')
        .select('version_number')
        .eq('document_id', documentId)
        .order('version_number', { ascending: false })
        .limit(1)
        .single()
      
      const nextVersionNumber = (latestVersion?.version_number || 0) + 1
      
      // Get current user
      const { data: { user } } = await supabase.auth.getUser()
      
      // Create the new version
      const { error } = await supabase
        .from('doc_versions')
        .insert({
          document_id: documentId,
          version_number: nextVersionNumber,
          title: versionTitle,
          content: content,
          data: data,
          created_by: user?.id,
          change_summary: versionSummary || `Version ${nextVersionNumber}: ${versionTitle}`,
          is_auto_save: false
        })
      
      if (error) {
        throw error
      }
      
      toast({
        title: 'Success',
        description: 'Version created successfully',
        'data-testid': 'version-created-toast'
      } as any)
      
      setCreateDialogOpen(false)
      setVersionTitle('')
      setVersionSummary('')
    } catch (error) {
      console.error('Error creating version:', error)
      toast({
        title: 'Error',
        description: 'Failed to create version',
        variant: 'destructive'
      })
    } finally {
      setIsCreating(false)
    }
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            data-testid="version-menu-trigger"
          >
            <Clock className="w-4 h-4 mr-1" />
            Version
            <ChevronDown className="w-3 h-3 ml-1" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem
            onClick={() => setCreateDialogOpen(true)}
            data-testid="create-version"
          >
            <Plus className="w-4 h-4 mr-2" />
            Create Version
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            onClick={onOpenHistory}
            data-testid="version-history"
          >
            <History className="w-4 h-4 mr-2" />
            Version History
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create Version</DialogTitle>
            <DialogDescription>
              Save the current state of your document as a named version
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="version-title">Version Title</Label>
              <Input
                id="version-title"
                placeholder="e.g., Draft v1, Final Review"
                value={versionTitle}
                onChange={(e) => setVersionTitle(e.target.value)}
                data-testid="version-title-input"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="version-summary">Summary (optional)</Label>
              <Textarea
                id="version-summary"
                placeholder="Describe the changes in this version..."
                value={versionSummary}
                onChange={(e) => setVersionSummary(e.target.value)}
                rows={3}
                data-testid="version-summary"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setCreateDialogOpen(false)}
              disabled={isCreating}
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreateVersion}
              disabled={isCreating}
              data-testid="create-version-confirm"
            >
              {isCreating ? 'Creating...' : 'Create Version'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
