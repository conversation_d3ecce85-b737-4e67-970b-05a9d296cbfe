'use client'

import React, { Component, ErrorInfo, ReactNode } from 'react'
import { Button } from '@ui/components/ui/button'

interface Props {
  children: ReactNode
}

interface State {
  hasError: boolean
  error: Error | null
}

export class EditorErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false, error: null }
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Editor error boundary caught an error:', error, errorInfo)
  }

  handleReloadEditor = () => {
    this.setState({ hasError: false, error: null })
  }

  handleReportIssue = () => {
    // In a real app, this would send error data to a reporting service
    console.log('Report issue:', this.state.error)
    window.open('https://github.com/ekointelligence/mono-repo/issues', '_blank')
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center p-8">
            <h2 className="text-xl font-semibold mb-2">Something went wrong</h2>
            <p className="text-muted-foreground mb-4">
              The editor encountered an unexpected error.
            </p>
            <div className="flex gap-2 justify-center">
              <Button 
                variant="outline" 
                onClick={this.handleReloadEditor}
              >
                Reload Editor
              </Button>
              <Button 
                variant="outline" 
                onClick={this.handleReportIssue}
              >
                Report Issue
              </Button>
            </div>
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="mt-4 text-left">
                <summary className="cursor-pointer text-sm text-muted-foreground">
                  Error Details (Development Only)
                </summary>
                <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto">
                  {this.state.error.stack}
                </pre>
              </details>
            )}
          </div>
        </div>
      )
    }

    return this.props.children
  }
}