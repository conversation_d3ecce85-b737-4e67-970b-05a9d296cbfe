/**
 * Utility for serializing TipTap/ProseMirror schemas to JSON-safe format
 * Handles circular references and functions that can't be serialized
 */

export interface SerializedSchema {
  nodes: Record<string, any>
  marks: Record<string, any>

  [key: string]: any
}

/**
 * Serializes a TipTap/ProseMirror schema to a JSON-safe format
 * Uses a custom replacer to handle circular references and functions
 *
 * @param schema - The TipTap/ProseMirror schema object
 * @returns Serialized schema or null if serialization fails
 */
export function serializeSchema(schema: any): SerializedSchema | null {
  if (!schema) return null

  try {
    // Create a custom replacer to handle circular references and functions
    const seen = new WeakSet()
    const replacer = (key: string, value: any) => {
      // Skip functions as they can't be serialized
      if (typeof value === 'function') {
        return '[Function]'
      }

      // Handle circular references
      if (typeof value === 'object' && value !== null) {
        if (seen.has(value)) {
          return '[Circular]'
        }
        seen.add(value)
      }

      return value
    }

    // Serialize the full schema with circular reference handling
    const serializedSchema = JSON.parse(JSON.stringify(schema, replacer))

    return serializedSchema
  } catch (error) {
    console.warn('Failed to serialize schema:', error)
    return null
  }
}

/**
 * Validates that a serialized schema has the expected structure
 *
 * @param serializedSchema - The serialized schema to validate
 * @returns true if valid, false otherwise
 */
export function validateSerializedSchema(serializedSchema: any): serializedSchema is SerializedSchema {
  if (!serializedSchema || typeof serializedSchema !== 'object') {
    return false
  }

  // Check for required properties
  if (!serializedSchema.nodes || typeof serializedSchema.nodes !== 'object') {
    return false
  }

  if (!serializedSchema.marks || typeof serializedSchema.marks !== 'object') {
    return false
  }

  return true
}

/**
 * Gets schema information for debugging/logging
 *
 * @param serializedSchema - The serialized schema
 * @returns Object with schema metadata
 */
export function getSchemaInfo(serializedSchema: SerializedSchema | null) {
  if (!serializedSchema) {
    return {
      hasSchema: false,
      nodeCount: 0,
      markCount: 0,
      nodeNames: [],
      markNames: [],
    }
  }

  const nodeNames = Object.keys(serializedSchema.nodes || {})
  const markNames = Object.keys(serializedSchema.marks || {})

  return {
    hasSchema: true,
    nodeCount: nodeNames.length,
    markCount: markNames.length,
    nodeNames,
    markNames,
  }
}
