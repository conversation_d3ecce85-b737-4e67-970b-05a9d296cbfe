/**
 * Utilities for transforming markdown headings based on report component configuration
 */

export type HeadingTransform = 'keep' | 'remove' | 'reset'

/**
 * Remove all markdown headings from text
 * @param markdown The markdown text to process
 * @returns Markdown with all headings removed
 */
export function removeHeadings(markdown: string): string {
  return markdown.replace(/^\s*#{1,6}\s.*$/gm, '')
}

/**
 * Keep headings unchanged
 * @param markdown The markdown text to process
 * @returns Original markdown unchanged
 */
export function keepHeadings(markdown: string): string {
  return markdown
}

/**
 * Reset heading levels to start from a specific depth
 * @param markdown The markdown text to process
 * @param targetDepth The target depth (1-6) to start headings from
 * @returns Markdown with heading levels adjusted
 */
export function resetHeadingDepth(markdown: string, targetDepth: number = 1): string {
  // Validate target depth
  if (targetDepth < 1 || targetDepth > 6) {
    console.warn(`Invalid target depth ${targetDepth}, using 1`)
    targetDepth = 1
  }

  // Find all headings and their levels
  const headingRegex = /^(\s*)(#{1,6})(\s+.*)$/gm
  const headings: Array<{ match: string; level: number; content: string; indent: string }> = []
  
  let match
  while ((match = headingRegex.exec(markdown)) !== null) {
    headings.push({
      match: match[0],
      level: match[2].length,
      content: match[3],
      indent: match[1]
    })
  }

  if (headings.length === 0) {
    return markdown
  }

  // Find the minimum heading level to use as baseline
  const minLevel = Math.min(...headings.map(h => h.level))
  
  // Calculate the offset needed to start from target depth
  const levelOffset = targetDepth - minLevel

  // Replace headings with adjusted levels
  let result = markdown
  headings.forEach(heading => {
    const newLevel = Math.max(1, Math.min(6, heading.level + levelOffset))
    const newHeading = `${heading.indent}${'#'.repeat(newLevel)}${heading.content}`
    result = result.replace(heading.match, newHeading)
  })

  return result
}

/**
 * Count the number of parent report-groups for a given position in the editor
 * @param editor The TipTap editor instance
 * @param pos The position in the document
 * @returns Number of parent report-groups (depth)
 */
export function calculateReportGroupDepth(editor: any, pos: number): number {
  if (!editor || pos === undefined) return 0

  try {
    const resolvedPos = editor.state.doc.resolve(pos)
    let depth = 0

    // Walk up the document tree to count parent report-groups
    for (let docDepth = resolvedPos.depth; docDepth >= 0; docDepth--) {
      const node = resolvedPos.node(docDepth)
      if (node.type.name === 'reportGroup') {
        depth++
      }
    }

    return depth
  } catch (error) {
    console.warn('Error calculating report group depth:', error)
    return 0
  }
}

/**
 * Apply heading transformation based on the specified transform type
 * @param markdown The markdown text to process
 * @param transform The type of transformation to apply
 * @param depth The depth to use for 'reset' transformation (defaults to 1)
 * @returns Transformed markdown
 */
export function transformHeadings(
  markdown: string, 
  transform: HeadingTransform, 
  depth: number = 1
): string {
  switch (transform) {
    case 'keep':
      return keepHeadings(markdown)
    case 'remove':
      return removeHeadings(markdown)
    case 'reset':
      return resetHeadingDepth(markdown, depth)
    default:
      console.warn(`Unknown heading transform: ${transform}, defaulting to 'remove'`)
      return removeHeadings(markdown)
  }
}
