## Common Patterns

The editor follows consistent architectural and design patterns that ensure maintainable, performant, and scalable code. Understanding these patterns is essential for effective development and extension of the editor system.

### 1. Architectural Patterns

#### Context Provider Pattern
The editor uses a layered context architecture where each context has a single responsibility and well-defined boundaries:

```typescript
// Provider composition pattern - contexts are composed hierarchically
export function EditorProvider({ children, reportId, onSave }: EditorProviderProps) {
  return (
    <DocumentProvider reportId={reportId} onSave={onSave}> {/* Note: Using deprecated DocumentContext */}
      <ComponentProvider onComponentStatusChange={onComponentStatusChange}>
        <DependencyProvider getComponentStatus={getComponentStatus}>
          <VersioningProvider documentId={reportId} editor={null}>
            {children}
          </VersioningProvider>
        </DependencyProvider>
      </ComponentProvider>
    </DocumentProvider>
  )
}

// Each context provides focused functionality
const DocumentProvider = ({ children, reportId, onSave }) => {
  // Document state management only
  const [state, dispatch] = useReducer(documentReducer, initialState)
  
  // Memoized context value to prevent unnecessary re-renders
  const contextValue = useMemo(() => ({
    state,
    dispatch,
    saveDocument: useCallback(async (content, data) => {
      // Business logic delegated to service layer
      await reportService.saveReport(reportId, content, data)
    }, [reportId])
  }), [state, dispatch, reportId])
  
  return (
    <DocumentContext.Provider value={contextValue}>
      {children}
    </DocumentContext.Provider>
  )
}
```

#### Service Layer Pattern
All business logic is encapsulated in service classes, completely separated from UI components:

```typescript
// Service classes handle all business operations
export class ReportService {
  private supabase = createClient()

  async saveReport(documentId: string, content: string, data: any): Promise<void> {
    logger.info('ReportService', 'saveReport', `Saving report ${documentId}`)
    
    try {
      const { error } = await this.supabase
        .from('documents')
        .update({ content, data, updated_at: new Date().toISOString() })
        .eq('id', documentId)

      if (error) {
        logger.error('ReportService', 'saveReport', `Failed to save report ${documentId}`, error)
        throw new Error(`Failed to save report: ${error.message}`)
      }

      logger.info('ReportService', 'saveReport', `Successfully saved report ${documentId}`)
    } catch (error) {
      logger.error('ReportService', 'saveReport', `Error saving report ${documentId}`, error as Error)
      throw error
    }
  }
}

// Components use services through clean interfaces
const MyComponent = () => {
  const handleSave = useCallback(async () => {
    try {
      await reportService.saveReport(documentId, content, data)
      setStatus('saved')
    } catch (error) {
      setStatus('error')
      logger.error('MyComponent', 'handleSave', 'Save failed', error)
    }
  }, [documentId, content, data])
}
```

#### State Machine Pattern
Component status transitions are managed by a strict state machine that prevents invalid state transitions:

```typescript
// State machine enforces valid transitions and conditions
class ComponentStateMachine {
  private transitions: StatusTransition[] = [
    {
      from: COMPONENT_STATUS.REGISTERING,
      to: COMPONENT_STATUS.WAITING,
      condition: (ctx) => Boolean(ctx.dependencies?.length)
    },
    {
      from: COMPONENT_STATUS.WAITING,
      to: COMPONENT_STATUS.LOADING
    }
  ]

  canTransition(from: ComponentStatus, to: ComponentStatus, context: ComponentContext): boolean {
    const transition = this.transitions.find(t => t.from === from && t.to === to)
    
    if (!transition) return false
    if (transition.condition && !transition.condition(context)) return false
    
    return true
  }

  transition(from: ComponentStatus, to: ComponentStatus, context: ComponentContext): ComponentStatus {
    if (!this.canTransition(from, to, context)) {
      logger.error('StateMachine', 'transition', 
        `Invalid transition from ${from} to ${to} for component ${context.componentId}`)
      return from // Stay in current state
    }
    
    // Execute side effects and return new state
    return to
  }
}

// Components use state machine for safe transitions
const updateComponentStatus = useCallback((componentId: string, newStatus: ComponentStatus) => {
  const component = components.get(componentId)
  if (!component) return

  const context = { componentId, componentType: component.type }
  const validatedStatus = componentStateMachine.transition(component.status, newStatus, context)
  
  if (validatedStatus !== component.status) {
    setComponents(prev => new Map(prev.set(componentId, { ...component, status: validatedStatus })))
  }
}, [components])
```

### 2. Component Communication Patterns

#### Event-Driven Communication
Components communicate through well-defined events and callbacks, maintaining loose coupling:

```typescript
// Publisher-subscriber pattern for component communication
interface ComponentEvents {
  'status-changed': { componentId: string; status: ComponentStatus }
  'dependency-resolved': { componentId: string; dependencyId: string }
  'content-updated': { componentId: string; content: string }
}

class ComponentEventBus {
  private listeners = new Map<keyof ComponentEvents, Set<Function>>()

  emit<K extends keyof ComponentEvents>(event: K, data: ComponentEvents[K]): void {
    const eventListeners = this.listeners.get(event)
    if (eventListeners) {
      eventListeners.forEach(listener => {
        try {
          listener(data)
        } catch (error) {
          logger.error('ComponentEventBus', 'emit', `Event listener error for ${event}`, error)
        }
      })
    }
  }

  on<K extends keyof ComponentEvents>(event: K, listener: (data: ComponentEvents[K]) => void): () => void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set())
    }
    
    this.listeners.get(event)!.add(listener)
    
    // Return unsubscribe function
    return () => {
      this.listeners.get(event)?.delete(listener)
    }
  }
}

// Components emit events for status changes
const ReportSectionComponent = ({ node, updateAttributes }) => {
  const eventBus = useContext(ComponentEventBusContext)
  
  const updateStatus = useCallback((newStatus: ComponentStatus) => {
    updateAttributes({ status: newStatus })
    eventBus.emit('status-changed', { componentId: node.attrs.id, status: newStatus })
  }, [node.attrs.id, updateAttributes, eventBus])
}
```

#### Dependency Injection Pattern
Services and utilities are injected through context providers, enabling testability and modularity:

```typescript
// Service provider pattern with dependency injection
interface EditorServices {
  reportService: ReportService
  versionService: VersionService
  performanceMonitor: PerformanceMonitor
  memoryManager: MemoryManager
}

const EditorServicesContext = createContext<EditorServices | null>(null)

export const EditorServicesProvider = ({ children }: { children: React.ReactNode }) => {
  // Services are instantiated once and shared across components
  const services = useMemo<EditorServices>(() => ({
    reportService: new ReportService(),
    versionService: new VersionService(),
    performanceMonitor: new PerformanceMonitor(),
    memoryManager: new MemoryManager()
  }), [])

  // Cleanup services on unmount
  useEffect(() => {
    return () => {
      services.memoryManager.cleanupAll()
      services.performanceMonitor.reset()
    }
  }, [services])

  return (
    <EditorServicesContext.Provider value={services}>
      {children}
    </EditorServicesContext.Provider>
  )
}

// Components consume services through custom hooks
export const useReportService = () => {
  const services = useContext(EditorServicesContext)
  if (!services) {
    throw new Error('useReportService must be used within EditorServicesProvider')
  }
  return services.reportService
}
```

### 3. Data Flow and State Management Patterns

#### Reducer Pattern with Actions
State updates follow a predictable reducer pattern with typed actions:

```typescript
// Typed action system ensures type safety and predictability
export type DocumentAction =
  | { type: 'EDITOR_CREATED'; editor: Editor }
  | { type: 'CONTENT_CHANGED'; content: string; data: any; source: 'user' | 'system' | 'restore' }
  | { type: 'COMPONENT_REGISTERED'; component: ReportComponent }
  | { type: 'COMPONENT_UPDATED'; id: string; updates: Partial<ReportComponent> }

function documentReducer(state: DocumentState, action: DocumentAction): DocumentState {
  console.log(`[REDUCER] Action: ${action.type}`, action)
  
  switch (action.type) {
    case 'CONTENT_CHANGED':
      return {
        ...state,
        content: action.content,
        data: action.data,
        isDirty: action.source === 'user' // Only user changes mark as dirty
      }
    
    case 'COMPONENT_REGISTERED':
      const newComponents = new Map(state.components)
      newComponents.set(action.component.id, action.component)
      return {
        ...state,
        components: newComponents
      }
    
    default:
      return state
  }
}

// Actions are dispatched through context
const { dispatch } = useDocumentContext()

const registerComponent = useCallback((component: ReportComponent) => {
  dispatch({ type: 'COMPONENT_REGISTERED', component })
}, [dispatch])
```

#### Memoization and Performance Optimization
All expensive computations and context values are properly memoized:

```typescript
// Context values are memoized to prevent unnecessary re-renders
const ComponentProvider = ({ children, onComponentStatusChange }) => {
  const [components, setComponents] = useState(new Map<string, ReportComponent>())
  
  // Memoize expensive operations
  const registerComponent = useCallback((component: ReportComponent) => {
    setComponents(prev => {
      const newMap = new Map(prev)
      newMap.set(component.id, component)
      return newMap
    })
    
    // Performance tracking
    performanceMonitor.recordComponentRegistration(component.id, component.type)
  }, [])

  const updateComponentStatus = useCallback((componentId: string, status: ComponentStatus) => {
    setComponents(prev => {
      const component = prev.get(componentId)
      if (!component || component.status === status) return prev
      
      const newMap = new Map(prev)
      newMap.set(componentId, { ...component, status })
      return newMap
    })
    
    onComponentStatusChange?.(componentId, status)
  }, [onComponentStatusChange])

  // Memoized context value prevents child re-renders
  const contextValue = useMemo(() => ({
    components,
    registerComponent,
    updateComponentStatus,
    // Derived state is also memoized
    componentCount: components.size,
    loadedComponents: Array.from(components.values()).filter(c => c.status === 'loaded')
  }), [components, registerComponent, updateComponentStatus])

  return (
    <ComponentContext.Provider value={contextValue}>
      {children}
    </ComponentContext.Provider>
  )
}
```

### 4. Error Handling and Recovery Patterns

#### Error Boundary Pattern
Components are wrapped in error boundaries that provide graceful degradation:

```typescript
// Error boundaries catch and handle component errors gracefully
export class EditorErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false, error: null }
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error with context
    logger.error('EditorErrorBoundary', 'componentDidCatch', 'Editor error caught', error)
    
    // Report to monitoring service
    if (process.env.NODE_ENV === 'production') {
      // Send to error monitoring service
      errorReportingService.report(error, errorInfo)
    }
  }

  handleReloadEditor = () => {
    this.setState({ hasError: false, error: null })
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center p-8">
            <h2 className="text-xl font-semibold mb-2">Something went wrong</h2>
            <p className="text-muted-foreground mb-4">
              The editor encountered an unexpected error.
            </p>
            <Button onClick={this.handleReloadEditor}>
              Reload Editor
            </Button>
            {/* Development error details */}
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="mt-4 text-left">
                <summary>Error Details</summary>
                <pre className="text-xs bg-gray-100 p-2 rounded">
                  {this.state.error.stack}
                </pre>
              </details>
            )}
          </div>
        </div>
      )
    }

    return this.props.children
  }
}
```

#### Service-Level Error Handling
Services implement consistent error handling with logging and recovery:

```typescript
// Services provide consistent error handling and recovery patterns
export class ReportService {
  async loadReport(documentId: string): Promise<{ content: string; data: any } | null> {
    logger.info('ReportService', 'loadReport', `Loading report ${documentId}`)
    
    try {
      const { data: document, error } = await this.supabase
        .from('documents')
        .select('content, data')
        .eq('id', documentId)
        .single()

      if (error) {
        // Handle specific error cases
        if (error.code === 'PGRST116') {
          logger.warn('ReportService', 'loadReport', `Report ${documentId} not found`)
          return null // Graceful handling of missing documents
        }
        
        logger.error('ReportService', 'loadReport', `Database error loading report ${documentId}`, error)
        throw new Error(`Failed to load report: ${error.message}`)
      }

      logger.info('ReportService', 'loadReport', `Successfully loaded report ${documentId}`)
      return { content: document.content, data: document.data }
      
    } catch (error) {
      // Re-throw with context for upper layers
      logger.error('ReportService', 'loadReport', `Error loading report ${documentId}`, error as Error)
      throw error
    }
  }
}

// Components handle service errors gracefully
const DocumentEditor = ({ documentId }) => {
  const [error, setError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  
  useEffect(() => {
    const loadDocument = async () => {
      try {
        setError(null)
        setIsLoading(true)
        
        const document = await reportService.loadReport(documentId)
        if (!document) {
          setError('Document not found')
          return
        }
        
        // Set document content
        setContent(document.content)
        setData(document.data)
        
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to load document'
        setError(errorMessage)
        logger.error('DocumentEditor', 'loadDocument', 'Document loading failed', error)
        
      } finally {
        setIsLoading(false)
      }
    }
    
    loadDocument()
  }, [documentId])
  
  if (error) {
    return <ErrorDisplay error={error} onRetry={() => window.location.reload()} />
  }
  
  if (isLoading) {
    return <LoadingSpinner />
  }
  
  return <EditorContent />
}
```

### 5. Performance Optimization Patterns

#### Debounced Operations
Frequent operations like status updates are debounced to prevent performance issues:

```typescript
// Debounced operations prevent excessive updates and improve performance
export class GroupStatusManager {
  private debouncedUpdates = new Map<string, NodeJS.Timeout>()
  
  updateGroupStatus(groupId: string): void {
    // Clear existing timeout
    const existingTimeout = this.debouncedUpdates.get(groupId)
    if (existingTimeout) {
      clearTimeout(existingTimeout)
    }
    
    // Debounce group status calculation
    const timeout = setTimeout(() => {
      this.calculateGroupStatus(groupId)
      this.debouncedUpdates.delete(groupId)
    }, 50) // 50ms debounce
    
    this.debouncedUpdates.set(groupId, timeout)
  }
  
  private calculateGroupStatus(groupId: string): void {
    performanceMonitor.timeFunction('calculateGroupStatus', () => {
      // Expensive group status calculation
      const children = this.getGroupChildren(groupId)
      const status = this.deriveStatusFromChildren(children)
      
      this.options.onGroupStatusChange?.(groupId, status)
      performanceMonitor.recordGroupUpdate(groupId, status)
    })
  }
}

// Auto-save operations are debounced to prevent excessive saves
export function useAutoSave({ editor, documentId, saveInterval = 30000 }) {
  const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  
  const scheduleAutoSave = useCallback(() => {
    // Clear existing timeout
    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current)
    }

    // Schedule new save with debouncing
    saveTimeoutRef.current = setTimeout(() => {
      if (hasContentChanged()) {
        saveDocument(false) // Auto-save without version creation
      }
    }, saveInterval)
  }, [hasContentChanged, saveDocument, saveInterval])

  useEffect(() => {
    if (!editor) return

    const handleUpdate = () => {
      scheduleAutoSave() // Debounced save on each update
    }

    editor.on('update', handleUpdate)
    return () => editor.off('update', handleUpdate)
  }, [editor, scheduleAutoSave])
}
```

#### Memory Management
Resources are tracked and cleaned up systematically to prevent memory leaks:

```typescript
// Memory management ensures proper cleanup of resources
export class MemoryManager {
  private resources = new Map<string, ResourceTracker>()
  
  register(id: string, type: 'timeout' | 'interval' | 'listener', cleanup: CleanupFunction): void {
    // Clean up existing resource if re-registering
    if (this.resources.has(id)) {
      this.cleanup(id)
    }

    this.resources.set(id, { id, type, cleanup, created: new Date() })
    logger.debug('MemoryManager', 'register', `Registered ${type} resource: ${id}`)
  }

  cleanup(id: string): void {
    const resource = this.resources.get(id)
    if (resource) {
      try {
        resource.cleanup()
        this.resources.delete(id)
        logger.debug('MemoryManager', 'cleanup', `Cleaned up resource: ${id}`)
      } catch (error) {
        logger.error('MemoryManager', 'cleanup', `Failed to cleanup resource: ${id}`, error)
      }
    }
  }

  cleanupAll(): void {
    for (const [id] of this.resources) {
      this.cleanup(id)
    }
  }
}

// Components use memory manager for automatic cleanup
const MyComponent = () => {
  const memoryManager = useContext(MemoryManagerContext)
  
  useEffect(() => {
    // Register timeout for cleanup
    const timeoutId = setTimeout(() => {
      // Some delayed operation
    }, 5000)
    
    memoryManager.registerTimeout('my-component-timeout', timeoutId)
    
    // Register event listener for cleanup
    const handleResize = () => {
      // Handle resize
    }
    
    window.addEventListener('resize', handleResize)
    memoryManager.registerListener('my-component-resize', window, 'resize', handleResize)
    
    return () => {
      // Cleanup is handled automatically by MemoryManager
      memoryManager.cleanup('my-component-timeout')
      memoryManager.cleanup('my-component-resize')
    }
  }, [memoryManager])
}
```

### 6. Testing Patterns and Strategies

#### Service Layer Testing
Business logic in services is thoroughly tested with mock dependencies:

```typescript
// Service testing with mocked dependencies
describe('ReportService', () => {
  let reportService: ReportService
  let mockSupabase: jest.Mocked<SupabaseClient>

  beforeEach(() => {
    mockSupabase = {
      from: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      single: jest.fn(),
      update: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis()
    } as any

    reportService = new ReportService(mockSupabase)
  })

  test('should save report successfully', async () => {
    // Arrange
    const documentId = 'doc-123'
    const content = '<p>Test content</p>'
    const data = { nodes: [] }
    
    mockSupabase.single.mockResolvedValue({ data: {}, error: null })

    // Act
    await reportService.saveReport(documentId, content, data)

    // Assert
    expect(mockSupabase.from).toHaveBeenCalledWith('documents')
    expect(mockSupabase.update).toHaveBeenCalledWith({
      content,
      data,
      updated_at: expect.any(String)
    })
    expect(mockSupabase.eq).toHaveBeenCalledWith('id', documentId)
  })

  test('should handle save errors gracefully', async () => {
    // Arrange
    const error = new Error('Database connection failed')
    mockSupabase.single.mockResolvedValue({ data: null, error })

    // Act & Assert
    await expect(reportService.saveReport('doc-123', 'content', {}))
      .rejects.toThrow('Failed to save report: Database connection failed')
  })
})
```

#### State Machine Testing
State transitions are verified to ensure system integrity:

```typescript
// State machine testing ensures valid transitions
describe('ComponentStateMachine', () => {
  test('should allow valid status transitions', () => {
    const context = { componentId: 'comp-1', componentType: 'report-section' }
    
    // Test initial registration
    expect(componentStateMachine.canTransition('unregistered', 'registering', context)).toBe(true)
    
    // Test dependency-based transitions
    const contextWithDeps = { ...context, dependencies: ['dep-1'] }
    expect(componentStateMachine.canTransition('registering', 'waiting', contextWithDeps)).toBe(true)
    expect(componentStateMachine.canTransition('registering', 'loading', contextWithDeps)).toBe(false)
    
    const contextNoDeps = { ...context, dependencies: [] }
    expect(componentStateMachine.canTransition('registering', 'loading', contextNoDeps)).toBe(true)
    expect(componentStateMachine.canTransition('registering', 'waiting', contextNoDeps)).toBe(false)
  })

  test('should prevent invalid transitions', () => {
    const context = { componentId: 'comp-1', componentType: 'report-section' }
    
    // Invalid transitions should be rejected
    expect(componentStateMachine.canTransition('loaded', 'registering', context)).toBe(false)
    expect(componentStateMachine.canTransition('error', 'loaded', context)).toBe(false)
  })

  test('should execute transition side effects', () => {
    const mockCallback = jest.fn()
    const context = { componentId: 'comp-1', componentType: 'report-section' }
    
    // Mock side effect for transition
    const originalTransition = componentStateMachine.transition
    componentStateMachine.transition = jest.fn().mockImplementation((from, to, ctx) => {
      if (from === 'loading' && to === 'loaded') {
        mockCallback(ctx)
      }
      return originalTransition.call(componentStateMachine, from, to, ctx)
    })
    
    componentStateMachine.transition('loading', 'loaded', context)
    expect(mockCallback).toHaveBeenCalledWith(context)
  })
})
```

#### Component Integration Testing
Components are tested with realistic contexts and user interactions:

```typescript
// Integration testing with realistic contexts
describe('ReportSectionComponent', () => {
  let mockDocumentContext: any
  let mockReportManager: any

  beforeEach(() => {
    mockDocumentContext = {
      state: {
        components: new Map(),
        currentEntity: 'ENT-123',
        currentRun: 'latest'
      },
      dispatch: jest.fn()
    }

    mockReportManager = {
      registerComponent: jest.fn(),
      updateComponentStatus: jest.fn(),
      loadComponent: jest.fn().mockResolvedValue('Loaded content')
    }
  })

  test('should register component on mount', () => {
    // Arrange
    const node = {
      attrs: { id: 'section-1', title: 'Test Section', endpoint: '/api/test' }
    }
    
    // Act
    render(
      <DocumentContextProvider value={mockDocumentContext}>
        <ReportManagerProvider value={mockReportManager}>
          <ReportSectionComponent node={node} updateAttributes={jest.fn()} />
        </ReportManagerProvider>
      </DocumentContextProvider>
    )

    // Assert
    expect(mockReportManager.registerComponent).toHaveBeenCalledWith({
      id: 'section-1',
      type: 'report-section',
      title: 'Test Section',
      endpoint: '/api/test',
      status: 'idle'
    })
  })

  test('should handle component loading and content update', async () => {
    // Test component loading lifecycle
    const updateAttributes = jest.fn()
    const node = { attrs: { id: 'section-1', endpoint: '/api/test' } }
    
    const { getByText } = render(
      <DocumentContextProvider value={mockDocumentContext}>
        <ReportManagerProvider value={mockReportManager}>
          <ReportSectionComponent node={node} updateAttributes={updateAttributes} />
        </ReportManagerProvider>
      </DocumentContextProvider>
    )

    // Trigger load
    fireEvent.click(getByText('Load'))
    
    // Verify loading state
    expect(mockReportManager.updateComponentStatus).toHaveBeenCalledWith('section-1', 'loading')
    
    // Wait for content load
    await waitFor(() => {
      expect(updateAttributes).toHaveBeenCalledWith({ 
        status: 'loaded',
        content: 'Loaded content'
      })
    })
  })
})
```

### 7. Configuration and Customization Patterns

#### Extension Configuration Pattern
Extensions follow a consistent configuration pattern for options and behavior:

```typescript
// Extension configuration pattern
export const ReportSectionExtension = Node.create({
  name: 'reportSection',
  
  addOptions() {
    return {
      // Default configuration options
      allowedTypes: ['report-section', 'report-group'],
      autoLoad: true,
      preserveOnRefresh: false,
      debounceDelay: 300,
      
      // Callback configurations
      onComponentLoad: null,
      onComponentError: null,
      onStatusChange: null
    }
  },

  addAttributes() {
    return {
      id: { default: null },
      title: { default: '' },
      endpoint: { default: null },
      status: { default: 'idle' },
      locked: { default: false, parseHTML: element => element.hasAttribute('data-locked') },
      preserved: { default: false, parseHTML: element => element.hasAttribute('data-preserved') }
    }
  },

  addCommands() {
    return {
      insertReportSection: (attributes) => ({ commands }) => {
        return commands.insertContent({
          type: this.name,
          attrs: {
            id: generateId(),
            ...attributes
          }
        })
      },
      
      updateReportSection: (id, updates) => ({ tr, state }) => {
        // Find and update the node
        let nodePos: number | null = null
        
        state.doc.descendants((node, pos) => {
          if (node.type.name === this.name && node.attrs.id === id) {
            nodePos = pos
            return false
          }
        })
        
        if (nodePos !== null) {
          tr.setNodeMarkup(nodePos, undefined, { ...tr.doc.nodeAt(nodePos)?.attrs, ...updates })
          return true
        }
        
        return false
      }
    }
  },

  addNodeView() {
    return ReactNodeViewRenderer(ReportSectionComponent, {
      // Node view configuration
      className: 'report-section-wrapper',
      as: 'div',
      contentDOMElementTag: 'div'
    })
  }
})

// Usage with custom configuration
const editor = useEditor({
  extensions: [
    ReportSectionExtension.configure({
      autoLoad: false, // Override default
      debounceDelay: 500,
      onComponentLoad: (componentId, content) => {
        logger.info('Extension', 'onComponentLoad', `Component ${componentId} loaded`)
      }
    })
  ]
})
```

#### Theme and Styling Configuration
The design system provides configurable theming through CSS custom properties:

```typescript
// Theme configuration pattern
interface ThemeConfig {
  colors: {
    primary: string
    secondary: string
    accent: string
    background: string
    surface: string
  }
  spacing: {
    xs: string
    sm: string
    md: string
    lg: string
    xl: string
  }
  borderRadius: {
    sm: string
    md: string
    lg: string
  }
  glassMorphism: {
    blur: string
    opacity: string
    border: string
  }
}

const defaultTheme: ThemeConfig = {
  colors: {
    primary: 'hsl(var(--primary))',
    secondary: 'hsl(var(--secondary))',
    accent: 'hsl(var(--accent))',
    background: 'hsl(var(--background))',
    surface: 'hsl(var(--surface))'
  },
  spacing: {
    xs: '0.5rem',
    sm: '1rem',
    md: '1.5rem',
    lg: '2rem',
    xl: '3rem'
  },
  borderRadius: {
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem'
  },
  glassMorphism: {
    blur: '10px',
    opacity: '0.1',
    border: '1px solid rgba(255, 255, 255, 0.2)'
  }
}

// Theme provider with CSS custom property injection
export const ThemeProvider = ({ theme = defaultTheme, children }) => {
  useEffect(() => {
    const root = document.documentElement
    
    // Inject theme values as CSS custom properties
    Object.entries(theme.colors).forEach(([key, value]) => {
      root.style.setProperty(`--color-${key}`, value)
    })
    
    Object.entries(theme.spacing).forEach(([key, value]) => {
      root.style.setProperty(`--spacing-${key}`, value)
    })
    
    Object.entries(theme.borderRadius).forEach(([key, value]) => {
      root.style.setProperty(`--radius-${key}`, value)
    })
  }, [theme])

  return (
    <div className="theme-provider" data-theme={theme.name}>
      {children}
    </div>
  )
}
```

### 8. Extension and Plugin Patterns

#### Plugin Architecture
The editor supports a plugin architecture for extending functionality:

```typescript
// Plugin interface for extending editor functionality
interface EditorPlugin {
  name: string
  version: string
  dependencies?: string[]
  
  // Lifecycle hooks
  onInstall?(editor: Editor): void | Promise<void>
  onUninstall?(editor: Editor): void | Promise<void>
  onEditorReady?(editor: Editor): void | Promise<void>
  
  // Extension contributions
  extensions?: Extension[]
  commands?: Record<string, Command>
  keymap?: Record<string, string>
  
  // UI contributions
  toolbarItems?: ToolbarItem[]
  menuItems?: MenuItem[]
  panels?: PanelDefinition[]
}

class PluginManager {
  private plugins = new Map<string, EditorPlugin>()
  private installedPlugins = new Set<string>()
  
  async installPlugin(plugin: EditorPlugin, editor: Editor): Promise<void> {
    // Check dependencies
    if (plugin.dependencies) {
      for (const dep of plugin.dependencies) {
        if (!this.installedPlugins.has(dep)) {
          throw new Error(`Plugin ${plugin.name} requires dependency: ${dep}`)
        }
      }
    }
    
    // Install extensions
    if (plugin.extensions) {
      plugin.extensions.forEach(ext => {
        editor.extensionManager.addExtension(ext)
      })
    }
    
    // Register commands
    if (plugin.commands) {
      Object.entries(plugin.commands).forEach(([name, command]) => {
        editor.commands.setContent // Register command
      })
    }
    
    // Execute install hook
    if (plugin.onInstall) {
      await plugin.onInstall(editor)
    }
    
    this.plugins.set(plugin.name, plugin)
    this.installedPlugins.add(plugin.name)
    
    logger.info('PluginManager', 'installPlugin', `Installed plugin: ${plugin.name}`)
  }
  
  async uninstallPlugin(pluginName: string, editor: Editor): Promise<void> {
    const plugin = this.plugins.get(pluginName)
    if (!plugin) return
    
    // Execute uninstall hook
    if (plugin.onUninstall) {
      await plugin.onUninstall(editor)
    }
    
    // Remove extensions
    if (plugin.extensions) {
      plugin.extensions.forEach(ext => {
        editor.extensionManager.removeExtension(ext.name)
      })
    }
    
    this.plugins.delete(pluginName)
    this.installedPlugins.delete(pluginName)
    
    logger.info('PluginManager', 'uninstallPlugin', `Uninstalled plugin: ${pluginName}`)
  }
}

// Example plugin implementation
const AIAssistantPlugin: EditorPlugin = {
  name: 'ai-assistant',
  version: '1.0.0',
  dependencies: ['tiptap-core'],
  
  extensions: [
    AICommandExtension,
    AISlashCommandExtension
  ],
  
  commands: {
    summarizeSelection: () => ({ state, view }) => {
      const { from, to } = state.selection
      const selectedText = state.doc.textBetween(from, to)
      
      // Trigger AI summarization
      aiService.summarize(selectedText).then(summary => {
        view.dispatch(
          state.tr.insertText(summary, to)
        )
      })
      
      return true
    }
  },
  
  toolbarItems: [
    {
      name: 'ai-assistant',
      icon: 'robot',
      title: 'AI Assistant',
      action: () => {
        // Open AI panel
      }
    }
  ],
  
  async onInstall(editor) {
    // Initialize AI service
    await aiService.initialize()
    logger.info('AIAssistantPlugin', 'onInstall', 'AI Assistant plugin installed')
  }
}
```

### 9. Accessibility Patterns and Implementation

#### Comprehensive Accessibility Support
The editor implements WCAG-compliant accessibility patterns throughout:

```typescript
// Accessibility extension for comprehensive a11y support
export const AccessibilityExtension = Extension.create({
  name: 'accessibility',
  
  addOptions() {
    return {
      announceChanges: true,
      keyboardNavigation: true,
      screenReaderSupport: true,
      highContrastMode: false
    }
  },

  addProseMirrorPlugins() {
    return [
      // Accessibility plugin for keyboard navigation and screen reader support
      new Plugin({
        key: new PluginKey('accessibility'),
        
        view(editorView) {
          return new AccessibilityView(editorView, this.options)
        },
        
        props: {
          // Handle keyboard navigation
          handleKeyDown(view, event) {
            return accessibilityKeyHandler(view, event, this.options)
          },
          
          // Announce content changes to screen readers
          handleDOMEvents: {
            'input': (view, event) => {
              if (this.options.announceChanges) {
                announceChange(view, 'Content updated')
              }
            }
          }
        }
      })
    ]
  },

  addCommands() {
    return {
      // Accessibility-specific commands
      announceToScreenReader: (message: string) => () => {
        announceToScreenReader(message)
        return true
      },
      
      focusNextElement: () => ({ view }) => {
        const nextElement = findNextFocusableElement(view.dom)
        if (nextElement) {
          nextElement.focus()
          return true
        }
        return false
      },
      
      toggleHighContrast: () => () => {
        document.body.classList.toggle('high-contrast')
        return true
      }
    }
  }
})

// Aria live region for dynamic announcements
export const AriaLiveRegion = () => {
  const [announcement, setAnnouncement] = useState('')
  
  useEffect(() => {
    // Listen for accessibility announcements
    const handleAnnouncement = (event: CustomEvent) => {
      setAnnouncement(event.detail.message)
      
      // Clear announcement after it's been read
      setTimeout(() => setAnnouncement(''), 1000)
    }
    
    window.addEventListener('accessibility-announce', handleAnnouncement)
    return () => window.removeEventListener('accessibility-announce', handleAnnouncement)
  }, [])
  
  return (
    <div 
      aria-live="polite" 
      aria-atomic="true"
      className="sr-only"
      role="status"
    >
      {announcement}
    </div>
  )
}

// Keyboard navigation helpers
function accessibilityKeyHandler(view: EditorView, event: KeyboardEvent, options: any): boolean {
  // Tab navigation through components
  if (event.key === 'Tab' && !event.shiftKey) {
    const components = findAllComponents(view.dom)
    const currentIndex = findCurrentComponentIndex(components, view.state.selection)
    
    if (currentIndex >= 0 && currentIndex < components.length - 1) {
      const nextComponent = components[currentIndex + 1]
      focusComponent(nextComponent, view)
      event.preventDefault()
      return true
    }
  }
  
  // Shift+Tab for reverse navigation
  if (event.key === 'Tab' && event.shiftKey) {
    const components = findAllComponents(view.dom)
    const currentIndex = findCurrentComponentIndex(components, view.state.selection)
    
    if (currentIndex > 0) {
      const prevComponent = components[currentIndex - 1]
      focusComponent(prevComponent, view)
      event.preventDefault()
      return true
    }
  }
  
  // Escape to exit component
  if (event.key === 'Escape') {
    exitComponent(view)
    announceToScreenReader('Exited component')
    return true
  }
  
  return false
}

// Screen reader announcement system
function announceToScreenReader(message: string, priority: 'polite' | 'assertive' = 'polite'): void {
  const event = new CustomEvent('accessibility-announce', {
    detail: { message, priority }
  })
  
  window.dispatchEvent(event)
  
  // Also log for debugging
  logger.debug('Accessibility', 'announceToScreenReader', `Announced: ${message}`)
}
```

### 10. Security Patterns and Best Practices

#### Content Sanitization and XSS Prevention
The editor implements comprehensive content sanitization to prevent XSS attacks:

```typescript
// Content sanitization patterns
export class ContentSanitizer {
  private allowedTags = new Set([
    'p', 'div', 'span', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
    'ul', 'ol', 'li', 'blockquote', 'pre', 'code',
    'strong', 'em', 'u', 's', 'sub', 'sup',
    'table', 'thead', 'tbody', 'tr', 'th', 'td',
    'br', 'hr', 'a', 'img'
  ])
  
  private allowedAttributes = new Map([
    ['a', new Set(['href', 'title', 'target'])],
    ['img', new Set(['src', 'alt', 'title', 'width', 'height'])],
    ['*', new Set(['class', 'id', 'data-component-id', 'data-status'])]
  ])
  
  private forbiddenProtocols = new Set(['javascript:', 'data:', 'vbscript:'])
  
  sanitizeHTML(html: string): string {
    const parser = new DOMParser()
    const doc = parser.parseFromString(html, 'text/html')
    
    this.sanitizeNode(doc.body)
    
    return doc.body.innerHTML
  }
  
  private sanitizeNode(node: Node): void {
    if (node.nodeType === Node.ELEMENT_NODE) {
      const element = node as Element
      
      // Remove disallowed tags
      if (!this.allowedTags.has(element.tagName.toLowerCase())) {
        this.replaceWithContents(element)
        return
      }
      
      // Sanitize attributes
      this.sanitizeAttributes(element)
      
      // Recursively sanitize children
      const children = Array.from(element.childNodes)
      children.forEach(child => this.sanitizeNode(child))
    }
  }
  
  private sanitizeAttributes(element: Element): void {
    const tagName = element.tagName.toLowerCase()
    const allowedForTag = this.allowedAttributes.get(tagName) || new Set()
    const allowedForAll = this.allowedAttributes.get('*') || new Set()
    
    // Remove disallowed attributes
    const attributes = Array.from(element.attributes)
    attributes.forEach(attr => {
      const attrName = attr.name.toLowerCase()
      
      if (!allowedForTag.has(attrName) && !allowedForAll.has(attrName)) {
        element.removeAttribute(attr.name)
        return
      }
      
      // Sanitize URL attributes
      if (['href', 'src'].includes(attrName)) {
        const value = attr.value.toLowerCase().trim()
        if (this.forbiddenProtocols.some(protocol => value.startsWith(protocol))) {
          element.removeAttribute(attr.name)
          logger.warn('ContentSanitizer', 'sanitizeAttributes', 
            `Removed dangerous ${attrName}: ${attr.value}`)
        }
      }
    })
  }
  
  private replaceWithContents(element: Element): void {
    const parent = element.parentNode
    if (!parent) return
    
    // Move all children to parent
    while (element.firstChild) {
      parent.insertBefore(element.firstChild, element)
    }
    
    // Remove the element
    parent.removeChild(element)
  }
}

// Input validation for user data
export class InputValidator {
  static validateDocumentId(id: string): boolean {
    // Only allow alphanumeric characters, hyphens, and underscores
    const pattern = /^[a-zA-Z0-9_-]{1,50}$/
    return pattern.test(id)
  }
  
  static validateEntityId(id: string): boolean {
    // Entity IDs should follow specific pattern
    const pattern = /^[A-Z]{2,4}-\d{1,6}$/
    return pattern.test(id)
  }
  
  static sanitizeUserInput(input: string): string {
    return input
      .trim()
      .replace(/[<>]/g, '') // Remove angle brackets
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .substring(0, 1000) // Limit length
  }
  
  static validateApiEndpoint(endpoint: string): boolean {
    try {
      const url = new URL(endpoint, window.location.origin)
      
      // Only allow same-origin or whitelisted domains
      const allowedDomains = ['api.example.com', 'localhost']
      const isAllowed = url.hostname === window.location.hostname || 
                      allowedDomains.includes(url.hostname)
      
      // Only allow HTTPS in production
      const isSecure = process.env.NODE_ENV === 'development' || url.protocol === 'https:'
      
      return isAllowed && isSecure
    } catch {
      return false
    }
  }
}

// CSP (Content Security Policy) enforcement
export function enforceCSP(): void {
  // Add CSP meta tag if not present
  if (!document.querySelector('meta[http-equiv="Content-Security-Policy"]')) {
    const cspMeta = document.createElement('meta')
    cspMeta.httpEquiv = 'Content-Security-Policy'
    cspMeta.content = `
      default-src 'self';
      script-src 'self' 'unsafe-inline' 'unsafe-eval';
      style-src 'self' 'unsafe-inline';
      img-src 'self' data: blob:;
      connect-src 'self' https://*.supabase.co;
      font-src 'self';
      object-src 'none';
      media-src 'self';
      frame-src 'none';
    `.replace(/\s+/g, ' ').trim()
    
    document.head.appendChild(cspMeta)
  }
}
```

### 11. Logging and Monitoring Patterns

#### Structured Logging System
The editor implements a comprehensive structured logging system:

```typescript
// Structured logging with context and performance tracking
class EditorLogger {
  private config: LogConfig = {
    level: 'info',
    prefix: 'Editor',
    enabled: process.env.NODE_ENV === 'development'
  }
  
  private context: Record<string, any> = {}
  
  setContext(key: string, value: any): void {
    this.context[key] = value
  }
  
  clearContext(): void {
    this.context = {}
  }
  
  private formatMessage(component: string, method: string, message: string, data?: any): string {
    const timestamp = new Date().toISOString()
    const contextStr = Object.keys(this.context).length > 0 ? 
      ` [${Object.entries(this.context).map(([k, v]) => `${k}=${v}`).join(', ')}]` : ''
    
    const baseMessage = `${timestamp} ${this.config.prefix}.${component}.${method}${contextStr}: ${message}`
    
    if (data) {
      return `${baseMessage} ${JSON.stringify(data)}`
    }
    
    return baseMessage
  }
  
  debug(component: string, method: string, message: string, data?: any): void {
    if (this.shouldLog('debug')) {
      console.log(this.formatMessage(component, method, message, data))
    }
  }
  
  info(component: string, method: string, message: string, data?: any): void {
    if (this.shouldLog('info')) {
      console.log(this.formatMessage(component, method, message, data))
    }
  }
  
  warn(component: string, method: string, message: string, data?: any): void {
    if (this.shouldLog('warn')) {
      console.warn(this.formatMessage(component, method, message, data))
    }
  }
  
  error(component: string, method: string, message: string, error?: Error, data?: any): void {
    if (this.shouldLog('error')) {
      const errorData = error ? { 
        name: error.name, 
        message: error.message, 
        stack: error.stack,
        ...data 
      } : data
      
      console.error(this.formatMessage(component, method, message, errorData))
      
      // Send to error tracking service in production
      if (process.env.NODE_ENV === 'production' && error) {
        this.reportError(component, method, message, error, errorData)
      }
    }
  }
  
  private async reportError(component: string, method: string, message: string, error: Error, data?: any): Promise<void> {
    try {
      // Report to external monitoring service
      await fetch('/api/errors', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          timestamp: new Date().toISOString(),
          component,
          method,
          message,
          error: {
            name: error.name,
            message: error.message,
            stack: error.stack
          },
          context: this.context,
          data,
          userAgent: navigator.userAgent,
          url: window.location.href
        })
      })
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError)
    }
  }
}

// Usage with automatic context setting
const useLogger = (componentName: string) => {
  const logger = useContext(LoggerContext)
  
  useEffect(() => {
    logger.setContext('component', componentName)
    logger.setContext('renderCount', renderCount.current++)
    
    return () => {
      logger.clearContext()
    }
  }, [componentName, logger])
  
  return {
    debug: (method: string, message: string, data?: any) => 
      logger.debug(componentName, method, message, data),
    info: (method: string, message: string, data?: any) => 
      logger.info(componentName, method, message, data),
    warn: (method: string, message: string, data?: any) => 
      logger.warn(componentName, method, message, data),
    error: (method: string, message: string, error?: Error, data?: any) => 
      logger.error(componentName, method, message, error, data)
  }
}
```

### 12. Code Organization and Module Patterns

#### Barrel Exports and Module Structure
The codebase follows consistent patterns for module organization and exports:

```typescript
// services/index.ts - Barrel export pattern
export { logger } from './utils/logger'
export { performanceMonitor } from './utils/performanceMonitor'
export { memoryManager } from './utils/memoryManager'
export { GroupStatusManager } from './utils/groupManager'
export { componentStateMachine } from './state/componentStateMachine'
export { ReportService } from './supabase/reportService'
export { VersionService } from './supabase/versionService'

// Re-export types for convenience
export type { ComponentStatus } from './utils/constants'
export type { PerformanceMetrics } from './utils/performanceMonitor'
export type { ComponentContext } from './state/componentStateMachine'

// context/index.ts - Context aggregation
export { DocumentProvider, useDocument } from './document/DocumentContext'
export { ComponentProvider, useComponents } from './component/ComponentContext'
export { VersioningProvider, useVersioning } from './versioning/VersioningContext'
export { DependencyProvider, useDependencies } from './dependency/DependencyContext'
export { EditorProvider } from './providers/EditorProvider'

// types/index.ts - Type definitions aggregation
export interface ReportComponent {
  id: string
  type: 'report-section' | 'report-group' | 'report-summary'
  status: ComponentStatus
  title?: string
  content?: string
  endpoint?: string
  dependencies?: string[]
  parentId?: string
  children?: string[]
}

export interface DocumentState {
  editor: Editor | null
  content: string
  data: any
  isDirty: boolean
  components: Map<string, ReportComponent>
  citations: CitationType[]
}

export type DocumentAction = 
  | { type: 'EDITOR_CREATED'; editor: Editor }
  | { type: 'CONTENT_CHANGED'; content: string; data: any; source: 'user' | 'system' }
  | { type: 'COMPONENT_REGISTERED'; component: ReportComponent }

// Directory structure pattern:
// /editor
//   /components     - React components
//   /context        - React context providers and hooks
//   /extensions     - TipTap extensions
//   /hooks          - Custom React hooks
//   /services       - Business logic services
//   /types          - TypeScript type definitions
//   /utils          - Utility functions
//   /dialogs        - Modal/dialog components
//   /panels         - Side panel components
//   /toolbar        - Toolbar components
//   index.ts        - Main barrel export
```

These patterns provide a comprehensive foundation for developing and extending the editor system. They ensure consistency, maintainability, and performance while providing clear guidance for new development work.

