## Troubleshooting

### Error Handling Architecture

The editor implements comprehensive error handling through multiple layers:

#### Error Boundaries

**EditorErrorBoundary** - Catches React errors with automatic recovery:
- Displays user-friendly error messages with reload/report options
- Shows stack traces in development mode via `process.env.NODE_ENV` checks
- Integrates with GitHub issue reporting system
- Implements graceful degradation without losing user work

**BubbleMenuErrorBoundary** - Specialized boundary for UI components:
- Silently handles bubble menu rendering errors
- Provides fallback UI or hides problematic components
- Prevents cascade failures in editor toolbar

#### Component State Management

**Component Status State Machine** - Manages component lifecycle with strict state transitions:
```typescript
// Status flow: unregistered -> registering -> waiting/loading -> loaded/error
COMPONENT_STATUS = {
  UNREGISTERED: 'unregistered',
  REGISTERING: 'registering', 
  WAITING: 'waiting',
  LOADING: 'loading',
  LOADED: 'loaded',
  ERROR: 'error',
  PRESERVED: 'preserved',
  LOCKED: 'locked'
}
```

**Error Recovery Mechanisms**:
- Automatic retry from ERROR state back to LOADING
- Dependency resolution validation before state transitions
- Conditional state changes based on component context
- Side-effect execution during state transitions

### Logging and Diagnostics

#### Structured Logging System

**EditorLogger** - Centralized logging with configurable levels:
```typescript
// Enable debug logging
import { logger } from './services/utils/logger'
logger.configure({ level: 'debug', enabled: true })

// Component-specific logging
logger.debug('ComponentName', 'methodName', 'message')
logger.error('ComponentName', 'methodName', 'error message', errorObject)
```

**Log Levels and Usage**:
- `debug`: Development debugging, component lifecycle events
- `info`: General operation information, state changes
- `warn`: Non-critical issues, performance warnings
- `error`: Critical failures, exception handling

#### Performance Monitoring

**PerformanceMonitor** - Real-time performance tracking:
```typescript
import { performanceMonitor } from './services/utils/performanceMonitor'

// Enable monitoring
performanceMonitor.setEnabled(true)

// Manual timing
performanceMonitor.startTiming('operationName')
// ... operation code ...
performanceMonitor.endTiming('operationName')

// Function timing
const result = performanceMonitor.timeFunction('functionName', () => {
  // ... function code ...
})

// Get performance metrics
const metrics = performanceMonitor.getMetrics()
console.log(performanceMonitor.getSummary())
```

**Performance Metrics Tracked**:
- Render count and average render time
- Component registration/removal counts
- Group status updates and dependency resolutions
- Memory usage (when available via `performance.memory`)
- Performance marks for browser dev tools integration

**Performance Issue Detection**:
- Excessive re-renders (>100 renders with >16ms average)
- High component count (>50 components)
- Excessive group updates vs registrations ratio
- Memory usage alerts (>100MB threshold)

### Memory Management

#### Resource Cleanup System

**MemoryManager** - Automatic resource lifecycle management:
```typescript
import { memoryManager } from './services/utils/memoryManager'

// Register resources for cleanup
memoryManager.registerTimeout('timeoutId', timeoutHandle)
memoryManager.registerInterval('intervalId', intervalHandle)
memoryManager.registerListener('listenerId', target, 'event', handler)
memoryManager.registerSubscription('subId', unsubscribeFunction)

// Cleanup specific resources
await memoryManager.cleanup('resourceId')

// Cleanup all resources
await memoryManager.cleanupAll()
```

**ScopedMemoryManager** - Component-specific cleanup:
```typescript
// React hook for automatic cleanup
import { useMemoryManager } from './services/utils/memoryManager'

function MyComponent() {
  const memoryManager = useMemoryManager('component-id')
  
  useEffect(() => {
    const timeout = setTimeout(() => {}, 1000)
    memoryManager.registerTimeout('my-timeout', timeout)
  }, [])
  
  // Automatic cleanup on unmount
}
```

**Memory Leak Detection**:
- Resource age tracking and leak detection
- Resource statistics by type
- Automatic cleanup of aged resources
- Memory usage monitoring and alerts

### Auto-Save Troubleshooting

#### Auto-Save Failure Scenarios

**Network Connectivity Issues**:
```typescript
// Check for network errors in auto-save hooks
const { error, isSaving } = useSupabaseAutoSave({ documentId, editor })

if (error) {
  // Common network error patterns:
  // "NetworkError: Failed to fetch" - Offline/connection issues
  // "TypeError: Failed to fetch" - CORS or network policy issues
  // "Error: 40X" - Authentication/authorization failures
}
```

**Authentication Failures**:
- User session expiration during auto-save operations
- Invalid or missing JWT tokens
- Permission changes during editing session
- Multi-tab authentication conflicts

**Database Constraint Violations**:
- Document ID validation failures
- User permission mismatches
- Version number conflicts in concurrent editing
- Foreign key constraint violations

#### Auto-Save Recovery Mechanisms

**Retry Logic**: Exponential backoff for transient failures
**Conflict Resolution**: Version number management for concurrent edits
**Offline Support**: Local storage fallback when Supabase unavailable
**Data Validation**: Content validation before save attempts

### Extension System Debugging

#### Extension Loading Issues

**Extension Registration Problems**:
```typescript
// Debug extension registration
import { logger } from './services/utils/logger'

// Check extension registration order
logger.debug('Extension', 'register', `Registering ${extensionName}`)

// Verify TipTap compatibility
if (!editor.extensionManager.extensions.find(ext => ext.name === 'extensionName')) {
  logger.error('Extension', 'init', `Extension ${extensionName} failed to register`)
}
```

**Common Extension Errors**:
- Circular dependency issues between extensions
- TipTap version compatibility problems
- Missing peer dependencies
- Extension conflict resolution
- Schema validation failures

#### Extension Performance Issues

**Heavy Extension Operations**:
- AI command processing timeouts
- Large document parsing in extensions
- Real-time collaboration synchronization delays
- Citation and reference resolution performance

### Component Lifecycle Issues

#### Component State Synchronization

**Group Status Management**:
```typescript
// Debug group status updates
import { useGroupStatusManager } from './context/hooks/useGroupStatusManager'

const { groupStatuses, updateGroupStatus } = useGroupStatusManager()

// Check for stuck components
Object.entries(groupStatuses).forEach(([id, status]) => {
  const isStuckLoading = status === 'loading' && hasTimedOut(id)
  if (isStuckLoading) {
    logger.warn('GroupManager', 'checkStatus', `Component ${id} stuck in loading state`)
  }
})
```

**Dependency Resolution Problems**:
- Circular dependencies between report components
- Missing dependency declarations
- Dependency resolution timeouts
- Race conditions in component initialization

**Group Status Synchronization Issues**:
- Visual progress showing complete (e.g., "27/27") while group status remains "loading"
  - Cause: Groups calculate status based only on direct children, not all descendants
  - Solution: Force update mechanism triggers immediate status recalculation
  - Debug: Check `calculateGroupProgress` output vs actual group status
- Status updates not propagating to parent groups
  - Cause: Debouncing delays or cycle detection preventing updates
  - Solution: Use immediate flag in `updateGroupStatus(groupId, true)`

#### State Machine Debugging

```typescript
import { componentStateMachine } from './services/state/componentStateMachine'

// Validate state transitions
const canTransition = componentStateMachine.canTransition(
  currentStatus,
  targetStatus,
  { componentId, componentType, dependencies }
)

if (!canTransition) {
  logger.error('StateMachine', 'transition', `Invalid transition attempted`)
}

// Get valid next states
const validTransitions = componentStateMachine.getValidTransitions(currentStatus)
```

### Browser Compatibility Issues

#### Browser-Specific Problems

**Safari Issues**:
- IndexedDB limitations affecting offline storage
- WebKit-specific TipTap rendering problems
- Performance API availability differences
- Clipboard API permission handling

**Firefox Issues**:
- Extension content security policy conflicts
- Performance timing API differences
- WebRTC collaboration feature limitations

**Chrome Issues**:
- Memory pressure handling in large documents
- Extension manifest v3 compatibility
- Service worker caching conflicts

#### Cross-Browser Debugging

```typescript
// Browser detection for compatibility
const isAppleBrowser = /Safari|iPhone|iPad|iPod/.test(navigator.userAgent)
const isFirefox = navigator.userAgent.includes('Firefox')

// Feature detection
if (!('performance' in window)) {
  logger.warn('Browser', 'compatibility', 'Performance API not available')
}

if (!('clipboard' in navigator)) {
  logger.warn('Browser', 'compatibility', 'Clipboard API not available')
}
```

### Network and Connectivity

#### Offline Handling

**Connection State Management**:
```typescript
// Monitor connection state
window.addEventListener('online', () => {
  logger.info('Network', 'status', 'Connection restored')
  // Trigger auto-save retry
})

window.addEventListener('offline', () => {
  logger.warn('Network', 'status', 'Connection lost')
  // Switch to offline mode
})
```

**Supabase Connection Issues**:
- Real-time subscription disconnections
- Database connection pooling limits
- Rate limiting and throttling
- Geographic latency considerations

### Data Synchronization

#### Conflict Resolution

**Concurrent Editing Conflicts**:
- Version number mismatches
- Operational transform failures
- Real-time collaboration sync errors
- Document state inconsistencies

**Version Control Issues**:
```typescript
// Debug version conflicts
const { data: versions, error } = await supabase
  .from('doc_versions')
  .select('*')
  .eq('document_id', documentId)
  .order('created_at', { ascending: false })

if (versions.length !== expectedVersionCount) {
  logger.error('Versioning', 'conflict', 'Version count mismatch detected')
}
```

### Diagnostic Tools

#### Performance Profiling

```typescript
// Create performance marks for browser dev tools
performanceMonitor.mark('editor-init-start')
// ... initialization code ...
performanceMonitor.mark('editor-init-end')
performanceMonitor.measure('editor-initialization', 'editor-init-start', 'editor-init-end')

// Check for performance issues
const issues = performanceMonitor.checkPerformanceIssues()
issues.forEach(issue => logger.warn('Performance', 'issue', issue))
```

#### Memory Diagnostics

```typescript
// Get memory statistics
const stats = memoryManager.getStats()
logger.info('Memory', 'stats', `Total resources: ${stats.total}`)

// Check for leaks
const leaks = memoryManager.checkForLeaks(60000) // 1 minute age threshold
if (leaks.length > 0) {
  logger.warn('Memory', 'leaks', `Found ${leaks.length} potential leaks`)
}
```

#### Component Diagnostics

```typescript
// Component registration debugging
import { useComponentRegistration } from './context/hooks/useComponentRegistration'

const { registeredComponents, getComponentStatus } = useComponentRegistration()

// Debug component states
Object.entries(registeredComponents).forEach(([id, component]) => {
  const status = getComponentStatus(id)
  if (status === 'error') {
    logger.error('Component', 'status', `Component ${id} in error state`)
  }
})
```

### Debug Mode Configuration

#### Comprehensive Debug Setup

```typescript
// Enable all debugging features
import { logger } from './services/utils/logger'
import { performanceMonitor } from './services/utils/performanceMonitor'
import { memoryManager } from './services/utils/memoryManager'

// Configure logging
logger.configure({
  level: 'debug',
  enabled: true,
  prefix: 'EditorDebug'
})

// Enable performance monitoring
performanceMonitor.setEnabled(true)

// Log performance summary periodically
setInterval(() => {
  performanceMonitor.logSummary()
}, 30000)

// Memory monitoring
setInterval(() => {
  const stats = memoryManager.getStats()
  logger.debug('Memory', 'monitoring', `Resources: ${stats.total}`)
}, 10000)
```

#### Development vs Production Debugging

```typescript
// Environment-specific debugging
if (process.env.NODE_ENV === 'development') {
  // Enable verbose logging
  logger.configure({ level: 'debug' })
  performanceMonitor.setEnabled(true)
  
  // Expose debug utilities to window
  (window as any).editorDebug = {
    logger,
    performanceMonitor,
    memoryManager,
    componentStateMachine
  }
} else {
  // Production: errors and warnings only
  logger.configure({ level: 'warn' })
}
```
