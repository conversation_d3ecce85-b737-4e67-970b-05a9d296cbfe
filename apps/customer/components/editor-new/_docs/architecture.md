# Architecture Overview

The editor recently underwent a major refactoring (January 2025) to improve performance, maintainability, and developer experience. The architecture was transformed from a monolithic 515-line context into a modular, service-oriented design.

## Design Philosophy

The editor architecture follows five core principles that guide all development decisions:

### 1. **Separation of Concerns** - Single Responsibility Principle
Each module has a single, well-defined responsibility with clear boundaries:

- **DocumentContext**: Only handles document state, loading, and saving operations
- **ComponentContext**: Exclusively manages component registration, status tracking, and lifecycle
- **VersioningContext**: Solely responsible for version history and auto-save functionality
- **DependencyContext**: Purely handles component dependency resolution and load ordering

**Example**: Before refactoring, DocumentContext handled 515 lines mixing document operations, component management, versioning, and performance monitoring. After refactoring, these concerns are cleanly separated into focused contexts of ~150 lines each.

### 2. **Service Layer Pattern** - Business Logic Separation
All business logic resides in dedicated service classes, completely separated from UI components:

**Service Examples**:
```typescript
// ReportService - Document operations
await reportService.createReport({
  title: 'New Report',
  content: initialContent,
  entityId: 'ENT-123'
})

// VersionService - Version management  
await versionService.createVersion({
  documentId: 'doc-456',
  content: editorHTML,
  isAutoSave: true
})
```

**Why this matters**: UI components become pure presentation logic, making them easier to test, maintain, and reuse. Business rules are centralized and consistent across the application.

### 3. **Performance First** - Optimization Built Into Architecture
Performance optimizations are architectural decisions, not afterthoughts:

**Debounced Updates**: Group status calculations are debounced at 50ms to prevent UI blocking during rapid changes
```typescript
const debouncedUpdateGroupStatus = debounce((groupId: string) => {
  groupStatusManager.calculateGroupStatus(groupId)
}, 50)
```

**Memoized Contexts**: All context values are properly memoized to prevent unnecessary re-renders
```typescript
const contextValue = useMemo(() => ({
  components: componentsMap,
  registerComponent,
  updateComponentStatus
}), [componentsMap, registerComponent, updateComponentStatus])
```

**Result**: 50% reduction in unnecessary re-renders compared to the previous monolithic architecture.

### 4. **Developer Experience** - Type-Safe, Testable, Debuggable
Every aspect prioritizes developer productivity and code quality:

**Type Safety**: Comprehensive TypeScript coverage with strict mode enabled
```typescript
interface ComponentRegistration {
  id: string
  type: ComponentType
  status: ComponentStatus
  parentId?: string
  dependencies?: string[]
}
```

**Testability**: Service layer enables comprehensive unit testing
```typescript
describe('ComponentStateMachine', () => {
  it('validates valid status transitions', () => {
    expect(stateMachine.canTransition('registering', 'waiting')).toBe(true)
    expect(stateMachine.canTransition('loaded', 'registering')).toBe(false)
  })
})
```

**Debuggability**: Centralized logging with component-scoped context
```typescript
logger.info('Component registered', { 
  componentId: 'comp-123', 
  type: 'report-section',
  context: 'ComponentContext' 
})
```

### 5. **Backward Compatibility** - Gradual Migration Path
The refactoring maintains complete backward compatibility while enabling gradual migration:

**Legacy API Support**: Old hooks continue to work during transition period
```typescript
// Legacy hook (still works)
const { isLoading, error } = useDocumentContext()

// New hooks (preferred)
const { isLoading } = useDocument()
const { error } = useDocumentErrors()
```

**Migration Strategy**: Components can be migrated one by one without breaking existing functionality, reducing risk and enabling incremental improvements.

**Benefits**: Teams can adopt new patterns at their own pace while immediately benefiting from performance improvements and bug fixes in the new architecture.

## Core Components

The editor system is built around three primary components, each with distinct responsibilities and use cases:

### 1. **EkoDocumentEditor.tsx** - Main Editor Orchestrator

**Purpose**: The primary component that coordinates all editor functionality, bringing together rich text editing, AI features, collaboration tools, and document operations in a cohesive interface.

**Key Responsibilities**:
- Manages complete TipTap editor instance with 18+ custom extensions
- Handles document initialization, content processing, and state management
- Provides error boundaries and safe wrappers around editor content
- Coordinates between AI features, collaboration tools, and document operations

**Configuration Options**:
```typescript
interface EkoDocumentEditorProps {
  documentId: string                 // Required document identifier
  citations?: CitationType[]         // Document citations for inline references
  initialContent?: string           // Markdown or HTML content to load
  initialData?: any                 // Structured JSON data for TipTap
  onSave?: (content: string, data?: any) => Promise<void>
  editable?: boolean               // Enable/disable editing (default: true)
  showToolbar?: boolean            // Show editor toolbar (default: true)
  showCollaboration?: boolean      // Enable collaboration features (default: true)
  showAI?: boolean                 // Enable AI features (default: true)
  viewMode?: boolean               // Read-only presentation mode
  printMode?: boolean              // Print-optimized styling
  entityId?: string | null         // Entity association for reports
  runId?: string                   // Run ID for report context
  user?: UserObject                // Current user information
  featureFlags?: FeatureFlags      // Granular feature control
}
```

**Main Features**:
- **Rich Text Editing**: Full TipTap/ProseMirror integration with custom extensions
- **Auto-save**: Supabase-based auto-save with configurable intervals (default: 5s)
- **AI Integration**: Custom AI provider with streaming responses and change tracking
- **Collaboration**: Real-time editing, presence indicators, comments, version history
- **Content Processing**: Markdown-to-HTML conversion with citation processing
- **Accessibility**: ARIA support, screen reader announcements, keyboard navigation

**Usage Examples**:
```typescript
// Basic setup
<EkoDocumentEditor
  documentId="doc-123"
  initialContent="# My Document"
  onSave={handleSave}
/>

// Advanced configuration for reports
<EkoDocumentEditor
  documentId={documentId}
  citations={citations}
  entityId="ENT-456"
  runId="RUN-789"
  user={currentUser}
  featureFlags={{ aiTools: true, comments: true, exportWord: true }}
  onSave={handleSave}
/>
```

### 2. **PublicDocumentViewer.tsx** - Read-Only Document Display

**Purpose**: Provides a clean, optimized interface for viewing shared documents without editing capabilities, designed for public consumption and presentation.

**Key Responsibilities**:
- Renders documents in read-only mode with full formatting preserved
- Provides print functionality with optimized print styles
- Maintains accessibility features while removing interactive elements
- Supports citation display without edit capabilities

**Configuration Options**:
```typescript
interface PublicDocumentViewerProps {
  documentId?: string              // Optional document identifier
  title?: string                   // Document title for header
  content?: string                 // Raw content (markdown/HTML)
  data?: any                      // Structured TipTap JSON data
  citations?: any[]               // Citation references
  className?: string              // Custom styling
}
```

**Main Features**:
- **Public Header**: Clean header with document title and "read-only" indicator
- **Print Support**: Built-in print button with optimized print styles
- **Citation Rendering**: Full citation display without edit capabilities
- **Responsive Design**: Mobile-friendly layout with proper typography
- **Performance Optimized**: Lighter weight than full editor for faster loading

**Usage Examples**:
```typescript
// Simple public viewing
<PublicDocumentViewer
  title="Shared Report"
  content={markdownContent}
  citations={citationData}
/>

// With structured TipTap data
<PublicDocumentViewer
  documentId="public-doc-123"
  data={tiptapJsonData}
  citations={citations}
/>
```

### 3. **DocumentList.tsx** - Document Management Interface

**Purpose**: Comprehensive document listing and management interface that handles document CRUD operations, real-time updates, and access control for both owned and shared documents.

**Key Responsibilities**:
- Manages document CRUD operations through Supabase integration
- Provides real-time document updates via Supabase subscriptions
- Handles search, filtering, and document organization
- Manages access control for owned vs. shared documents

**Configuration Options**:
```typescript
interface DocumentListProps {
  currentUser?: UserObject         // Current user for ownership checks
  onSelectDocument?: (documentId: string) => void  // Document selection handler
  onCreateDocument?: () => void    // Custom document creation
  selectedDocumentId?: string     // Currently selected document
  className?: string              // Custom styling
}
```

**Main Features**:
- **Real-time Updates**: Live document list updates via Supabase subscriptions
- **Search and Filter**: Full-text search and filtering by ownership (My/Shared)
- **Document Metadata**: Author information, creation/update timestamps, ownership indicators
- **Access Control**: Visual differentiation between owned and shared documents
- **Mobile Responsive**: Sidebar layout with proper scrolling and touch support

**Usage Examples**:
```typescript
// Basic document management
<DocumentList
  currentUser={user}
  onSelectDocument={handleSelectDocument}
  selectedDocumentId={currentDocumentId}
/>

// With custom creation workflow
<DocumentList
  currentUser={user}
  onSelectDocument={handleSelectDocument}
  onCreateDocument={handleCustomCreation}
/>
```

### Component Integration Architecture

All three components integrate through a shared architecture:

**Shared Context System**:
- **DocumentContext**: Document state, component tracking, versioning
- **ComponentContext**: Report sections, groups, and summaries management
- **DependencyContext**: Component dependency resolution and load ordering

**Common Extension System**:
- Extensions adapt behavior based on component type (editable vs. read-only)
- Shared citation processing and content rendering pipeline
- Consistent styling through common CSS classes and Tailwind utilities

**Performance Optimizations**:
- Proper memoization prevents unnecessary re-renders
- Debounced operations for group status updates and auto-save
- Automatic resource cleanup for subscriptions and timeouts
- Error boundaries prevent component crashes from affecting the application

## Directory Structure

The editor component follows a well-structured modular architecture that separates concerns into distinct functional areas. At the root level, the component serves as the entry point through `index.ts`, which exports the main EditorProvider along with individual context providers for advanced usage scenarios. This design allows developers to use either the complete editor system or selectively import specific functionality as needed.

The **context directory** forms the backbone of the editor's state management system, implementing a sophisticated hierarchy of specialized contexts. The main DocumentContext sits alongside specialized subdirectories for different aspects of the editor. The component subdirectory manages the registration and lifecycle of individual report components, while the dependency subdirectory handles inter-component dependencies. The document subdirectory focuses specifically on document-level operations, and the versioning subdirectory manages document version control. Within the hooks subdirectory, specialized hooks like `useComponentRegistration`, `useComponentUpdater`, and `useDependencyManager` provide the actual implementation logic for these contexts. The providers subdirectory contains the main EditorProvider that orchestrates all these contexts together.

The **extensions directory** represents the editor's plugin architecture, containing a rich collection of TipTap extensions that provide the editor's functionality. This includes AI-powered features through `AICommandExtension` and `AISlashCommandExtension`, content structuring through `ReportSectionExtension` and `TableOfContentsExtension`, and specialized document features like `CitationExtension` and `MathematicsExtension`. The extensions also include accessibility support and collaborative features through `ChangeTrackingExtension`. The presence of both newer extensions and legacy extensions suggests an ongoing evolution of the plugin system.

The **services directory** contains the core business logic and utilities that power the editor. The state subdirectory houses `componentStateMachine.ts`, which implements a finite state machine for managing component lifecycles and transitions. The supabase subdirectory contains integration services that handle database operations and version control. The utils subdirectory provides essential utilities including comprehensive logging, performance monitoring, memory management, and operational utilities for group management and timeout handling. These services work together to provide a robust foundation for the editor's operations.

The **components directory** contains smaller, reusable UI components that enhance the editor experience. These include interactive elements like context bubble menus and right-click context menus, as well as specialized components for AI slash commands and cursor indicators that provide visual feedback and user interaction capabilities.

The **hooks directory** provides custom React hooks that encapsulate complex editor behaviors. These include general editor operations, automatic document saving functionality, and report management capabilities. The hooks serve as the primary interface between React components and the underlying editor services.

The **panels directory** contains larger UI components that provide extended functionality through side panels. These include AI interaction panels, collaborative commenting interfaces, version history browsers, and document sharing controls. The tabbed side panel serves as a container that organizes these various panels into a cohesive interface.

The **types directory** contains TypeScript type definitions that provide type safety across the entire editor system. The main exports include interfaces for core concepts like report components, component updates, document state, and various status enums that maintain consistency across the application.

The **utils directory** provides utility functions for specific operations like markdown processing, export functionality, and schema serialization. These utilities handle the more technical aspects of document manipulation and format conversion.

The **templates directory** contains document templating functionality, allowing users to create new documents from predefined structures. This includes both static templates and dynamic template generation capabilities.

The **toolbar directory** houses various toolbar components including basic editing functions, AI-powered features, and collaboration toolbars for real-time editing capabilities.

The **dialogs directory** provides modal interfaces for specific operations like collaboration settings and report component configuration.

This architecture demonstrates a sophisticated approach to building a collaborative document editor that separates concerns effectively while maintaining strong integration between components. The extensive use of context providers, hooks, and services creates a flexible system that can accommodate complex document editing scenarios while remaining maintainable and extensible.