## Testing

The editor employs a comprehensive multi-layered testing strategy that ensures reliability, performance, and maintainability across all components and features. The testing architecture is designed to catch issues at multiple levels while providing fast feedback during development.

### Testing Architecture and Framework Setup

The testing infrastructure is built around **Playwright** as the primary end-to-end testing framework, complemented by **Jest/Vitest** for unit testing utilities and specialized evaluation tools for AI features.

#### Framework Configuration

**Playwright Configuration** (`playwright.config.ts`):
```typescript
export default defineConfig({
  testDir: './tests',
  timeout: 120_000,
  fullyParallel: false, // Disabled for stability
  retries: process.env.CI ? 4 : 1,
  workers: process.env.CI ? 1 : 1, // Single worker prevents race conditions
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'retain-on-failure',
    actionTimeout: 15_000,
    navigationTimeout: 30_000,
    viewport: { width: 1280, height: 720 },
    screenshot: 'only-on-failure'
  }
})
```

The configuration prioritizes **stability over speed** with single-worker execution to prevent race conditions in collaborative editing tests and component state management.

**Environment Setup** (`.env.test`):
```bash
NEXT_PUBLIC_SUPABASE_URL=test_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=test_key
```

### Unit Testing Patterns for Components, Hooks, and Services

Unit tests are implemented using Playwright's testing capabilities for editor-specific components that require DOM manipulation, while standalone utilities use traditional Jest patterns.

#### Component State Machine Testing

The editor's component lifecycle is governed by a finite state machine that requires comprehensive validation:

```typescript
// tests/editor-context-refactored.test.ts
describe('Component State Machine', () => {
  test('validates component status transitions', () => {
    const context = {
      componentId: 'test-component',
      componentType: 'report-section',
      dependencies: []
    }

    // Valid transition: unregistered -> registering
    expect(componentStateMachine.canTransition(
      'unregistered', 'registering', context
    )).toBe(true)

    // Invalid transition: loaded -> unregistered  
    expect(componentStateMachine.canTransition(
      'loaded', 'unregistered', context
    )).toBe(false)
  })

  test('handles dependencies in state transitions', () => {
    const contextWithDeps = {
      componentId: 'test-component',
      dependencies: ['dep1', 'dep2']
    }

    // Should go to waiting when dependencies exist
    expect(componentStateMachine.canTransition(
      'registering', 'waiting', contextWithDeps
    )).toBe(true)

    // Should not skip waiting when dependencies exist
    expect(componentStateMachine.canTransition(
      'registering', 'loading', contextWithDeps
    )).toBe(false)
  })
})
```

#### Service Layer Testing

Service classes are tested in isolation with comprehensive mocking of external dependencies:

```typescript
describe('ReportService', () => {
  test('creates document with proper validation', async () => {
    const mockResponse = { id: 'doc-123', title: 'Test Report' }
    await testUtils.mockApiResponse('/api/reports', mockResponse)

    const result = await reportService.createReport({
      title: 'Test Report',
      content: 'Initial content',
      entityId: 'ENT-456'
    })

    expect(result.id).toBe('doc-123')
    expect(result.title).toBe('Test Report')
  })

  test('handles validation errors gracefully', async () => {
    await testUtils.mockApiError('/api/reports', 400)

    await expect(reportService.createReport({
      title: '', // Invalid empty title
      content: 'content'
    })).rejects.toThrow('Validation failed')
  })
})
```

#### Hook Testing Patterns

Custom React hooks are tested using Playwright's component testing capabilities:

```typescript
describe('useDocumentContext', () => {
  test('provides document state and actions', async ({ page }) => {
    await page.goto('/test-harness/document-context')
    
    // Test initial state
    await expect(page.locator('[data-testid="document-status"]')).toHaveText('unloaded')
    
    // Test state transitions
    await page.click('[data-testid="load-document"]')
    await expect(page.locator('[data-testid="document-status"]')).toHaveText('loading')
    
    // Wait for async operation
    await expect(page.locator('[data-testid="document-status"]')).toHaveText('loaded')
  })
})
```

### Integration Testing Strategies for Cross-Component Functionality

Integration tests verify that components work together correctly, particularly focusing on the complex interactions between the editor context, component registration system, and dependency management.

#### Component Lifecycle Integration

Tests verify the complete component lifecycle from registration through loading to final states:

```typescript
describe('Component Lifecycle Integration', () => {
  test('handles complete component registration and loading flow', async ({ page }) => {
    const testUtils = new TestUtils(page)
    await testUtils.login()
    
    const documentId = await testUtils.createDocumentFromTemplate()
    await testUtils.waitForEditor()

    // Add a new component
    await testUtils.addReportComponent('section', {
      id: 'integration-test-section',
      title: 'Integration Test',
      prompt: 'Test prompt'
    })

    // Verify component progresses through expected states
    const component = page.locator('[data-id="integration-test-section"]')
    
    // Should start as registering
    await expect(component).toHaveAttribute('data-status', 'registering')
    
    // Progress to loading
    await expect(component).toHaveAttribute('data-status', 'loading', { timeout: 10000 })
    
    // Finally reach loaded state
    await expect(component).toHaveAttribute('data-status', 'loaded', { timeout: 30000 })

    // Verify component content is rendered
    await expect(component.locator('.component-content')).toBeVisible()
  })
})
```

#### Context Interaction Testing

The editor uses multiple React contexts that must interact correctly:

```typescript
describe('Context Interactions', () => {
  test('document and component contexts synchronize correctly', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    // Document context should initialize first
    await expect(page.locator('[data-testid="document-status"]')).toHaveText('loaded')

    // Component context should register components after document loads
    await expect(page.locator('[data-component-count]')).not.toHaveAttribute('data-component-count', '0')

    // Verify component registration doesn't trigger document re-initialization
    await testUtils.addReportComponent('section')
    await expect(page.locator('[data-testid="document-status"]')).toHaveText('loaded')
  })
})
```

#### Dependency Resolution Testing

Components with dependencies require specialized testing to ensure proper loading order:

```typescript
describe('Dependency Resolution', () => {
  test('loads components in correct dependency order', async ({ page }) => {
    // Create components with dependency relationships
    await testUtils.addReportComponent('section', { id: 'section-1' })
    await testUtils.addReportComponent('section', { id: 'section-2' })
    await testUtils.addReportComponent('summary', { 
      id: 'summary-1',
      dependencies: ['section-1', 'section-2']
    })

    // Sections should load first
    await expect(page.locator('[data-id="section-1"]')).toHaveAttribute('data-status', 'loaded')
    await expect(page.locator('[data-id="section-2"]')).toHaveAttribute('data-status', 'loaded')

    // Summary should wait for dependencies
    await expect(page.locator('[data-id="summary-1"]')).toHaveAttribute('data-status', 'waiting')

    // Then progress to loaded after dependencies complete
    await expect(page.locator('[data-id="summary-1"]')).toHaveAttribute('data-status', 'loaded', { timeout: 45000 })
  })
})
```

### End-to-End Testing with Playwright

End-to-end tests validate complete user workflows from authentication through document creation, editing, and collaboration.

#### User Workflow Testing

Complete user journeys are tested to ensure the editor functions correctly in real-world scenarios:

```typescript
describe('Complete Report Creation Workflow', () => {
  test('user can create, edit, and save a complete report', async ({ page }) => {
    const testUtils = new TestUtils(page)
    await testUtils.login()

    // Create document from template
    const documentId = await testUtils.createDocumentFromTemplate('ESG Report')
    
    // Wait for template components to load
    await testUtils.waitForComponentLoading('.report-section')
    
    // Edit document content
    await testUtils.typeInEditor('This is my custom ESG report introduction.')
    
    // Configure existing components
    await testUtils.openComponentConfig('.report-section')
    await testUtils.fillComponentConfig({
      prompt: 'Focus on environmental impact metrics'
    })
    await testUtils.confirmComponentConfig()
    
    // Add custom components
    await testUtils.addReportComponent('section', {
      id: 'financial-impact',
      title: 'Financial Impact Analysis'
    })
    
    // Verify auto-save functionality
    await testUtils.waitForAutoSave()
    
    // Navigate away and back to verify persistence
    await testUtils.goToDocuments()
    await page.goto(`/customer/documents/${documentId}`)
    
    // Verify content persisted
    await testUtils.checkEditorContent('This is my custom ESG report introduction.')
    await testUtils.checkComponentExists('financial-impact')
  })
})
```

#### Collaboration Testing

Real-time collaboration features are tested using multiple browser contexts:

```typescript
describe('Collaborative Editing', () => {
  test('multiple users can edit simultaneously', async ({ page, context }) => {
    const testUtils1 = new TestUtils(page)
    await testUtils1.login('<EMAIL>')
    const documentId = await testUtils1.createDocumentFromTemplate()

    // Create second user session
    const page2 = await context.newPage()
    const testUtils2 = new TestUtils(page2)
    await testUtils2.login('<EMAIL>')
    await page2.goto(`/customer/documents/${documentId}`)

    // User 1 adds content
    await testUtils1.typeInEditor('User 1 content')
    await testUtils1.waitForAutoSave()

    // User 2 should see the content
    await testUtils2.checkEditorContent('User 1 content')

    // User 2 adds content
    await testUtils2.typeInEditor(' and User 2 addition')
    await testUtils2.waitForAutoSave()

    // User 1 should see combined content
    await testUtils1.checkEditorContent('User 1 content and User 2 addition')
  })
})
```

### Performance Testing and Benchmarking

Performance tests ensure the editor maintains responsiveness under various load conditions and validate optimization implementations.

#### Rendering Performance Tests

```typescript
describe('Editor Performance', () => {
  test('renders large documents efficiently', async ({ page }) => {
    const testUtils = new TestUtils(page)
    await testUtils.login()
    
    const documentId = await testUtils.createDocumentFromTemplate()
    const startTime = Date.now()

    // Add many components to test performance
    const componentCount = 20
    for (let i = 0; i < componentCount; i++) {
      await testUtils.addReportComponent('section', {
        id: `perf-section-${i}`,
        title: `Performance Section ${i}`
      })
    }

    // Wait for all components to load
    await expect(page.locator('.report-section')).toHaveCount(componentCount)
    
    // Measure total rendering time
    const endTime = Date.now()
    const totalTime = endTime - startTime

    // Should complete within reasonable time (30 seconds for 20 components)
    expect(totalTime).toBeLessThan(30000)
    
    console.log(`Rendered ${componentCount} components in ${totalTime}ms`)
  })

  test('debounces group status updates effectively', async ({ page }) => {
    // Track performance of rapid status changes
    let updateCount = 0
    await page.evaluate(() => {
      window.groupUpdates = []
      const originalConsole = console.log
      console.log = (...args) => {
        if (args.join(' ').includes('GroupStatusManager')) {
          window.groupUpdates.push(Date.now())
        }
        originalConsole.apply(console, args)
      }
    })

    // Rapidly add components
    for (let i = 0; i < 15; i++) {
      await testUtils.addReportComponent('section')
    }

    await page.waitForTimeout(1000) // Wait for debouncing

    const updates = await page.evaluate(() => window.groupUpdates.length)
    
    // Should have significantly fewer updates than operations due to debouncing
    expect(updates).toBeLessThan(15)
    expect(updates).toBeGreaterThan(0)
  })
})
```

#### Memory Usage Monitoring

```typescript
describe('Memory Management', () => {
  test('cleans up resources properly', async ({ page }) => {
    const testUtils = new TestUtils(page)
    await testUtils.login()

    // Monitor memory usage
    const initialMemory = await page.evaluate(() => performance.memory?.usedJSHeapSize || 0)

    // Perform operations that should clean up after themselves
    for (let i = 0; i < 10; i++) {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.addReportComponent('section')
      await page.goto('/customer/documents') // Navigate away to trigger cleanup
    }

    // Force garbage collection if available
    await page.evaluate(() => {
      if (window.gc) window.gc()
    })

    const finalMemory = await page.evaluate(() => performance.memory?.usedJSHeapSize || 0)
    const memoryGrowth = finalMemory - initialMemory

    // Memory growth should be reasonable (less than 50MB for 10 document cycles)
    expect(memoryGrowth).toBeLessThan(50 * 1024 * 1024)
  })
})
```

### Visual Regression Testing Approaches

While the current implementation focuses on functional testing, visual regression testing is handled through Playwright's built-in screenshot capabilities and systematic visual verification patterns.

#### Screenshot Testing Strategy

```typescript
describe('Visual Regression', () => {
  test('editor layout remains consistent', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()
    await testUtils.waitForEditor()

    // Take screenshot of editor in clean state
    await expect(page.locator('.editor-container')).toHaveScreenshot('editor-clean-state.png')

    // Add components and verify layout
    await testUtils.addReportComponent('section')
    await testUtils.addReportComponent('group')
    
    await expect(page.locator('.editor-container')).toHaveScreenshot('editor-with-components.png')
  })

  test('component states have consistent visual indicators', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()
    
    // Verify loading state appearance
    const component = page.locator('.report-section').first()
    await expect(component.locator('.animate-spin')).toBeVisible()
    
    // Verify loaded state appearance
    await expect(component).toHaveAttribute('data-status', 'loaded')
    await expect(component.locator('.animate-spin')).not.toBeVisible()
    
    // Verify locked state appearance
    await testUtils.performComponentAction('lock')
    await expect(component.locator('svg.text-blue-600')).toBeVisible()
  })
})
```

#### Print Mode Visual Testing

```typescript
describe('Print Mode Visual Consistency', () => {
  test('print styles render correctly', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()
    
    // Switch to print mode
    await page.click('[data-testid="print-mode-toggle"]')
    
    // Verify print-specific layout
    await expect(page.locator('.print-mode')).toHaveClass(/print-layout/)
    await expect(page.locator('.no-print')).not.toBeVisible()
    
    // Take screenshot for visual regression
    await page.screenshot({ path: 'print-mode-layout.png', fullPage: true })
  })
})
```

### Accessibility Testing Implementation

Accessibility testing ensures the editor meets WCAG compliance standards and provides excellent keyboard navigation and screen reader support.

#### ARIA and Keyboard Navigation Testing

```typescript
describe('Accessibility Compliance', () => {
  test('keyboard navigation works throughout editor', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()
    
    // Test tab navigation through interface
    await page.keyboard.press('Tab') // Should focus first interactive element
    let focusedElement = await page.evaluate(() => document.activeElement?.tagName)
    expect(['BUTTON', 'INPUT', 'TEXTAREA']).toContain(focusedElement)
    
    // Navigate through toolbar
    for (let i = 0; i < 5; i++) {
      await page.keyboard.press('Tab')
    }
    
    // Should be able to reach editor
    await page.keyboard.press('Tab')
    focusedElement = await page.evaluate(() => document.activeElement?.className)
    expect(focusedElement).toContain('ProseMirror')
  })

  test('ARIA attributes are properly set', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()
    
    // Check dialog accessibility when opened
    await testUtils.openComponentConfig('.report-section')
    
    const dialog = page.locator('[role="dialog"]')
    await expect(dialog).toBeVisible()
    await expect(dialog).toHaveAttribute('aria-labelledby')
    await expect(dialog).toHaveAttribute('aria-describedby')
    
    // Check close button accessibility
    await expect(page.locator('[aria-label="Close"]')).toBeVisible()
    
    // Test escape key closes dialog
    await page.keyboard.press('Escape')
    await expect(dialog).not.toBeVisible()
  })

  test('screen reader announcements work correctly', async ({ page }) => {
    // Monitor aria-live regions
    await testUtils.createDocumentFromTemplate()
    
    const liveRegion = page.locator('[aria-live]')
    await expect(liveRegion).toBeVisible()
    
    // Trigger state change that should announce
    await testUtils.addReportComponent('section')
    
    // Verify announcement appears in live region
    await expect(liveRegion).toContainText('Component added successfully')
  })
})
```

#### Color Contrast and Visual Accessibility

```typescript
describe('Visual Accessibility', () => {
  test('sufficient color contrast in all states', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()
    
    // Check contrast in different component states
    const component = page.locator('.report-section').first()
    
    // Normal state
    await expect(component).toHaveCSS('color', expect.stringMatching(/^rgb/))
    
    // Hover state
    await component.hover()
    await expect(component).toHaveCSS('background-color', expect.stringMatching(/^rgb/))
    
    // Focus state
    await component.focus()
    await expect(component).toHaveCSS('outline', expect.stringMatching(/^\d+px/))
  })
})
```

### Testing Utilities and Helper Functions

The `TestUtils` class provides a comprehensive suite of helper functions that abstract common testing operations and reduce code duplication across tests.

#### Core Utilities

```typescript
export class TestUtils {
  constructor(private page: Page) {}

  // Authentication utilities
  async login(email?: string, password?: string) {
    const credentials = getAuthCredentials()
    await this.page.goto('/login')
    await this.page.fill('#email', email || credentials.email)
    await this.page.fill('#password', password || credentials.password)
    await this.page.click('button[type="submit"]')
    await this.page.waitForURL(/\/customer/)
  }

  // Document operations
  async createDocumentFromTemplate(templateName?: string) {
    const template = templateName || getTestTemplate('primary')
    await this.page.goto('/customer/documents')
    await this.page.click('[data-testid="new-document-button"]')
    
    await this.page.waitForSelector('[data-testid="template-dialog"]')
    await this.page.click(`text=${template}`)
    await this.page.waitForURL(/\/customer\/documents\/[a-f0-9-]+/)
    
    return this.getDocumentIdFromUrl()
  }

  // Component interaction utilities
  async addReportComponent(type: 'section' | 'group' | 'summary', config?: any) {
    const buttonTitles = {
      section: 'Insert Report Section',
      group: 'Insert Report Group', 
      summary: 'Insert Report Summary'
    }
    
    await this.page.click(`button[title="${buttonTitles[type]}"]`)
    await expect(this.page.locator('[role="dialog"]')).toBeVisible()
    
    if (config) {
      await this.fillComponentConfig(config)
    }
    
    await this.confirmComponentConfig()
  }

  // Waiting and synchronization utilities
  async waitForComponentLoading(componentSelector = '.report-section') {
    const component = this.page.locator(componentSelector).first()
    
    try {
      // Wait for loading to start
      await expect(component.locator('.animate-spin')).toBeVisible({ timeout: 5000 })
      // Wait for loading to complete
      await expect(component.locator('.animate-spin')).not.toBeVisible({ timeout: 45000 })
    } catch (error) {
      // Component might load too quickly to catch spinner
      console.log('Loading spinner not detected - component may have loaded quickly')
    }
  }

  async waitForAutoSave() {
    const saveButton = this.page.locator('[data-testid="save-button"]')
    
    if (await saveButton.count() > 0) {
      // Wait for save button to show "Saving..." then return to "Save"
      try {
        await expect(saveButton).toContainText('Save', { timeout: 10000 })
      } catch (error) {
        // Fallback to network idle
        await this.page.waitForLoadState('networkidle', { timeout: 5000 })
      }
    }
  }
}
```

#### Assertion Helpers

```typescript
// Extended assertion patterns for editor-specific functionality
export class EditorAssertions {
  static async componentHasStatus(page: Page, componentId: string, status: string) {
    const component = page.locator(`[data-id="${componentId}"]`)
    await expect(component).toHaveAttribute('data-status', status)
  }

  static async editorContainsText(page: Page, text: string) {
    await expect(page.locator('.ProseMirror')).toContainText(text)
  }

  static async componentCount(page: Page, type: string, count: number) {
    await expect(page.locator(`.report-${type}`)).toHaveCount(count)
  }
}
```

### Mock Strategies for External Dependencies

The testing framework includes comprehensive mocking strategies for external services, AI providers, and database operations.

#### API Mocking Patterns

```typescript
describe('API Mocking Strategies', () => {
  test('mocks report generation API calls', async ({ page }) => {
    // Mock successful API response
    await testUtils.mockApiResponse('/api/report/section', {
      text: '<h2>Mocked Section Content</h2><p>This is test content.</p>',
      citations: [
        { doc_page_id: 12345, title: 'Test Citation', url: 'https://example.com' }
      ]
    })

    await testUtils.createDocumentFromTemplate()
    await testUtils.addReportComponent('section')
    
    // Verify mocked content appears
    await expect(page.locator('text=Mocked Section Content')).toBeVisible()
  })

  test('handles API error conditions', async ({ page }) => {
    // Mock API error
    await testUtils.mockApiError('/api/report/section', 500)

    await testUtils.createDocumentFromTemplate()
    await testUtils.addReportComponent('section')

    // Verify error handling
    await expect(page.locator('.error-message')).toBeVisible()
    await expect(page.locator('text=Failed to load content')).toBeVisible()
  })
})
```

#### AI Provider Mocking

```typescript
describe('AI Provider Mocking', () => {
  test('mocks AI generation responses', async ({ page }) => {
    await testUtils.mockApiResponse('/api/ai/generate', {
      result: 'This is improved AI-generated content.',
      success: true,
      usage: { tokens: 150, cost: 0.001 }
    })

    await testUtils.createDocumentFromTemplate()
    await testUtils.typeInEditor('Original text to improve')
    await testUtils.selectAllEditorText()
    
    await page.click('[data-testid="ai-command-improve"]')
    
    await expect(page.locator('text=This is improved AI-generated content.')).toBeVisible()
  })
})
```

### Test Data Management and Fixtures

Test data is managed through a centralized configuration system that provides consistent, maintainable test scenarios.

#### Test Configuration System

```typescript
// tests/test-config.ts
export const TEST_CONFIG = {
  auth: {
    email: '<EMAIL>',
    password: 'test1_pass'
  },
  entities: {
    primary: 'NxEZa0dXLn',    // Colgate test entity
    secondary: 'JN6ZWej7Rw',  // Inflexion test entity
    invalid: 'invalid-entity'
  },
  templates: {
    primary: 'Blank Document',
    secondary: 'ESG Report'
  },
  models: {
    primary: 'sdg',
    secondary: 'doughnut'
  }
}

export function getTestEntity(type: 'primary' | 'secondary' = 'primary') {
  return TEST_CONFIG.entities[type]
}
```

#### Dynamic Test Data Generation

```typescript
describe('Dynamic Test Data', () => {
  test('generates test content for various scenarios', async ({ page }) => {
    const testData = {
      largeDocument: generateLargeContent(5000), // 5000 words
      complexStructure: generateNestedComponents(10), // 10 nested levels
      multipleUsers: generateUserSessions(5) // 5 concurrent users
    }

    // Use generated data in tests
    await testUtils.createDocumentFromTemplate()
    await testUtils.fillEditor(testData.largeDocument)
    
    // Verify performance with large content
    const renderTime = await testUtils.verifyPerformanceMetrics(30000)
    expect(renderTime).toBeLessThan(30000)
  })
})
```

### Continuous Integration Testing Workflows

The CI/CD pipeline includes comprehensive testing automation through GitHub Actions, with parallel execution strategies and intelligent test selection.

#### GitHub Actions Configuration

**Customer App CI Pipeline** (`.github/workflows/customer-app.yml`):
```yaml
name: Customer App CI

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '18'
      - name: Install dependencies
        run: pnpm install
      - name: TypeScript check
        run: npx tsc --noEmit
      - name: Build application
        run: pnpm build

  playwright-tests:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Install Playwright Browsers
        run: npx playwright install --with-deps
      - name: Run Playwright tests
        run: npx playwright test --workers=2 --reporter=line
        env:
          CI: true
      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: playwright-report
          path: playwright-report/
```

**Python Backend Testing** (`.github/workflows/python-tests.yml`):
```yaml
name: Backend CI

jobs:
  tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      - name: Install uv
        uses: astral-sh/setup-uv@v3
      - name: Run pytest
        run: uv run python -m pytest tests -v

  type-check:
    runs-on: ubuntu-latest
    continue-on-error: true  # Allow warnings without blocking
    steps:
      - name: Run type check
        run: uvx ty check . || true
```

#### Test Selection and Optimization

```typescript
// Intelligent test selection based on changed files
describe('CI Optimization', () => {
  // Only run expensive tests when specific files change
  test.describe('Performance Tests', () => {
    test.skip(
      !process.env.CI || !changedFiles.includes('components/editor/'),
      'Performance tests only in CI for editor changes'
    )
    
    test('editor performance regression test', async ({ page }) => {
      // Expensive performance test
    })
  })
  
  // Always run critical path tests
  test.describe('Critical Path', () => {
    test('user can create and save document', async ({ page }) => {
      // Essential functionality test
    })
  })
})
```

### Test Coverage Analysis and Reporting

While traditional code coverage metrics are not formally tracked (no Jest/Istanbul configuration found), the testing strategy focuses on **functional coverage** through comprehensive workflow testing.

#### Coverage Strategy

1. **Component Coverage**: Every major component has corresponding test files
2. **Workflow Coverage**: All user workflows are tested end-to-end
3. **Error Path Coverage**: Error conditions and edge cases are systematically tested
4. **Integration Coverage**: All context interactions and service integrations are validated

#### Test Reporting

```typescript
// Custom test reporting for CI/CD
test.afterEach(async ({ page }, testInfo) => {
  if (testInfo.status === 'failed') {
    // Capture additional debugging information
    const screenshot = await page.screenshot()
    const logs = await page.evaluate(() => console.logs)
    
    // Attach to test report
    await testInfo.attach('screenshot', { 
      body: screenshot, 
      contentType: 'image/png' 
    })
    await testInfo.attach('console-logs', { 
      body: JSON.stringify(logs, null, 2), 
      contentType: 'application/json' 
    })
  }
})
```

#### LLM Evaluation Testing

A specialized testing framework exists for evaluating AI/LLM features:

**Location**: `backoffice/src/tests/llm_evals/`

**Framework**: Custom pytest-based evaluation system

**Usage**:
```bash
cd backoffice/src
uv run python -m pytest tests/llm_evals -v
```

**Configuration**: Models are configured in `conftest.py`:
```python
@pytest.fixture(scope="session")
def favorite_models():
    return [
        "vertex_ai/gemini-2.0-flash-lite",
        "claude-3-5-sonnet-20240620",
        "groq/llama3-70b-8192"
    ]
```

This testing architecture ensures comprehensive coverage of the editor's complex functionality while maintaining fast feedback loops and reliable CI/CD execution. The focus on stability over speed, combined with comprehensive mocking and realistic test scenarios, provides confidence in the editor's reliability across all supported features and use cases.

