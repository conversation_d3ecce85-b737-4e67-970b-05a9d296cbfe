## State Management

The editor implements a sophisticated state management system using React Context API, following the architecture patterns documented in `CLAUDE.md`.

### Context Architecture

The editor implements a sophisticated multi-layered context architecture that separates concerns into four specialized contexts, each managing distinct aspects of the editor's functionality. This architecture demonstrates advanced React patterns including complex state management, dependency injection, performance optimization, and cross-context communication.

### Context Hierarchy and Provider Relationships

The context system follows a carefully designed hierarchy that ensures proper initialization order and dependency resolution. The following diagram illustrates the architectural overview of the entire context system:

```mermaid
graph TB
    subgraph "Editor Architecture"
        A[EditorProvider] --> B[DocumentProvider]
        B --> C[ComponentProvider]
        C --> D[DependencyProvider]
        D --> E[VersioningProvider]
        E --> F[EditorProviderInner]
        F --> G[Child Components]
    end

    subgraph "Services Layer"
        S1[GroupStatusManager]
        S2[DependencyManager]
        S3[DocumentVersioning]
        S4[ReportService]
    end

    subgraph "State Management"
        R1[DocumentReducer]
        R2[ComponentReducer]
        R3[DependencyReducer]
        R4[VersioningReducer]
    end

    B -.-> S1
    C -.-> S1
    D -.-> S2
    E -.-> S3
    B -.-> S4

    B --> R1
    C --> R2
    D --> R3
    E --> R4

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec
```

The `EditorProvider` serves as the orchestrating component, composing all specialized contexts in the correct order:

```typescript
<EditorProvider reportId={id} onSave={handleSave}>
  <DocumentProvider>
    <ComponentProvider>
      <DependencyProvider>
        <VersioningProvider>
          <EditorProviderInner>
            {children}
          </EditorProviderInner>
        </VersioningProvider>
      </DependencyProvider>
    </ComponentProvider>
  </DocumentProvider>
</EditorProvider>
```

This hierarchical structure ensures that contexts with dependencies on other contexts are initialized after their dependencies. The `DocumentProvider` forms the foundation since it manages the core editor instance that other contexts depend upon. The `ComponentProvider` and `DependencyProvider` work together to manage component lifecycle and inter-component relationships. The `VersioningProvider` sits at the top of the specialized contexts, as it depends on the stable document state provided by the lower layers.

#### Provider Initialization and Lifecycle Management

Each provider follows a consistent initialization pattern using React hooks and the reducer pattern for state management. The providers implement comprehensive cleanup mechanisms through `useEffect` hooks that ensure proper resource disposal when components unmount. This prevents memory leaks and ensures that event listeners, timeouts, and async operations are properly cleaned up.

The `EditorProvider` coordinates initialization across all contexts through a two-phase approach: first initializing core services like the `GroupStatusManager` and memory scoping utilities, then providing them to child contexts through the provider hierarchy. This approach enables dependency injection while maintaining clean separation of concerns.

### State Management Patterns Across Contexts

#### Reducer-Based State Management with Complex Actions

Each context implements state management through React's `useReducer` hook with sophisticated action patterns that handle complex state transitions. The reducer patterns demonstrate advanced state management techniques including:

**Immutable State Updates**: All contexts maintain immutability through careful state reconstruction. For example, the `ComponentContext` uses Map copying to prevent mutation:

```typescript
case 'REGISTER_COMPONENT': {
  const newComponents = new Map(state.components)
  const newHierarchy = new Map(state.componentHierarchy)
  newComponents.set(component.id, component)
  return { ...state, components: newComponents, componentHierarchy: newHierarchy }
}
```

**Conditional State Changes**: The `DocumentContext` implements intelligent state updates that only trigger re-renders when content actually changes, preventing unnecessary updates during programmatic content modifications:

```typescript
case 'CONTENT_CHANGED': {
  const contentChanged = action.content !== state.content || 
                        JSON.stringify(action.data) !== JSON.stringify(state.data)
  const shouldMarkDirty = contentChanged && (action.source === 'user' || action.source === 'system')
  
  return {
    ...state,
    content: action.content,
    data: action.data,
    isDirty: shouldMarkDirty || state.isDirty,
    saveError: null
  }
}
```

**Complex State Calculations**: The dependency management system performs sophisticated graph operations within reducer functions, maintaining dependency graphs and calculating resolution chains while ensuring immutability.

#### Cross-Context State Synchronization

The architecture implements sophisticated cross-context communication patterns that maintain consistency without creating tight coupling. Key synchronization mechanisms include:

**Event-Based Communication**: Contexts communicate through callback functions passed down through the provider hierarchy. The `DocumentContext` provides entity change listeners that notify all dependent contexts when entity or run IDs change:

```typescript
const addEntityChangeListener = useCallback(
  (listener: (entity: string | null, run: string) => void) => {
    entityChangeListenersRef.current.add(listener)
    return () => entityChangeListenersRef.current.delete(listener)
  }, []
)
```

**Ref-Based State Sharing**: To avoid stale closures in debounced and async operations, contexts maintain refs to current state that can be accessed from within closures without triggering dependency updates:

```typescript
const stateRef = useRef(state)
stateRef.current = state

// Used in debounced operations to access current state
const debouncedUpdateGroupStatus = debounce((groupId: string) => {
  groupStatusManager.calculateGroupStatus(groupId, stateRef.current.components)
}, 50)
```

### Context Composition and Dependency Injection

#### Service Layer Integration

The context architecture integrates seamlessly with a service layer that provides business logic abstraction. Services are injected into contexts through the provider pattern, enabling clean separation between state management and business operations:

```typescript
export function DocumentProvider({ documentId, onSave }: DocumentProviderProps) {
  const groupStatusManager = useGroupStatusManager({ dispatch, stateRef })
  const dependencyManager = useDependencyManager({ stateRef })
  const documentVersioning = useDocumentVersioning({ editor: state.editor, documentId, dispatch })
  
  // Context provides high-level operations through service delegation
  const contextValue = useMemo(() => ({
    // Direct state access
    state,
    dispatch,
    // Service-delegated operations
    updateGroupStatus: groupStatusManager.updateGroupStatus,
    waitForDependencies: dependencyManager.waitForDependencies,
    createDocumentVersion: documentVersioning.createDocumentVersion
  }), [/* ... */])
}
```

This pattern enables complex business logic to be implemented in testable service classes while keeping contexts focused on state management and UI integration.

#### Hook-Based Functionality Composition

Each context delegates specific functionality to custom hooks that encapsulate related operations. This approach enables fine-grained composition and testing of functionality:

**Component Registration Logic**: The `useComponentRegistration` hook handles complex component lifecycle management including orphaned component handling, post-registration processing, and hierarchy maintenance.

**Document Versioning Logic**: The `useDocumentVersioning` hook manages version creation, auto-save scheduling, and version cleanup operations with debounced execution and error handling.

**Dependency Resolution Logic**: The `useDependencyManager` hook implements sophisticated dependency graph management including circular dependency detection, timeout handling, and resolution chain calculation.

### Data Flow Between Contexts and Components

#### Top-Down Data Flow with Selective Subscriptions

The architecture implements a sophisticated data flow pattern where data flows top-down through the provider hierarchy, but components can selectively subscribe to specific aspects of context state to minimize re-renders. The following diagram illustrates the data flow patterns:

```mermaid
flowchart TD
    subgraph "Data Flow Patterns"
        A[EditorProvider] -->|"Initialize Services"| B[DocumentProvider]
        B -->|"Editor Instance"| C[ComponentProvider]
        C -->|"Component State"| D[DependencyProvider]
        D -->|"Dependency Graph"| E[VersioningProvider]
        
        B -.->|"Entity Changes"| F[Entity Change Listeners]
        C -.->|"Component Updates"| G[Component Subscribers]
        D -.->|"Dependency Events"| H[Dependency Watchers]
        E -.->|"Version Events"| I[Version Listeners]
    end

    subgraph "Bidirectional Communication"
        J[Components] -->|"Dispatch Actions"| K[Context Reducers]
        K -->|"State Updates"| J
        J -.->|"Event Callbacks"| L[Cross-Context Events]
        L -.->|"Notifications"| J
    end

    subgraph "Performance Optimization"
        M[Memoized Context Values]
        N[Selective Dependencies]
        O[Debounced Operations]
        P[Ref-Based State Access]
    end

    E --> J
    J --> M
    M --> N
    N --> O
    O --> P

    style A fill:#e3f2fd
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec
    style J fill:#fff9c4
```

```typescript
const contextValue = useMemo(
  () => ({
    state,
    dispatch,
    // ... operations
  }),
  [
    // Selective dependencies - only state properties that should trigger re-renders
    state.editor,
    state.content,
    state.isDirty,
    state.components,
    // Function dependencies with stable references
    registerComponent,
    updateComponent
  ]
)
```

This memoization pattern ensures that context consumers only re-render when relevant state changes, preventing cascade re-renders throughout the component tree.

#### Bidirectional Component Communication

Components can communicate with contexts through multiple channels:

**Direct Operations**: Components call context operations that dispatch actions to update state immediately.

**Event-Based Updates**: Components register event listeners for cross-cutting concerns like entity changes that affect multiple components simultaneously.

**Callback-Based Notifications**: Components provide callbacks that contexts invoke when specific conditions are met, enabling reactive programming patterns.

### Context Initialization and Lifecycle Management

#### Phased Initialization with Error Recovery

Context initialization follows a carefully orchestrated sequence that handles missing dependencies and error conditions gracefully:

```typescript
const documentInitialization = useDocumentInitialization({
  editor: state.editor,
  hasTriggeredInitialLoad: state.hasTriggeredInitialLoad,
  isInitialized: state.isInitialized,
  dispatch,
  stateRef
})
```

The initialization system tracks initialization state and provides recovery mechanisms for failed initialization attempts. This ensures that the editor can recover from transient failures and provides clear error states when initialization cannot proceed.

#### Resource Cleanup and Memory Management

The context system implements comprehensive cleanup mechanisms that prevent memory leaks and ensure proper resource disposal:

**Timeout Management**: All contexts register timeouts and intervals with cleanup functions that clear them on unmount.

**Event Listener Cleanup**: Event listeners are properly removed using cleanup functions returned from useEffect hooks.

**Service Cleanup**: Service classes provide cleanup methods that are called during context teardown to dispose of resources and clear caches.

#### State Persistence and Restoration

The context architecture supports state persistence across page reloads and navigation through integration with the browser's storage APIs and Supabase real-time synchronization:

**Auto-Save Integration**: The `VersioningContext` integrates with auto-save mechanisms to preserve document state automatically.

**Real-Time Synchronization**: Changes made in one browser tab are synchronized to other tabs through Supabase's real-time capabilities.

**Error Recovery**: The system can recover from partial failures by restoring state from the most recent successful save point.

### Advanced Context Patterns

#### State Machine Integration

The context architecture integrates with finite state machines to ensure valid state transitions and prevent invalid operations. The following diagram shows the component state machine with valid transitions:

```mermaid
stateDiagram-v2
    [*] --> idle: Initial State
    
    idle --> loading: Start Loading
    loading --> loaded: Content Loaded
    loading --> error: Load Failed
    loading --> idle: Reset/Cancel
    
    loaded --> loading: Reload Content
    loaded --> preserved: User Preserves
    loaded --> locked: User Locks
    loaded --> error: Update Failed
    
    preserved --> loading: Force Reload
    preserved --> locked: Lock Content
    
    locked --> loading: Unlock & Reload
    locked --> preserved: Unlock to Preserved
    
    error --> idle: Reset
    error --> loading: Retry
    
    note right of preserved
        Content preserved
        from updates
    end note
    
    note right of locked
        Content locked
        against changes
    end note
```

```typescript
const canTransition = componentStateMachine.canTransition(
  existing.status,
  update.status,
  {
    componentId: existing.id,
    componentType: existing.type,
    dependencies: existing.dependencies,
    hasError: Boolean(update.error)
  }
)

if (!canTransition) {
  logger.error('ComponentProvider', 'updateComponent', 
    `Invalid status transition from ${existing.status} to ${update.status}`)
  return
}
```

This integration ensures that component lifecycle transitions follow valid patterns and provides clear error messages when invalid transitions are attempted.

#### Performance Optimization Through Debouncing

The context system implements sophisticated debouncing patterns that balance responsiveness with performance:

```typescript
const debouncedUpdateGroupStatus = debounce((groupId: string) => {
  groupStatusManager.calculateGroupStatus(groupId)
}, 50)
```

Debouncing is applied at multiple levels including group status calculations, auto-save operations, and UI updates to prevent excessive computation during rapid state changes.

#### Memory-Scoped Resource Management

The architecture implements scoped memory management that tracks resource allocation and provides automatic cleanup:

```typescript
const scopedMemoryRef = useRef<ReturnType<typeof memoryManager.createScope> | null>(null)

if (!scopedMemoryRef.current) {
  scopedMemoryRef.current = memoryManager.createScope(`editor-${reportId || 'new'}`)
}

// Cleanup on unmount
React.useEffect(() => {
  return () => {
    scopedMemoryRef.current?.cleanupAll()
  }
}, [reportId])
```

This approach ensures that resources allocated during editor operation are properly tracked and cleaned up, preventing memory leaks in long-running sessions.

### Context-Specific Implementation Details

#### 1. DocumentContext - Central State Orchestration
- **Location**: `context/DocumentContext.tsx`
- **Purpose**: Serves as the primary state coordinator for the entire editor system
- **Key Features**:
  - **Editor Instance Management**: Maintains the TipTap editor instance and provides it to all dependent contexts
  - **Content State Tracking**: Tracks content changes with intelligent dirty state detection that distinguishes between user and system changes
  - **Entity/Run Association**: Manages document association with specific entities and analysis runs for report generation
  - **Auto-Save Coordination**: Coordinates with auto-save mechanisms while preventing conflicts with user editing
  - **Component Registration Hub**: Provides central registration point for report components with hierarchy management
  - **Citation Management**: Handles citation registration and deduplication for document references

**Advanced Implementation Details**:
- Implements a sophisticated reducer with 15+ action types handling everything from editor initialization to complex component relationship management
- Uses ref-based state sharing to prevent stale closures in async operations and debounced functions
- Provides entity change notification system that enables reactive updates across the editor when data context changes
- Implements render tracking and performance monitoring to identify optimization opportunities

#### 2. ComponentContext - Lifecycle and Hierarchy Management
- **Location**: `context/component/ComponentContext.tsx`
- **Purpose**: Manages the complete lifecycle of report components with sophisticated hierarchy and dependency tracking
- **Key Features**:
  - **State Machine Integration**: Uses finite state machine validation to ensure valid component status transitions
  - **Hierarchy Management**: Maintains parent-child relationships with automatic orphan adoption when parents are registered
  - **Component Validation**: Validates component registration with comprehensive error checking and recovery
  - **Bulk Operations**: Supports bulk component operations for efficient updates during report generation
  - **Component Queries**: Provides sophisticated querying capabilities for components by type, status, parent, and custom criteria

**Advanced Implementation Details**:
- Implements orphaned component management that temporarily holds child components when parents aren't yet registered
- Uses Map-based state storage for O(1) component lookups with immutable update patterns
- Integrates with logging and performance monitoring systems for debugging component lifecycle issues
- Provides tree-building capabilities for hierarchical component visualization
- **Component Hierarchy Tracking**: The `findParentReportGroup` function traverses the TipTap document tree to establish parent-child relationships based on document structure rather than explicit configuration
- **Direct Children vs Descendants**: Groups distinguish between direct children (immediate nested components) and all descendants (entire subtree), with status calculations based only on direct children for accurate hierarchical representation

#### 3. DependencyContext - Graph-Based Dependency Resolution
- **Location**: `context/dependency/DependencyContext.tsx`
- **Purpose**: Implements sophisticated dependency management with graph algorithms and circular dependency detection

The dependency resolution system manages complex component relationships through a sophisticated graph-based architecture:

```mermaid
graph TB
    subgraph "Dependency Resolution Flow"
        A[Component Registration] --> B{Has Dependencies?}
        B -->|Yes| C[Add to Dependency Graph]
        B -->|No| D[Mark Ready]
        
        C --> E{Circular Check}
        E -->|Valid| F[Add to Wait Queue]
        E -->|Circular| G[Reject Registration]
        
        F --> H[Wait for Dependencies]
        H --> I{Dependencies Ready?}
        I -->|Yes| J[Resolve Waiter]
        I -->|No| K[Continue Waiting]
        
        J --> L[Update Component Status]
        K --> M{Timeout Reached?}
        M -->|Yes| N[Timeout Error]
        M -->|No| H
    end

    subgraph "Graph Operations"
        O[Forward Graph] 
        P[Reverse Graph]
        Q[Waiter Queues]
        R[Circular Detection]
    end

    C --> O
    C --> P
    F --> Q
    E --> R

    style A fill:#e3f2fd
    style D fill:#e8f5e8
    style G fill:#ffebee
    style N fill:#ffebee
    style J fill:#e8f5e8
```

- **Key Features**:
  - **Dependency Graph Management**: Maintains both forward and reverse dependency graphs for efficient querying
  - **Circular Dependency Detection**: Implements depth-first search algorithm to detect and prevent circular dependencies
  - **Promise-Based Waiting**: Provides async waiting mechanisms that resolve when dependencies become available
  - **Timeout Management**: Configurable timeouts prevent infinite waiting when dependencies fail to resolve
  - **Dependency Chain Analysis**: Calculates complete dependency chains for complex component relationships

**Advanced Implementation Details**:
- Uses Set-based dependency storage for efficient membership testing and graph operations
- Implements waiter queues that automatically resolve when dependencies become available
- Provides dependency visualization data for debugging complex component relationships
- Uses topological sorting concepts for optimal dependency resolution ordering

#### 4. VersioningContext - Document History and Auto-Save
- **Location**: `context/versioning/VersioningContext.tsx`
- **Purpose**: Manages comprehensive document versioning with automated backup and recovery capabilities
- **Key Features**:
  - **Automatic Version Creation**: Creates versions at configurable intervals with intelligent change detection
  - **Version History Management**: Maintains complete version history with cleanup policies for storage optimization
  - **Diff Preparation**: Prepares version data for future diff visualization capabilities
  - **Auto-Save Integration**: Coordinates with editor updates to provide seamless auto-save functionality
  - **Recovery Operations**: Enables rollback to any previous version with conflict resolution

**Advanced Implementation Details**:
- Uses debounced auto-save creation to balance data safety with performance
- Implements version cleanup policies that retain manual versions while managing auto-save storage
- Integrates with Supabase for persistent version storage with optimistic updates
- Provides hooks into editor update events with filtering to prevent version creation during system updates

### Context Testing Patterns and Strategies

#### Unit Testing Context Logic

The context architecture enables comprehensive unit testing through several patterns:

**Reducer Testing**: Context reducers can be tested in isolation with predictable state transitions:

```typescript
describe('DocumentContext Reducer', () => {
  it('should handle component registration correctly', () => {
    const initialState = /* ... */
    const action = { type: 'COMPONENT_REGISTERED', component: mockComponent }
    const newState = documentReducer(initialState, action)
    
    expect(newState.components.has(mockComponent.id)).toBe(true)
    expect(newState.components.get(mockComponent.id)).toEqual(mockComponent)
  })
})
```

**Hook Testing**: Custom hooks can be tested using React Testing Library's `renderHook`:

```typescript
describe('useComponentRegistration', () => {
  it('should handle orphaned component adoption', async () => {
    const { result } = renderHook(() => useComponentRegistration(mockProps))
    
    act(() => {
      result.current.registerComponent(childComponent)
      result.current.registerComponent(parentComponent)
    })
    
    await waitFor(() => {
      expect(mockUpdateGroupStatus).toHaveBeenCalledWith(parentComponent.id)
    })
  })
})
```

#### Integration Testing Cross-Context Behavior

Integration tests verify that contexts work correctly together:

```typescript
describe('Context Integration', () => {
  it('should propagate entity changes across all contexts', async () => {
    const { result } = renderContextHierarchy()
    
    act(() => {
      result.current.document.setEntityRun('ENT-123', 'RUN-456')
    })
    
    await waitFor(() => {
      expect(result.current.component.getComponentsByStatus('loading')).toHaveLength(3)
      expect(result.current.dependency.checkDependencies('comp-1')).toBe(false)
    })
  })
})
```

#### End-to-End Context Testing

E2E tests verify complete workflows across the context system:

```typescript
test('complete report generation workflow', async () => {
  await page.goto('/editor/new')
  
  // Verify context initialization
  await expect(page.locator('[data-testid="editor-initialized"]')).toBeVisible()
  
  // Trigger component registration
  await page.click('[data-testid="add-report-section"]')
  
  // Verify dependency resolution
  await expect(page.locator('[data-testid="component-loaded"]')).toHaveCount(1)
  
  // Verify auto-save functionality
  await page.type('[data-testid="editor-content"]', 'Test content')
  await expect(page.locator('[data-testid="auto-save-indicator"]')).toContainText('Saved')
})
```

This comprehensive context architecture demonstrates sophisticated React patterns while maintaining clean separation of concerns, testability, and performance optimization. The system enables complex document editing workflows while providing the flexibility and extensibility needed for future enhancements.

### Provider Hierarchy

All contexts are composed through the `EditorProvider`:

```typescript
<EditorProvider reportId={id} onSave={handleSave}>
  <DocumentProvider>
    <ComponentProvider>
      <DependencyProvider>
        <VersioningProvider>
          {/* Editor content */}
        </VersioningProvider>
      </DependencyProvider>
    </ComponentProvider>
  </DocumentProvider>
</EditorProvider>
```

### State Machine

The editor uses a state machine to ensure valid component status transitions:

```typescript
// Valid transitions:
unregistered -> registering -> waiting/loading -> loaded/error
loaded -> refreshing -> loaded/error
// And more...
```