# Collaboration Features

The editor implements a sophisticated real-time collaboration system that combines multiple approaches to deliver seamless multi-user editing experiences. The architecture leverages Supabase's real-time capabilities alongside YJS for conflict-free document synchronization, providing robust collaborative editing with comprehensive user management and access control.

## Real-time Document Synchronization Architecture

### YJS Integration with Supabase Provider

The collaboration system is built on YJS (Yjs), a mature Conflict-Free Replicated Data Type (CRDT) framework that ensures document consistency across multiple concurrent editors. The custom `SupabaseProvider` class bridges YJS with Supabase's real-time infrastructure, creating a hybrid approach that combines the reliability of database persistence with the performance of WebSocket-based real-time updates.

The `SupabaseProvider` extends the EventEmitter pattern to provide a unified interface for document synchronization. Configuration requires a document name identifier, a YJS document instance, an optional awareness instance for user presence tracking, and database table details specifying the schema and column mappings for persistent storage. Document updates are debounced at 1-second intervals to prevent excessive database writes while maintaining responsiveness.

The synchronization process operates through a dual-channel architecture. Document content changes flow through YJS
update events that trigger database upserts to the `doc_documents` table, while real-time broadcast messages propagate
changes to active collaborators through Supabase's RealtimeChannel system. This design ensures that document state
persists beyond user sessions while enabling immediate collaboration feedback.

### Database Schema and Document Model

The collaboration system utilizes a sophisticated database schema centered around the `doc_documents` table. Core fields
include `id` (string primary key), `title` and `content` (HTML representation), `data` (TipTap JSON structure), and
comprehensive audit fields tracking `created_by`, `updated_by`, `created_at`, and `updated_at` timestamps. The
`is_public` boolean controls document visibility, while the flexible `metadata` JSON field stores collaboration settings
and extended document properties.

Document versioning operates through the `doc_versions` table, which maintains a complete history of document states.
Each version includes a sequential `version_number`, `change_summary` describing the modifications, and an
`is_auto_save` flag distinguishing automatic saves from manual version creation. The versioning system automatically
retains the five most recent auto-save versions while preserving all manual versions indefinitely.

Document relationships are enforced through foreign key constraints linking `created_by` and `updated_by` fields to the `profiles` table, ensuring referential integrity and enabling user attribution across all document operations. Row-level security policies control access based on ownership, shared permissions, and public visibility settings.

## User Presence and Awareness System

### Real-time Presence Tracking

The `usePresence` hook implements comprehensive user presence tracking that extends beyond simple online/offline status to include detailed editor state information. Presence updates capture cursor position, text selection ranges, and user metadata including name, avatar, and assigned color for visual identification.

Presence synchronization operates through Supabase's channel-based broadcasting system, with each document establishing a dedicated channel namespace (`document:${documentId}`). User presence state includes cursor positions represented as ProseMirror document coordinates, text selections with start/end positions and selected text content, and user identification details with avatars and color assignments for visual differentiation.

The system implements intelligent debouncing to balance real-time responsiveness with network efficiency. Selection updates trigger immediate presence broadcasts, but subsequent updates within a 2-second window are debounced to prevent excessive network traffic. Page visibility changes automatically trigger presence updates, ensuring accurate online status even when users switch browser tabs.

### Collaborative Cursor and Selection Display

Visual presence indicators provide immediate feedback about collaborator activity within the document. The `SupabaseCollaborationToolbar` renders active user avatars with custom color assignments, displaying up to five users directly with overflow indication for larger teams. Each user receives a persistent color assignment that remains consistent across sessions, ensuring visual continuity in collaborative editing scenarios.

Cursor position tracking integrates with TipTap's editor state management to capture precise document coordinates as users navigate and edit content. Selection ranges are transmitted with full context, including selected text content, enabling advanced features like highlighting collaborator selections and providing context-aware presence information.

## Change Tracking and Conflict Resolution

### AI-Generated Content Change Tracking

The `ChangeTrackingExtension` provides sophisticated change tracking specifically designed for AI-assisted editing workflows. The system implements two custom ProseMirror marks: `InsertionMark` for content additions and `DeletionMark` for content removals, each carrying metadata about the operation including user attribution, timestamp, and change description.

Change visualization employs color-coded styling with green highlighting for insertions (`bg-green-100 text-green-800`) and red highlighting with strikethrough styling for deletions (`bg-red-100 text-red-800 line-through`). Interactive hover controls appear dynamically, providing accept/reject buttons with intelligent positioning that avoids viewport boundaries and cursor interference.

The change resolution system supports both individual and bulk operations. Individual changes can be accepted or rejected through hover interactions, while bulk operations process all pending changes in document order. Position-based change operations include intelligent mark detection with fallback search algorithms that locate change marks within a ±3 position range to handle dynamic document layouts.

### Conflict Resolution through CRDT Architecture

YJS provides automatic conflict resolution through its CRDT implementation, eliminating the need for manual merge conflict resolution in most scenarios. The system handles concurrent edits by preserving user intent while maintaining document consistency across all connected clients. Character-level operations are automatically merged, while structural changes like paragraph insertion and deletion are resolved through operational transformation.

The database persistence layer provides additional conflict resolution through optimistic updates with automatic retry mechanisms. Failed updates trigger error states that are communicated to users through the UI, while successful synchronization triggers save completion events that update the editor's status indicators.

## Document Versioning and History Management

### Automatic Version Creation and Management

The versioning system operates through the `useDocumentVersioning` hook, which provides both automatic and manual version creation capabilities. Auto-save versions are created at configurable intervals (default 5 minutes) whenever content changes are detected, providing continuous backup without user intervention. Manual versions can be created with custom change summaries for milestone documentation.

Version management includes automatic cleanup of auto-save versions, retaining only the five most recent automatic saves while preserving all manual versions indefinitely. This approach balances storage efficiency with comprehensive history preservation, ensuring that accidental data loss is prevented while managing database growth.

The versioning API provides methods for creating document versions (`createDocumentVersion`), triggering auto-save versions (`triggerAutoSaveVersion`), and forcing immediate saves (`triggerImmediateSave`). Each operation includes comprehensive error handling and status reporting through the document context's dispatch system.

### Version Restoration and Diff Visualization

Version history browsing enables users to examine previous document states through the history panel interface. Each version entry displays creation timestamp, author information, change summary, and version type (manual vs. auto-save). Version restoration creates a new version containing the restored content, preserving the complete history chain.

The system prepares for future diff visualization capabilities by maintaining both HTML content and TipTap JSON data structures for each version. This dual-format approach enables both human-readable content comparison and structural diff analysis for advanced version comparison features.

## Access Control and Permission Management

### Multi-tier Permission System

Document access control operates through a sophisticated permission system with three primary access levels: read-only access for viewing documents without modification capabilities, write access enabling content editing and commenting, and admin access providing full document control including sharing, permission management, and deletion capabilities.

Permission inheritance follows a hierarchy where document creators automatically receive admin permissions, shared users receive permissions based on invitation settings, and public documents grant default permissions configured by the document owner. The system supports both explicit permission grants through user invitations and implicit permissions through public sharing with configurable default access levels.

### Authentication and Authorization Flow

The TipTap authentication route (`/api/tiptap/auth`) provides JWT-based authentication for collaborative editing sessions. Token generation includes user identification, document access permissions, and session metadata with configurable expiration times (default 1 hour). The authentication system integrates with Supabase's user management, ensuring consistent identity across all application features.

Document access validation occurs at multiple levels, including API route authentication, database row-level security policies, and client-side permission checks. This layered approach prevents unauthorized access while maintaining optimal performance for legitimate users.

## WebSocket Communication and Real-time Infrastructure

### Supabase Realtime Integration

Real-time communication flows through Supabase's WebSocket-based Realtime API, which provides guaranteed message delivery and automatic reconnection handling. Each document establishes a dedicated channel that facilitates both document synchronization and presence updates through distinct event types.

Document update events carry YJS state updates encoded as base64 strings, enabling efficient transmission of document changes. Awareness events broadcast user presence information including cursor positions, selection states, and user metadata. The channel subscription system handles connection state management, providing status updates for connected, disconnected, and error states.

### Network Resilience and Offline Support

The collaboration system implements comprehensive network resilience through automatic reconnection logic and offline state management. Connection status is continuously monitored and communicated to users through visual indicators in the collaboration toolbar. Disconnection events trigger cleanup of remote user presence states while preserving local document state.

Document changes made during offline periods are automatically synchronized when connectivity resumes, leveraging YJS's conflict resolution capabilities to merge offline changes with remote updates. This approach ensures that collaborative editing remains functional even in unstable network conditions.

## Collaborative Document Management

### Document Creation and Sharing Workflows

Document creation supports both immediate collaboration enablement and private document workflows. New documents are automatically configured for collaborative editing with the creator receiving admin permissions. The `useCollaborativeDocument` hook provides comprehensive document lifecycle management including creation, updates, deletion, and permission management.

Sharing workflows support both link-based sharing for public documents and explicit user invitations for private documents. Public documents can be configured with default permissions (read or write), while private documents require explicit permission grants. The sharing interface provides granular control over document visibility and access levels.

### Multi-user Editing Workflows

Concurrent editing scenarios are handled through YJS's operational transformation, which ensures that simultaneous modifications preserve user intent while maintaining document consistency. Character insertions, deletions, and formatting changes are automatically merged, while structural modifications like list item creation and paragraph restructuring are resolved through conflict-free operations.

The collaborative editing experience includes visual feedback for user actions, real-time cursor and selection indicators, and immediate content synchronization. Edit conflicts are resolved automatically at the character level, while higher-level conflicts (such as conflicting formatting changes) are resolved through last-writer-wins semantics with appropriate user notification.

## Integration with AI Features

### AI-Generated Content Collaboration

AI-generated content integrates seamlessly with the collaboration system through the change tracking extension. AI modifications are visually distinguished from human edits, appearing with distinctive styling and metadata indicating AI authorship. Collaborative review of AI changes enables teams to collectively evaluate and approve AI-generated content.

The AI integration maintains full collaboration compatibility, ensuring that AI operations don't interfere with concurrent human editing. AI-generated changes are treated as standard document modifications within the YJS framework, allowing them to be merged with human edits through the same conflict resolution mechanisms.

### Collaborative AI Assistance

The AI chat panel supports collaborative AI assistance, where AI responses and document modifications are visible to all collaborators in real-time. AI-generated changes are broadcast through the same channels as human edits, ensuring that all team members see AI modifications immediately.

AI change tracking enables collaborative review workflows where team members can collectively accept or reject AI suggestions. The bulk change operations support team-based decision making, allowing designated users to approve or reject multiple AI suggestions simultaneously.
