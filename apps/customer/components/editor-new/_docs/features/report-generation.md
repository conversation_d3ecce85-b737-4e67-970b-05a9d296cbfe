# Report Generation

The editor implements a sophisticated report generation system that combines streaming data processing, hierarchical document structures, and dynamic component templates to create comprehensive analytical reports. The system operates through multiple interconnected subsystems that handle everything from data fetching to template-based document creation.

## Report Pipeline Architecture

The report generation system follows a multi-stage pipeline architecture designed for scalability and real-time feedback. The foundation rests on the `streamHierarchicalReport` function in `streaming/stream.ts`, which orchestrates the entire report creation process through parallel section processing and intelligent caching mechanisms.

**Pipeline Stages:**

1. **Data Preparation and Validation**: Entity and run validation, model section categorization, and dependency resolution ensure that all required data is available before processing begins. The system validates entity IDs, run IDs, and model configurations while categorizing sections into environmental, social, and governance classifications using the `categorizeSections` utility.

2. **Parallel Section Processing**: Multiple report sections are processed simultaneously using Promise-based concurrency control. The system implements intelligent batching (4 concurrent requests maximum) to prevent API overload while maintaining responsiveness. Each section follows its own lifecycle from registration through loading, processing, and completion.

3. **Content Transformation and Integration**: Raw API responses are processed through a sophisticated markdown transformation pipeline that handles heading restructuring, citation integration, and content sanitization. The `processMarkdown` function applies whitelist-based filtering and heading transformations to ensure consistent document structure.

4. **Real-time Progress Tracking**: The `ReportProgressProvider` context system provides granular progress tracking with status updates for each section. Progress indicators show loading states, completion percentages, and error conditions with real-time updates through the component registration system.

5. **Final Assembly and Rendering**: Completed sections are assembled into a cohesive document structure using custom HTML tags (`<report-section>`, `<report-sub-section>`) that maintain semantic meaning while enabling flexible styling and navigation.

## Streaming Implementation and Real-time Updates

The streaming system operates through multiple layers of abstraction that provide both performance and user experience benefits. The `StreamingHierarchicalReport` component implements a sophisticated state management system that handles concurrent section generation while providing immediate user feedback.

**Streaming Architecture Components:**

**Section-Level Streaming**: Each report section operates as an independent streaming unit with its own AbortController for cancellation support. The `streamSection` function handles both cached and live content generation, implementing intelligent caching with version-specific cache keys (`v21-report-${hashId}-${endpoint}-${includeDisclosures}`).

**Progress Context System**: The `ReportProgressProvider` manages section registration, status tracking, and progress aggregation across the entire report generation process. Progress updates flow through a centralized context that prevents race conditions and ensures consistent state management.

**Real-time Content Updates**: The `onSectionReady` callback system enables immediate content display as sections complete, providing users with progressive report building rather than waiting for complete generation. Content updates use React's `startTransition` API to maintain UI responsiveness during heavy content processing.

**Error Handling and Recovery**: Comprehensive error handling at each streaming layer includes automatic retry mechanisms, graceful degradation for failed sections, and user-friendly error messages that don't block overall report generation.

## Component-Based Report Building System

The report generation system implements a hierarchical component architecture that enables flexible document structures through reusable building blocks. This system combines TipTap extensions with React components to create interactive, editable report structures.

**Core Component Types:**

**ReportSectionExtension**: Implements individual report sections with configurable endpoints, automatic content loading, and status management. Each section includes attributes for endpoint configuration, heading transformation rules, locking mechanisms, and preservation settings. The component implements intelligent loading logic that responds to entity/run changes and provides manual refresh capabilities.

**ReportGroupComponent**: Provides hierarchical organization of multiple report sections with dependency management, aggregate status calculation, and comprehensive progress visualization. Groups can contain nested sections and implement parent-child relationships that affect loading order and status propagation. The component features visual progress indicators including progress bars, completion counters (X/Y format), and detailed status tooltips. Importantly, groups calculate their status based only on their direct children, not all descendants, ensuring accurate status representation at each hierarchy level.

**ReportSummaryExtension**: Generates executive summaries and section overviews by analyzing completed sections and generating synthetic content. Summary components can reference other sections by ID and automatically regenerate when dependent sections change.

**Advanced Component Features:**

**Dynamic Endpoint Resolution**: Components support placeholder-based endpoint configuration (`[ENTITY_ID]`, `[RUN_ID]`) that automatically resolves based on current document context. This enables template reuse across different entities and analysis runs.

**Heading Transformation System**: Sophisticated heading transformation capabilities include removal, depth adjustment, and hierarchical restructuring. The `transformHeadings` utility supports multiple transformation modes (`remove`, `reset`, `keep`) with automatic depth calculation based on document structure.

**Status Management and Lifecycle**: Components implement a comprehensive status system (`idle`, `loading`, `loaded`, `error`, `preserved`, `locked`) with automatic state transitions and persistence. Status changes trigger appropriate UI updates and can be used to control component behavior. Report groups calculate their aggregate status using a hierarchical priority system where `loading` takes precedence over `error`, which takes precedence over `loaded`. Groups monitor only their direct children for status calculation, with a force update mechanism to ensure status synchronization when mismatches are detected between visual progress (e.g., 27/27 complete) and the group's status.

**Content Preservation and Locking**: Advanced preservation mechanisms allow users to lock sections against automatic updates or preserve specific content across regeneration cycles. This enables selective report updates while maintaining important customizations.

**Progress Visualization**: Report groups display comprehensive progress information through multiple visual indicators. The `calculateGroupProgress` function analyzes direct children to provide real-time progress metrics. Visual elements include:
- Progress bars that appear automatically when group completion is below 100%
- Counter badges in X/Y format showing completed vs total direct children
- Interactive tooltips displaying detailed breakdowns (loading count, error count, pending count)
- Color-coded status indicators that change based on group state (green for loaded, yellow for loading, red for error)

## Template Engine Integration and Customization

The template system provides both static and dynamic document creation capabilities through a sophisticated template generation engine. The `DocumentTemplates` component integrates with the `generateDynamicTemplates` function to provide context-aware template creation.

**Static Template System:**

Static templates provide predefined document structures for common use cases including meeting notes, project proposals, and weekly reports. Templates support both markdown content and TipTap JSON data structures, enabling complex nested layouts with proper component initialization.

**Dynamic Template Generation:**

The `generateDynamicTemplates` function creates report templates based on live database queries of available model sections. This system automatically generates templates for different sustainability frameworks (SDG, CSRD, EKO, etc.) with appropriate section configurations and endpoint mappings.

**Template Structure:**

Dynamic templates follow a consistent hierarchical structure:
- Table of Contents with automatic heading detection
- Executive Summary with dependency-based generation
- Categorized Impact Sections (Ecological, Social, Governance)
- Reliability and Transparency Analysis sections
- Automatic reference and citation management

**Template Customization:**

Templates support extensive customization through attribute configuration including section endpoints, prompt customization, heading transformation rules, and dependency relationships. The template system enables organizational-specific customizations while maintaining compatibility with the core report generation pipeline.

## Data Binding and Dynamic Content Injection

The report system implements sophisticated data binding mechanisms that connect live data sources with document structures. This system enables real-time updates and ensures report accuracy across changing data conditions.

**Entity-Run Binding**: Reports are automatically bound to specific entity and run combinations through the document context system. Changes to entity or run selection trigger automatic section reloading with proper data substitution.

**Citation Integration**: The citation system provides automatic reference management with real-time citation database lookups. Citations are processed during content transformation and automatically linked to document page references with proper formatting and validation.

**Dynamic Content Substitution**: Template placeholders are resolved at runtime using current document context, enabling template reuse across different analytical scenarios. The substitution system supports complex parameter passing and validation.

**Real-time Data Synchronization**: The document context system monitors entity and run changes, automatically triggering content updates when underlying data changes. This ensures report accuracy without manual intervention.

## Multi-format Export Capabilities

The export system provides comprehensive multi-format document generation through the `export-utils.ts` module. Export capabilities include PDF, DOCX, HTML, and Markdown formats with format-specific optimizations.

**PDF Export Implementation:**

PDF generation leverages browser-based printing through dedicated print routes (`/customer/documents/${documentId}/print`) that provide optimized print styling. The system automatically saves document content before opening print dialogs and applies print-specific CSS for optimal page layouts.

**DOCX Export with Fallback:**

DOCX export implements a dual-strategy approach using `prosemirror-docx` as the primary method with a custom `docx` library fallback. The system handles complex document structures including tables, lists, headings, and formatting while providing graceful degradation for unsupported elements.

**HTML Export with Embedded Styling:**

HTML export generates complete, self-contained documents with embedded CSS and proper document structure. The export includes comprehensive styling for typography, tables, code blocks, and interactive elements while maintaining accessibility features.

**Markdown Export with Citation Preservation:**

Markdown export utilizes TipTap's native markdown serialization with custom extensions for citation preservation and structural element handling. The export maintains document semantics while ensuring compatibility with standard markdown processors.

## Report Preview and Rendering Mechanisms

The preview system implements sophisticated rendering mechanisms that handle complex document structures while maintaining performance and user experience.

**Progressive Rendering**: The `StreamingHierarchicalReport` component implements progressive rendering that displays content as it becomes available rather than waiting for complete document generation. This approach provides immediate user feedback and maintains perceived performance.

**Print Preview Optimization**: Dedicated print routes provide optimized preview experiences with print-specific styling, page break management, and layout optimization. The system includes print-friendly typography and removes interactive elements for clean document presentation.

**Real-time Content Updates**: The rendering system supports real-time content updates through React's concurrent features, enabling smooth content transitions without blocking user interactions.

**Error Boundary Integration**: Comprehensive error boundaries prevent rendering failures from affecting overall document stability while providing graceful degradation for problematic content sections.

## Print Optimization and Layout Management

Print optimization encompasses both technical implementation and user experience considerations to ensure high-quality document output across different printing scenarios.

**Print Route Architecture**: Dedicated print routes (`(print)/layout.tsx`) provide isolated rendering environments optimized for print output. These routes include print-specific styling, navigation removal, and layout optimization for paper formats.

**CSS Print Media Queries**: Comprehensive print styling through Tailwind CSS print variants ensures optimal layout across different paper sizes and printer configurations. The system includes page break management, margin optimization, and typography scaling for print readability.

**Print Button Integration**: The report generation system includes integrated print functionality that automatically saves document content before opening print dialogs, ensuring exported PDFs reflect the most recent document state.

**Layout Preservation**: Print layouts maintain document structure and formatting while adapting to print constraints. The system preserves tables, images, and complex layouts while ensuring readability in printed format.

## Report Metadata and Configuration Management

The metadata system provides comprehensive configuration management for report generation parameters, user preferences, and document settings.

**Configuration Storage**: Report configurations are stored as document attributes within TipTap nodes, enabling persistent settings that survive document reloading and sharing. Configuration includes endpoint mappings, transformation rules, and user preferences.

**Metadata Persistence**: Document metadata is stored in Supabase doc_documents table with JSON-based flexible schema
supporting extensible properties including collaboration settings, template configurations, and entity associations.

**User Preference Management**: The system supports user-specific preferences for report generation including disclosure settings, heading preferences, and section customizations that persist across sessions.

**Version Configuration**: Configuration versioning ensures compatibility across different template versions and system updates while maintaining backward compatibility for existing documents.

## Integration with Entity Data and Analysis Results

The report system implements deep integration with the underlying analytical data infrastructure to provide accurate, real-time reporting capabilities.

**Entity Context Integration**: The `useEntity` context provides seamless integration with entity selection, run management, and data loading states. Reports automatically respond to entity changes and adapt content accordingly.

**Analysis Data Binding**: Direct integration with analysis results including flags, model sections, claims, promises, and cherry-picking analysis ensures reports reflect current analytical state without manual data management.

**Real-time Data Updates**: The system monitors underlying data changes and triggers appropriate report updates when analysis results change, ensuring report accuracy across evolving analytical scenarios.

**Cross-reference Resolution**: Automatic resolution of cross-references between different analytical components enables comprehensive reporting that spans multiple data sources and analytical dimensions.

## Performance Optimizations for Large Reports

Performance optimization strategies ensure scalable report generation even for complex, multi-section documents with extensive data requirements.

**Concurrent Processing Limits**: Intelligent concurrency control prevents API overload while maximizing parallel processing benefits. The system implements batching strategies that balance performance with resource constraints.

**Caching Strategies**: Multi-level caching including section-level content caching, template caching, and query result caching reduces redundant processing and improves response times for frequently accessed content.

**Memory Management**: The `MemoryManager` utility provides automatic resource cleanup, scoped memory tracking, and memory leak detection to maintain performance across long document sessions.

**Progressive Loading**: Section-based progressive loading enables immediate user interaction while background processing continues, improving perceived performance and user experience.

**Debounced Updates**: Strategic debouncing of status updates, progress reporting, and content changes prevents excessive re-rendering while maintaining responsive user interfaces.

## Report Sharing and Distribution Features

The sharing system provides comprehensive document distribution capabilities with appropriate access control and collaboration features.

**Public Document Sharing**: The `PublicDocumentViewer` component enables secure public sharing of reports with read-only access, print functionality, and citation display without editing capabilities.

**Collaboration Integration**: Full integration with the collaboration system enables real-time collaborative report editing with presence indicators, change tracking, and version management.

**Access Control Management**: Granular access control through Supabase RLS policies ensures appropriate document access while enabling flexible sharing workflows including organization-based permissions and public document capabilities.

**Export-based Distribution**: Multi-format export capabilities enable document distribution in user-preferred formats while maintaining content fidelity and accessibility features.

## Custom Component Development for Reports

The report system provides extensible architecture for custom component development, enabling organization-specific reporting requirements and specialized analytical displays.

**Component Extension Framework**: The TipTap extension system enables custom report components with full lifecycle management, status tracking, and integration with the broader report generation pipeline.

**Custom Node Development**: Guidelines and patterns for developing custom report nodes including proper attribute handling, content processing, and UI integration with the existing design system.

**Template Component Integration**: Custom components can be integrated into template systems with appropriate configuration interfaces and validation logic to ensure proper document generation.

**Advanced Component Patterns**: Implementation patterns for complex components including multi-section dependencies, custom data processing, and specialized rendering requirements that extend beyond standard report sections.
