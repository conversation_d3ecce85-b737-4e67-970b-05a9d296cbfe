# Document Management

The editor implements a comprehensive document management system that handles the complete document lifecycle from creation to deletion, with robust persistence, versioning, and backup mechanisms.

## Document Creation and Initialization Workflows

Document creation follows a multi-tier initialization pattern that supports both manual and automated workflows. The
`DocumentList` component provides the primary creation interface through the `handleCreateDocument` method, which
generates unique document identifiers using a timestamp-based pattern (
`doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`) and creates initial database records in the
`doc_documents` table.

The `useCollaborativeDocument` hook implements an auto-creation pattern that can automatically generate documents when a requested document ID doesn't exist. This pattern enables deep-linking to non-existent documents with automatic initialization using configurable default titles and content. Document initialization includes setting ownership relationships through the `created_by` and `updated_by` fields, establishing default metadata structures, and configuring initial collaboration settings.

Document templates extend the creation workflow through the `DocumentTemplates` component, which provides both static templates (meeting notes, project proposals, research templates) and dynamic templates generated from the `generateDynamicTemplates` function. Templates support both markdown content initialization and structured TipTap JSON data, enabling complex document structures with pre-configured report sections, grouped components, and automatic content generation hooks.

The template system integrates with entity and run selection through the `DocumentEntityRunSelector`, allowing users to create documents pre-configured with specific data contexts. This integration enables automatic population of report sections with entity-specific data and run-specific analytics when documents are created from templates.

## Document Loading and Persistence Mechanisms

Document persistence operates through a dual-layer architecture combining Supabase database storage with TipTap's JSON document model. The `ReportService` class provides the core persistence interface with `loadReport` and `saveReport` methods that handle both HTML content and structured TipTap JSON data. Document loading implements intelligent error handling that distinguishes between missing documents (PGRST116 errors) and actual database failures.

The persistence layer stores documents in multiple formats within the `doc_documents` table: HTML content for display
and export purposes, JSON data for TipTap editor state, title and metadata for document management, and audit fields
tracking creation and modification history. The database schema includes comprehensive indexing on `created_by`,
`updated_by`, and `updated_at` fields to optimize document retrieval and listing operations.

Real-time synchronization is achieved through Supabase's WebSocket-based real-time capabilities, with automatic subscription management in the `DocumentList` component. Document changes are broadcast to all connected clients through PostgreSQL's LISTEN/NOTIFY system, ensuring that document lists remain synchronized across multiple browser sessions and users.

## Auto-save Functionality and Intervals

Auto-save functionality is implemented through the `useSupabaseAutoSave` hook, which provides configurable automatic saving with intelligent change detection and conflict resolution. The auto-save system operates on multiple timers: a 5-second interval for incremental content saves and a 5-minute interval for automatic version creation.

Change detection utilizes a sophisticated algorithm that compares both content and normalized content (removing whitespace variations) to prevent false positives from editor state updates. The system tracks transaction metadata to distinguish between user-initiated changes and programmatic updates, preventing auto-save triggering during AI edits or collaborative synchronization.

Auto-save operations include comprehensive error handling with automatic retry mechanisms, status tracking for UI feedback, and integration with the manual save workflow. The system provides `manualSave` and `forceSave` methods that override change detection and timing restrictions for user-triggered save operations.

Version creation during auto-save follows a retention policy that maintains the five most recent automatic versions while preserving all manual versions indefinitely. This approach balances storage efficiency with comprehensive history preservation, ensuring that accidental data loss is prevented while managing database growth.

## Document Metadata Management

Document metadata is managed through a flexible JSON-based system that supports extensible document properties. The
`metadata` field in the `doc_documents` table stores structured information including collaboration settings, template
configurations, entity associations, and custom document properties.

The metadata system supports hierarchical organization through parent-child relationships, tagging systems for categorization and search, access control settings for sharing and permissions, and integration hooks for external systems. Metadata updates are handled through the `updateDocument` method in the `useCollaborativeDocument` hook, which provides atomic updates with automatic timestamp management.

Title management includes automatic title generation from document content, conflict resolution for duplicate titles, and integration with the template system for smart default naming. The title system supports both user-defined titles and automatically generated titles based on document content patterns.

## Document Templates and Initialization

The template system provides both static and dynamic document creation patterns through the `DocumentTemplates` component. Static templates include predefined content structures for common document types (meeting notes, project proposals, research documents, task lists), while dynamic templates are generated based on available data entities and analysis runs.

Template initialization supports multiple content formats including markdown text for simple content structures, TipTap JSON for complex structured documents, and hybrid formats that combine both approaches. The `multi-column-layout` template demonstrates sophisticated document structure creation with nested column blocks, hierarchical headings, and integrated table-of-contents generation.

The ESG report template showcases advanced template capabilities with automatic report section generation, component dependency management, and dynamic content population. Templates can include placeholder tokens for entity IDs and run IDs that are automatically replaced during document creation.

Template categorization and filtering enable users to quickly locate appropriate starting points for their documents. The system supports category-based organization, tag-based search, and template preview capabilities.

## File Operations (Save, Load, Export, Import)

File operations are handled through multiple specialized systems depending on the operation type. Save operations utilize the `ReportService` for database persistence and the `VersionService` for history management. Load operations include error handling for missing documents, automatic format conversion between storage and editor formats, and real-time synchronization with collaborative changes.

Export functionality is implemented through the `export-utils.ts` module, which provides multi-format export capabilities including PDF through browser print functionality, DOCX through both prosemirror-docx integration and custom docx library fallback, HTML with complete styling and embedded CSS, and Markdown through TipTap's markdown serialization.

The export system handles complex document structures including tables, lists, headings, and custom node types through specialized serialization functions. DOCX export includes sophisticated content mapping that converts TipTap JSON structures to Word document elements while preserving formatting and hierarchy.

PDF export leverages a dedicated print route (`/customer/documents/${documentId}/print`) that provides optimized print styling and layout. The print system automatically saves document content before opening the print dialog, ensuring that exported PDFs reflect the most recent document state.

Import functionality is designed around the template system and file upload integration through the `FileHandlerExtension`. The system supports drag-and-drop file uploads to Supabase storage with automatic URL embedding in document content.

## Document State Management and Lifecycle

Document state management follows a sophisticated lifecycle pattern implemented through the context system and service layer. The `DocumentContext` manages primary document state including load status, save status, content synchronization, and error conditions. The `ComponentContext` handles document component registration, status tracking, and dependency resolution for complex documents with multiple report sections.

Document lifecycle events include creation with automatic metadata initialization, loading with content restoration and collaboration setup, editing with change tracking and auto-save management, collaboration with real-time synchronization and conflict resolution, versioning with automatic and manual snapshot creation, and deletion with cascade cleanup of related records.

The state machine implemented in `componentStateMachine.ts` ensures valid state transitions for document components, preventing invalid operations and maintaining consistency across complex document structures. State transitions include `unregistered -> registering -> waiting/loading -> loaded/error` for component initialization and `loaded -> refreshing -> loaded/error` for content updates.

## Integration with Supabase for Persistence

Supabase integration provides the foundation for all document persistence operations through a comprehensive database
schema that supports collaborative editing, version control, and access management. The schema includes the
`doc_documents` table for primary document storage, `doc_versions` for version history, `doc_permissions` for access
control, `doc_comments` for collaborative annotations, and `doc_presence` for real-time user tracking.

Row-level security (RLS) policies ensure proper access control with organization-based permissions, ownership-based access rights, and public document sharing capabilities. The RLS system prevents unauthorized document access while enabling collaborative workflows and public document sharing.

Real-time capabilities are implemented through Supabase's WebSocket infrastructure with automatic subscription management in React components. Document changes are broadcast through PostgreSQL's LISTEN/NOTIFY system, providing immediate updates to all connected collaborators.

Authentication integration ensures that all document operations are performed within the context of authenticated users, with proper attribution for all changes and comprehensive audit trails for document modifications.

## Document Relationship Handling

Document relationships are managed through a flexible system that supports parent-child relationships, cross-document references, and component dependencies. The `DependencyContext` handles complex dependency resolution for documents with multiple interconnected components, ensuring proper load ordering and status propagation.

Component relationships are established through the `ReportGroupComponent` and `ReportSectionExtension` systems, which enable hierarchical document organization with nested components. Dependencies can be configured at the component level, enabling complex document structures where component loading depends on the successful completion of prerequisite components.

Cross-document relationships are supported through the citation system implemented in `CitationExtension`, which enables references to external documents and automatic link management. The citation system maintains referential integrity and provides automatic link validation.

## Backup and Recovery Mechanisms

Backup and recovery are implemented through multiple complementary systems that ensure comprehensive data protection. The version control system provides primary backup functionality through automatic version creation at configurable intervals, manual version snapshots for milestone preservation, and retention policies that balance storage efficiency with data protection.

Version recovery is implemented through the `VersionService` with methods for version restoration, content comparison, and selective recovery of specific document elements. The recovery system maintains complete document history while providing efficient access to recent versions.

Database-level backup is handled through Supabase's automatic backup systems, which provide point-in-time recovery and automatic failover capabilities. The RLS policies ensure that backup operations maintain proper access control and data isolation.

Export-based backup enables users to create local copies of their documents in multiple formats, providing an additional layer of data protection and enabling offline access to document content. The export system maintains document fidelity across different formats while providing human-readable backups.

Real-time synchronization provides implicit backup functionality by maintaining document state across multiple browser sessions and devices. The collaborative editing system ensures that document changes are immediately persisted and replicated, reducing the risk of data loss from browser crashes or network interruptions.
