'use client'

import React, { useEffect, useState } from 'react'
import { Badge } from '@ui/components/ui/badge'
import { createClient } from '@/app/supabase/client'
import { cn } from '@utils/lib/utils'

interface EntityOption {
  id: string
  name: string
}

interface RunOption {
  id: string
  name: string
  run_date?: string
  start_year?: number
  end_year?: number
  run_type?: string
}

interface DocumentEntityRunDisplayProps {
  entityId?: string | null
  runId?: string
  className?: string
}

export function DocumentEntityRunDisplay({
  entityId,
  runId = 'latest',
  className
}: DocumentEntityRunDisplayProps) {
  const [entityName, setEntityName] = useState<string>('')
  const [runName, setRunName] = useState<string>('')
  const [loading, setLoading] = useState(true)

  const supabase = createClient()

  useEffect(() => {
    async function fetchDisplayData() {
      try {
        setLoading(true)

        // Fetch entity name if entityId is provided
        if (entityId) {
          const { data: entityData, error: entityError } = await supabase
            .from('xfer_entities')
            .select('name')
            .eq('entity_xid', entityId)
            .single()

          if (!entityError && entityData) {
            setEntityName(entityData.name)
          } else {
            setEntityName(`Entity ${entityId}`)
          }
        } else {
          setEntityName('No entity selected')
        }

        // Fetch run name if runId is provided and not 'latest'
        if (runId && runId !== 'latest') {
          const { data: runData, error: runError } = await supabase
            .from('xfer_runs')
            .select('run_id, run_date, start_year, end_year, run_type')
            .eq('run_id', parseInt(runId, 10))
            .single()

          if (!runError && runData) {
            const runInfo = runData as any // Type assertion to avoid TypeScript issues
            setRunName(`Run ${runInfo.run_id} (${runInfo.run_date ? new Date(runInfo.run_date).toLocaleDateString() : 'No date'}) : ${runInfo.start_year} - ${runInfo.end_year || 'now'} (${runInfo.run_type})`)
          } else {
            setRunName(`Run ${runId}`)
          }
        } else {
          setRunName('Latest Run')
        }
      } catch (error) {
        console.error('Error fetching display data:', error)
        setEntityName(entityId ? `Entity ${entityId}` : 'No entity selected')
        setRunName(runId === 'latest' ? 'Latest Run' : `Run ${runId}`)
      } finally {
        setLoading(false)
      }
    }

    fetchDisplayData()
  }, [entityId, runId]) // supabase not included to prevent infinite loops

  if (loading) {
    return (
      <div className={cn('flex items-center gap-2', className)}>
        <Badge variant="secondary">Loading...</Badge>
        <Badge variant="outline">Loading...</Badge>
      </div>
    )
  }

  return (
    <div className={cn('flex items-center gap-2', className)}>
      <Badge variant="secondary">{entityName}</Badge>
      <Badge variant="outline">{runName}</Badge>
    </div>
  )
}
