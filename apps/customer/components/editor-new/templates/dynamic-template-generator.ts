import { createClient } from '@/app/supabase/client'
import { ModelSectionTypeV2 } from '@/types/model-section'

export interface DynamicTemplate {
  id: string
  name: string
  description: string
  category: string
  icon: string
  data: any
  tags: string[]
}

/**
 * Fetches all active model sections from xfer_model_sections
 */
async function fetchModelSections(): Promise<ModelSectionTypeV2[]> {
  const supabase = createClient()

  const { data, error } = await supabase
    .from('xfer_model_sections')
    .select('*')
    .eq('status', 'active')
    .order('model')
    .order('level')
    .order('section')

  if (error) {
    console.error('Error fetching model sections:', error)
    return []
  }

  // Filter out any records with null required fields and convert null values to undefined
  return (data || [])
    .filter(item => item.model && item.section) // Filter out items with null required fields
    .map(item => ({
      ...item,
      model: item.model!, // We know it's not null due to filter
      section: item.section!, // We know it's not null due to filter
      id: item.id ?? undefined,
      title: item.title ?? undefined,
      description: item.description ?? undefined,
      level: item.level ?? undefined,
      icon: item.icon ?? undefined,
      status: item.status ?? undefined,
    }))
}

/**
 * Groups model sections by model and level
 */
function groupSectionsByModel(sections: ModelSectionTypeV2[]): Record<string, Record<string, ModelSectionTypeV2[]>> {
  const grouped: Record<string, Record<string, ModelSectionTypeV2[]>> = {}

  sections.forEach(section => {
    if (!grouped[section.model]) {
      grouped[section.model] = {}
    }

    const level = section.level || 'governance' // Default to governance if no level
    if (!grouped[section.model][level]) {
      grouped[section.model][level] = []
    }

    grouped[section.model][level].push(section)
  })

  return grouped
}

/**
 * Creates report sections for a specific level (ecological, social, governance)
 * Returns both the sections and their IDs for use in summary components
 */
function createLevelSections(
  model: string,
  level: string,
  sections: ModelSectionTypeV2[],
): { sections: any[], sectionIds: string[] } {
  const sectionIds: string[] = []
  const reportSections = sections.map(section => {
    const sectionId = `${level}-${section.section}`
    sectionIds.push(sectionId)
    return {
      type: 'reportSection',
      attrs: {
        id: sectionId,
        prompt: `Analysis of ${section.title || section.section} impacts and initiatives`,
        title: section.title || section.section.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        endpoint: `/report/entity/[ENTITY_ID]/[RUN_ID]/harm/model/${model}/section/${section.section}?includeDisclosures=true`,
        headings: 'remove',
        depth: 4, // Sections are typically at depth 4 in dynamic templates
      },
    }
  })

  return { sections: reportSections, sectionIds }
}

/**
 * Creates the main report structure for a specific model
 */
function createModelReportStructure(model: string, groupedSections: Record<string, ModelSectionTypeV2[]>): any[] {
  const content: any[] = []

  // Table of Contents at the beginning
  content.push({
    type: 'tableOfContents',
    attrs: {
      id: 'table-of-contents',
      headings: null,
    },
  })

  // Executive Summary (will be generated after all sections)
  content.push({
    type: 'reportSummary',
    attrs: {
      id: 'exec-summary',
      prompt: `Executive summary focusing on key ${model.toUpperCase()} findings and overall assessment`,
      title: 'Executive Summary',
      summarize: 'report',
      headings: 'remove',
      depth: 1,
    },
  })

  // Main report group
  const reportGroup: any = {
    type: 'reportGroup',
    attrs: { id: 'report' },
    content: [],
  }

  // Introduction section
  reportGroup.content.push({
    type: 'reportSection',
    attrs: {
      id: 'introduction',
      prompt: 'Introduction to the analysis and methodology',
      title: 'Introduction',
      endpoint: `/report/entity/[ENTITY_ID]/[RUN_ID]/introduction?model=${model}&includeDisclosures=true`,
      headings: 'remove',
      depth: 2,
    },
  })

  // Ecological Impact section
  if (groupedSections.ecological && groupedSections.ecological.length > 0) {
    const { sections: ecologicalSections, sectionIds: ecologicalSectionIds } = createLevelSections(model, 'ecological', groupedSections.ecological)

    const ecologicalGroup = {
      type: 'reportGroup',
      attrs: { id: 'ecological' },
      content: [
        {
          type: 'heading',
          attrs: { level: 2 },
          content: [{ type: 'text', text: 'Ecological Impact' }],
        },
        {
          type: 'reportSummary',
          attrs: {
            id: 'ecological-summary',
            prompt: 'Summary of key ecological impacts, risks, and opportunities across all environmental dimensions',
            title: 'Ecological Impact Summary',
            summarize: ecologicalSectionIds.join(','),
            headings: 'remove',
            depth: 3,
          },
        },
        ...ecologicalSections,
      ],
    }
    reportGroup.content.push(ecologicalGroup)
  }

  // Social Impact section
  if (groupedSections.social && groupedSections.social.length > 0) {
    const { sections: socialSections, sectionIds: socialSectionIds } = createLevelSections(model, 'social', groupedSections.social)

    const socialGroup = {
      type: 'reportGroup',
      attrs: { id: 'social' },
      content: [
        {
          type: 'heading',
          attrs: { level: 2 },
          content: [{ type: 'text', text: 'Social Impact' }],
        },
        {
          type: 'reportSummary',
          attrs: {
            id: 'social-summary',
            prompt: 'Summary of key social impacts, community effects, and stakeholder considerations across all social dimensions',
            title: 'Social Impact Summary',
            summarize: socialSectionIds.join(','),
            headings: 'remove',
            depth: 3,
          },
        },
        ...socialSections,
      ],
    }
    reportGroup.content.push(socialGroup)
  }

  // Governance section
  if (groupedSections.governance && groupedSections.governance.length > 0) {
    const { sections: governanceSections, sectionIds: governanceSectionIds } = createLevelSections(model, 'governance', groupedSections.governance)

    const governanceGroup = {
      type: 'reportGroup',
      attrs: { id: 'governance' },
      content: [
        {
          type: 'heading',
          attrs: { level: 2 },
          content: [{ type: 'text', text: 'Governance' }],
        },
        {
          type: 'reportSummary',
          attrs: {
            id: 'governance-summary',
            prompt: 'Summary of key governance practices, compliance measures, and organizational oversight across all governance dimensions',
            title: 'Governance Summary',
            summarize: governanceSectionIds.join(','),
            headings: 'remove',
            depth: 3,
          },
        },
        ...governanceSections,
      ],
    }
    reportGroup.content.push(governanceGroup)
  }

  // Reliability Analysis section
  reportGroup.content.push({
    type: 'reportGroup',
    attrs: { id: 'reliability' },
    content: [
      {
        type: 'heading',
        attrs: { level: 2 },
        content: [{ type: 'text', text: 'Data Reliability' }],
      },
      {
        type: 'reportSection',
        attrs: {
          id: 'reliability-analysis',
          prompt: 'Assessment of data quality and reliability',
          title: 'Reliability Assessment',
          endpoint: `/report/entity/[ENTITY_ID]/[RUN_ID]/reliability?model=${model}&includeDisclosures=true`,
          headings: 'remove',
          depth: 3,
        },
      },
    ],
  })

  // Transparency Analysis section
  reportGroup.content.push({
    type: 'reportGroup',
    attrs: { id: 'transparency' },
    content: [
      {
        type: 'heading',
        attrs: { level: 2 },
        content: [{ type: 'text', text: 'Transparency & Disclosure' }],
      },
      {
        type: 'reportSection',
        attrs: {
          id: 'transparency-analysis',
          prompt: 'Analysis of transparency and disclosure practices',
          title: 'Transparency Analysis',
          endpoint: `/report/entity/[ENTITY_ID]/[RUN_ID]/transparency?model=${model}&includeDisclosures=true`,
          headings: 'remove',
          depth: 3,
        },
      },
    ],
  })

  // References section
  reportGroup.content.push({
    type: 'references',
    attrs: { id: 'references' },
  })

  content.push(reportGroup)
  return content
}

/**
 * Gets the appropriate icon for a model
 */
function getModelIcon(model: string): string {
  const iconMap: Record<string, string> = {
    'sdg': 'Target',
    'csrd': 'FileText',
    'eko': 'Lightbulb',
    'doughnut': 'TrendingUp',
    'drawdown': 'TrendingDown',
    'eu_taxonomy': 'BookOpen',
    'oecd_compact': 'Briefcase',
    'plant_based_treaty': 'Leaf',
    'sfdr': 'Shield',
  }

  return iconMap[model] || 'FileText'
}

/**
 * Gets a human-readable model name
 */
function getModelDisplayName(model: string): string {
  const nameMap: Record<string, string> = {
    'sdg': 'SDG',
    'csrd': 'CSRD',
    'eko': 'EKO',
    'doughnut': 'Doughnut Economics',
    'drawdown': 'Project Drawdown',
    'eu_taxonomy': 'EU Taxonomy',
    'oecd_compact': 'OECD Compact',
    'plant_based_treaty': 'Plant Based Treaty',
    'sfdr': 'SFDR',
  }

  return nameMap[model] || model.toUpperCase()
}

/**
 * Generates dynamic report templates based on available model sections
 * This function queries the database at runtime to get current model sections
 */
export async function generateDynamicTemplates(): Promise<DynamicTemplate[]> {
  try {
    console.log('Fetching model sections from database...')
    const sections = await fetchModelSections()
    console.log(`Found ${sections.length} model sections`)

    if (sections.length === 0) {
      console.warn('No model sections found in database')
      return []
    }

    const groupedByModel = groupSectionsByModel(sections)
    console.log('Models found:', Object.keys(groupedByModel))

    const templates: DynamicTemplate[] = []

    Object.entries(groupedByModel).forEach(([model, levelSections]) => {
      const displayName = getModelDisplayName(model)
      const sectionCount = Object.values(levelSections).flat().length

      console.log(`Creating template for ${model} with ${sectionCount} sections`)

      const template: DynamicTemplate = {
        id: `${model}-report`,
        name: `${displayName} Report`,
        description: `Comprehensive ${displayName} analysis report with ${sectionCount} specialized sections`,
        category: 'Reports',
        icon: getModelIcon(model),
        data: {
          type: 'doc',
          content: createModelReportStructure(model, levelSections),
        },
        tags: [model, 'report', 'analysis', 'automated', 'sustainability'],
      }

      templates.push(template)
    })

    console.log(`Generated ${templates.length} dynamic templates`)
    return templates
  } catch (error) {
    console.error('Error generating dynamic templates:', error)
    return []
  }
}
