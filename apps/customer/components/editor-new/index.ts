// Main Provider
export { EditorProvider } from './context/providers/EditorProvider'

// Individual Context Providers (for advanced usage)
export { ComponentProvider, useComponents } from './context/component/ComponentContext'
// export { DocumentProvider, useDocument } from './context/document/_deprecated_DocumentContext'
export { VersioningProvider, useVersioning } from './context/versioning/VersioningContext'

// Main Hooks
export { useEditor, useEditorComponent } from './hooks/useEditor'

// Types
export type {
  ReportComponent,
  ComponentUpdate,
  ComponentOperations,
  DocumentState,
  DocumentOperations,
  EntityChange,
  GroupInfo,
  PerformanceMetrics,
  ComponentStatus,
  GroupStatus,
  DocumentStatus,
  EntityChangeStatus
} from './types'

// Services (for advanced usage)
export {
  logger,
  debounce,
  throttle,
  TimeoutRegistry,
  TIMEOUT_DELAYS,
  COMPONENT_STATUS,
  GROUP_STATUS,
  DOCUMENT_STATUS,
  ENTITY_CHANGE_STATUS,
  FINAL_STATES,
  READY_STATES,
  componentStateMachine,
  reportService,
  versionService
} from './services'

export type {
  TimeoutManager,
  ComponentContext,
  StatusTransition,
  DocumentVersion
} from './services'