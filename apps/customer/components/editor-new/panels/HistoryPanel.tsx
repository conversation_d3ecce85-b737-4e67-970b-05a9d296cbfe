'use client'

import React, { useCallback, useEffect, useState } from 'react'
import { Editor } from '@tiptap/react'
import { Button } from '@ui/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@ui/components/ui/avatar'
import { Badge } from '@ui/components/ui/badge'
import { ScrollArea } from '@ui/components/ui/scroll-area'
import { Clock, Download, Eye, FileText, History, Loader2, RotateCcw, User } from 'lucide-react'
import { cn } from '@utils/lib/utils'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@ui/components/ui/tooltip'
import { createClient } from '@/app/supabase/client'
import { useToast } from '@ui/hooks/use-toast'
import { formatTimeAgo } from '@utils/date-utils'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@ui/components/ui/dialog'

interface HistoryVersion {
  id: string
  versionNumber: number
  title?: string | null
  content?: string | null
  data?: any
  author: {
    id: string
    name: string
    avatar?: string
    email?: string
  }
  createdAt: Date
  changeSummary?: string | null
  isAutoSave?: boolean | null
  isCurrent?: boolean
}

interface HistoryPanelProps {
  editor: Editor
  documentId: string
  currentUser?: {
    id: string
    name: string
    avatar?: string
    email?: string
  }
  onRestoreVersion?: (version: HistoryVersion) => void
  className?: string
}

export function HistoryPanel({
  editor,
  documentId,
  currentUser,
  onRestoreVersion,
  className
}: HistoryPanelProps) {
  const [versions, setVersions] = useState<HistoryVersion[]>([])
  const [selectedVersion, setSelectedVersion] = useState<string | null>(null)
  const [previewMode, setPreviewMode] = useState(false)
  const [loading, setLoading] = useState(false)
  const [restoring, setRestoring] = useState(false)
  const [restoreDialogOpen, setRestoreDialogOpen] = useState(false)
  const [versionToRestore, setVersionToRestore] = useState<HistoryVersion | null>(null)

  const { toast } = useToast()
  const supabase = createClient()

  // Load versions from Supabase
  const loadVersions = useCallback(async () => {
    if (!documentId) return

    setLoading(true)
    try {
      const { data: versions, error } = await supabase
        .from('doc_versions')
        .select(`
          id,
          version_number,
          title,
          content,
          data,
          created_at,
          change_summary,
          is_auto_save,
          created_by
        `)
        .eq('document_id', documentId)
        .order('version_number', { ascending: false })

      if (error) {
        throw error
      }

      // Get current user for author information
      const { data: { user } } = await supabase.auth.getUser()

      // Transform versions to match the expected format
      const transformedVersions = versions?.map((version, index) => ({
        id: version.id,
        versionNumber: version.version_number,
        title: version.title,
        content: version.content,
        data: version.data,
        createdAt: new Date(version.created_at || new Date()),
        changeSummary: version.change_summary,
        isAutoSave: version.is_auto_save,
        isCurrent: index === 0, // First version is the latest/current
        author: {
          id: version.created_by || 'unknown',
          name: version.created_by === user?.id && user
            ? (user.user_metadata?.name || user.email || 'You')
            : 'Unknown User',
          email: version.created_by === user?.id && user ? user.email : '<EMAIL>',
          avatar: version.created_by === user?.id && user ? user.user_metadata?.avatar_url : undefined
        }
      })) || []

      setVersions(transformedVersions)
    } catch (error) {
      console.error('Error loading versions:', error)
      toast({
        title: 'Error',
        description: 'Failed to load version history',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }, [documentId, toast, supabase])

  // Load versions on mount and set up real-time subscription
  useEffect(() => {
    loadVersions()

    // Set up real-time subscription for versions
    const channel = supabase
      .channel(`doc_versions:${documentId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'doc_versions',
          filter: `document_id=eq.${documentId}`
        },
        () => {
          // Reload versions when changes occur
          loadVersions()
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [documentId, loadVersions, supabase])

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }


  const getChangesSummary = (changes?: any) => {
    if (!changes) return null

    const total = changes.added + changes.removed + changes.modified
    if (total === 0) return 'No changes'

    const parts = []
    if (changes.added > 0) parts.push(`+${changes.added}`)
    if (changes.removed > 0) parts.push(`-${changes.removed}`)
    if (changes.modified > 0) parts.push(`~${changes.modified}`)

    return parts.join(' ')
  }

  const handlePreview = (version: HistoryVersion) => {
    setSelectedVersion(version.id)
    setPreviewMode(true)

    // Load the version content into the editor for preview
    if (version.data) {
      editor.commands.setContent(version.data, true)
    } else if (version.content) {
      editor.commands.setContent(version.content, true)
    }
  }

  const handleRestoreClick = (version: HistoryVersion) => {
    setVersionToRestore(version)
    setRestoreDialogOpen(true)
  }

  const handleRestore = async () => {
    if (!versionToRestore || restoring) return

    setRestoring(true)
    const version = versionToRestore
    try {
      // Call the parent's restore handler if provided
      if (onRestoreVersion) {
        onRestoreVersion(version)
      } else {
        // Default restore behavior - set content and create new version
        if (version.data) {
          editor.commands.setContent(version.data, true)
        } else if (version.content) {
          editor.commands.setContent(version.content, true)
        }

        // Get the next version number
        const { data: latestVersion } = await supabase
          .from('doc_versions')
          .select('version_number')
          .eq('document_id', documentId)
          .order('version_number', { ascending: false })
          .limit(1)
          .single()

        const nextVersionNumber = (latestVersion?.version_number || 0) + 1

        // Get current user
        const { data: { user } } = await supabase.auth.getUser()

        // Create a new version marking this as a restore
        const { error: insertError } = await supabase
          .from('doc_versions')
          .insert({
            document_id: documentId,
            version_number: nextVersionNumber,
            title: version.title,
            content: version.content,
            data: version.data,
            created_by: user?.id,
            change_summary: `Restored from version ${version.versionNumber}`,
            is_auto_save: false
          })

        if (insertError) {
          throw insertError
        }

        toast({
          title: 'Success',
          description: `Restored to version ${version.versionNumber}`,
        })
      }

      setPreviewMode(false)
      setSelectedVersion(null)
      setRestoreDialogOpen(false)
      setVersionToRestore(null)
    } catch (error) {
      console.error('Error restoring version:', error)
      toast({
        title: 'Error',
        description: 'Failed to restore version',
        variant: 'destructive'
      })
    } finally {
      setRestoring(false)
    }
  }

  const exitPreview = () => {
    setPreviewMode(false)
    setSelectedVersion(null)

    // Restore current content (latest version)
    const currentVersion = versions.find(v => v.isCurrent)
    if (currentVersion) {
      if (currentVersion.data) {
        editor.commands.setContent(currentVersion.data, true)
      } else if (currentVersion.content) {
        editor.commands.setContent(currentVersion.content, true)
      }
    }
  }

  const handleExport = async (version: HistoryVersion) => {
    try {
      // Create a temporary editor content for export
      const tempContent = version.data || version.content || ''

      // For now, export as HTML
      const htmlContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${version.title || `Version ${version.versionNumber}`}</title>
    <style>
        .export-content { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; max-width: 800px; margin: 0 auto; padding: 2rem; }
        .export-content h1, .export-content h2, .export-content h3, .export-content h4, .export-content h5, .export-content h6 { margin-top: 2rem; margin-bottom: 1rem; }
        .export-content p { margin-bottom: 1rem; }
    </style>
</head>
<body>
    <div class="export-content">
        <h1>${version.title || `Version ${version.versionNumber}`}</h1>
        <p><small>Created: ${version.createdAt.toLocaleString()}</small></p>
        ${typeof tempContent === 'string' ? tempContent : JSON.stringify(tempContent)}
    </div>
</body>
</html>`

      const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `version-${version.versionNumber}-${version.createdAt.toISOString().slice(0, 10)}.html`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      toast({
        title: 'Success',
        description: 'Version exported successfully'
      })
    } catch (error) {
      console.error('Error exporting version:', error)
      toast({
        title: 'Error',
        description: 'Failed to export version',
        variant: 'destructive'
      })
    }
  }

  return (
    <div
      className={cn('w-80 bg-background border-l flex flex-col h-full', className)}
      data-testid="history-panel"
    >
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center gap-2">
          <History className="w-4 h-4" />
          <h3 className="font-semibold">Version History</h3>
          <Badge variant="secondary" className="ml-auto">
            {versions.length}
          </Badge>
        </div>
        {previewMode && (
          <div className="mt-2 p-2 bg-blue-50 dark:bg-blue-950 rounded-md">
            <div className="flex items-center justify-between">
              <span className="text-xs text-blue-700 dark:text-blue-300">
                Previewing version
              </span>
              <Button size="sm" variant="outline" onClick={exitPreview}>
                Exit Preview
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Versions List */}
      <ScrollArea className="flex-1 min-h-0">
        <div className="p-4 space-y-2">
          {loading ? (
            <div className="text-center text-muted-foreground py-8">
              <Loader2 className="w-8 h-8 mx-auto mb-2 animate-spin" />
              <p className="text-sm">Loading version history...</p>
            </div>
          ) : versions.length === 0 ? (
            <div className="text-center text-muted-foreground py-8">
              <History className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No version history</p>
              <p className="text-xs">Changes will appear here as you edit</p>
            </div>
          ) : (
            versions.map((version, index) => (
              <div
                key={version.id}
                className={cn(
                  'p-3 rounded-lg border transition-colors cursor-pointer',
                  selectedVersion === version.id
                    ? 'border-primary bg-primary/5'
                    : 'border-border hover:bg-muted/50',
                  version.isCurrent && 'ring-2 ring-green-500/20 bg-green-50/50 dark:bg-green-950/20'
                )}
                onClick={() => handlePreview(version)}
                data-testid="version-item"
              >
                <div className="flex items-start gap-3">
                  <Avatar className="w-6 h-6">
                    <AvatarImage src={version.author.avatar} alt={version.author.name} />
                    <AvatarFallback className="text-xs">
                      {getInitials(version.author.name)}
                    </AvatarFallback>
                  </Avatar>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-sm font-medium">
                        Version {version.versionNumber}
                      </span>
                      {version.isCurrent && (
                        <Badge variant="secondary" className="text-xs">
                          Current
                        </Badge>
                      )}
                      {version.isAutoSave && (
                        <Badge variant="outline" className="text-xs">
                          Auto
                        </Badge>
                      )}
                    </div>

                    <div className="flex items-center gap-2 text-xs text-muted-foreground mb-1">
                      <User className="w-3 h-3" />
                      <span>{version.author.name}</span>
                      <Clock className="w-3 h-3 ml-1" />
                      <span data-testid="version-timestamp">{formatTimeAgo(version.createdAt)}</span>
                    </div>

                    {version.changeSummary && (
                      <p className="text-xs text-foreground mb-2 line-clamp-2">
                        {version.changeSummary}
                      </p>
                    )}

                    {version.title && version.title !== `Version ${version.versionNumber}` && (
                      <div className="flex items-center gap-2 text-xs">
                        <FileText className="w-3 h-3" />
                        <span className="truncate">
                          {version.title}
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Action buttons */}
                <div className="flex items-center gap-1 mt-2">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={(e) => {
                            e.stopPropagation()
                            handlePreview(version)
                          }}
                          className="h-6 px-2"
                        >
                          <Eye className="w-3 h-3" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>Preview this version</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>

                  {!version.isCurrent && (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleRestoreClick(version)
                            }}
                            className="h-6 px-2"
                            data-testid="restore-version"
                          >
                            <RotateCcw className="w-3 h-3" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Restore this version</TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}

                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleExport(version)
                          }}
                          className="h-6 px-2"
                        >
                          <Download className="w-3 h-3" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>Export this version</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </div>
            ))
          )}
        </div>
      </ScrollArea>

      {/* Footer */}
      <div className="p-4 border-t">
        <div className="text-xs text-muted-foreground text-center">
          <FileText className="w-3 h-3 inline mr-1" />
          Auto-saved versions are created as you edit
        </div>
      </div>

      {/* Restore Confirmation Dialog */}
      <Dialog open={restoreDialogOpen} onOpenChange={setRestoreDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Restore Version</DialogTitle>
            <DialogDescription>
              Are you sure you want to restore version {versionToRestore?.versionNumber}? 
              This will replace the current document content with the selected version.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setRestoreDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleRestore}
              data-testid="confirm-restore"
            >
              Restore
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
