We currently have [streaming-hierarchical-report.tsx](../report/streaming/streaming-hierarchical-report.tsx) which uses EkoMarkdown component which renders static reports.

I'd like to allow customers to edit reports.

# Intro

The new report editor component should go in: apps/customer/components/editor

# Tip Tap

The report editor will be based on Tip Tap https://tiptap.dev/docs it should not try to reinvent the wheel. Except for user management and document storage please use their provided components.

# Report Editor

We need a EkoDocumentEditor component in: apps/customer/components/editor - this will be based on Tip Tap using the apps/customer/components/editor/supabase-provider.ts supabase provider.

Details on providers here: https://tiptap.dev/docs/collaboration/provider/integration
Details on auth here: https://tiptap.dev/docs/collaboration/getting-started/authenticate


# Report Management

The existing reports route is app/customer/dashboard/reports and that should be used for report generation but we now need a button on that page that says Edit.

The edit button should save the report to the database, as TipTap JSON making use of all the plugins (table, charts etc.), with an additional custom citations plugin with the citations data in it for citation resolution and reference listing.

The id from the save should then be used to redirect to our new route app/customer/documents/[id]

This should then provide an editor page with:

a) All the Notion style features available.
b) Collaboration support

# Collaboration Support

Please provide:

a) Comments - https://tiptap.dev/docs/comments/getting-started/overview
b) History - https://tiptap.dev/docs/collaboration/documents/history
c) Conversion - https://tiptap.dev/docs/conversion/getting-started/overview

I have an active trial for pro features, so they can be used as needed, our main difference is that rather than storing the docs in their doc repo I want to store them in our Supabase database, and for user and presence management I want to use Supabase. Otherwise I'd like to use their pro features, especially support for Agentic editing later on.

I'm not sure how best to support these features while using a supabase provider so I'll need you to read up online and have a good think first.

# The Plan:

[✅] Start with `npx @tiptap/cli add simple-editor` (see https://tiptap.dev/docs/ui-components/templates/simple-editor)
[✅] Update it to have all the features we need and call this EkoDocumentEditor
[✅] Create the new editor route at app/customer/documents/[id]/page.tsx
[✅] Integrate TipTap Cloud collaboration (using TipTap Cloud instead of Supabase provider)
[✅] Add in collaboration features (real-time editing, cursors, awareness)
[✅] Update app/customer/dashboard/reports to have an 'Edit Report' button which saves the report and then redirects to app/customer/documents/[id]
[✅] Make sure that the markdown extensions added in eko-markdown.tsx are now extensions in our new editor, table, charts etc.
[✅] Make sure citations work properly using a custom citations plugin.
[✅] Database table `doc_documents` created in Supabase
[✅] TipTap Cloud authentication API route `/api/tiptap/auth`

## Remaining Tasks:

[✅] Create EditorToolbar component with comprehensive formatting options
[✅] Create CollaborationToolbar component for collaboration features
[✅] Create CommentsPanel component for TipTap Pro Comments
[✅] Create HistoryPanel component for TipTap Pro History
[✅] Create SlashCommands extension for Notion-like commands
[✅] Enhance the basic toolbar in EkoDocumentEditor to use the new toolbar components
[✅] Install tippy.js dependency for SlashCommands
[✅] Fix TypeScript errors and ensure type safety

## Future Enhancements:

[ ] Add documentation conversion features (PDF, Word, Markdown export)
[ ] Add comments, create a database table, create comment extension so that comments can be added at any point in the doc. Use Supabase realtime to see comments made instantly.
[ ] Store history in supabase in a seperate table so that users can recall previous versions and switch to them if neeed.
[ ] Add proper error handling and loading states for collaboration features
[ ] Allow users to share a document within an organisation.
[ ] Add a list of documents on the left hand side in a seperate component that lists all your documents and all the shared documents within your organisation.
[ ] Add real-time user presence indicators again using supabase realtime
[ ] Add document settings and metadata management
[ ] Add link extension for proper link functionality
[ ] Add more advanced slash commands (tables, charts, etc.)
[ ] Add document templates and presets
