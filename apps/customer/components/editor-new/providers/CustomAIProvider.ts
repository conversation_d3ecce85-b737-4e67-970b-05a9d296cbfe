import { EventEmitter } from 'events'

export interface AIMessage {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: Date
  toolCalls?: any[]
}

export interface AIGenerationOptions {
  prompt: string
  context?: string
  selection?: string
  command?: string
  stream?: boolean
  model?: string
  temperature?: number
  maxTokens?: number
}

export interface SerializedSchema {
  nodes: Record<string, any>
  marks: Record<string, any>
}

export interface AIChatOptions {
  messages: AIMessage[]
  documentContent?: string
  documentId?: string
  schema?: SerializedSchema | null
  stream?: boolean
  model?: string
  temperature?: number
  maxTokens?: number
}

export interface AIProviderState {
  isGenerating: boolean
  isConnected: boolean
  messages: AIMessage[]
  error: string | null
  lastResponse: string | null
}

export class CustomAIProvider extends EventEmitter {
  private state: AIProviderState = {
    isGenerating: false,
    isConnected: true,
    messages: [],
    error: null,
    lastResponse: null,
  }

  private apiKey: string
  private baseUrl: string

  constructor(options: { apiKey: string; baseUrl?: string }) {
    super()
    this.apiKey = options.apiKey
    this.baseUrl = options.baseUrl || '/api/ai'
  }

  getState(): AIProviderState {
    return { ...this.state }
  }

  private setState(updates: Partial<AIProviderState>) {
    const previousState = { ...this.state }
    this.state = { ...this.state, ...updates }
    this.emit('stateChange', this.state, previousState)
  }

  private async makeRequest(endpoint: string, data: any): Promise<Response> {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
      },
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      const error = await response.json().catch(() => ({ error: 'Unknown error' }))
      throw new Error(error.error || `HTTP ${response.status}`)
    }

    return response
  }

  async generateText(options: AIGenerationOptions): Promise<string> {
    this.setState({ isGenerating: true, error: null })

    try {
      const response = await this.makeRequest('/generate', {
        ...options,
        stream: false,
      })

      const result = await response.json()
      const generatedText = result.text || ''

      this.setState({ 
        isGenerating: false, 
        lastResponse: generatedText 
      })

      this.emit('textGenerated', generatedText, options)
      return generatedText
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Generation failed'
      this.setState({ 
        isGenerating: false, 
        error: errorMessage 
      })
      this.emit('error', error)
      throw error
    }
  }

  async *streamText(options: AIGenerationOptions): AsyncGenerator<string, void, unknown> {
    this.setState({ isGenerating: true, error: null })

    try {
      const response = await this.makeRequest('/generate', {
        ...options,
        stream: true,
      })

      if (!response.body) {
        throw new Error('No response body')
      }

      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let buffer = ''

      try {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          buffer += decoder.decode(value, { stream: true })
          const lines = buffer.split('\n')
          buffer = lines.pop() || ''

          for (const line of lines) {
            if (line.trim()) {
              yield line
              this.emit('textChunk', line)
            }
          }
        }

        // Process any remaining buffer
        if (buffer.trim()) {
          yield buffer
          this.emit('textChunk', buffer)
        }
      } finally {
        reader.releaseLock()
      }

      this.setState({ isGenerating: false })
      this.emit('streamComplete')
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Streaming failed'
      this.setState({ 
        isGenerating: false, 
        error: errorMessage 
      })
      this.emit('error', error)
      throw error
    }
  }

  async sendChatMessage(content: string, documentContent?: string, documentId?: string, schema?: SerializedSchema | null): Promise<AIMessage> {
    const userMessage: AIMessage = {
      id: `user-${Date.now()}`,
      role: 'user',
      content,
      timestamp: new Date(),
    }

    const updatedMessages = [...this.state.messages, userMessage]
    this.setState({ 
      messages: updatedMessages, 
      isGenerating: true, 
      error: null 
    })

    try {
      const response = await this.makeRequest('/chat', {
        messages: updatedMessages.map(msg => ({
          role: msg.role,
          content: msg.content,
          ...(msg.toolCalls && { tool_calls: msg.toolCalls }),
        })),
        documentContent,
        documentId,
        schema,
        stream: false,
      })

      const result = await response.json()
      const assistantMessage: AIMessage = {
        id: `assistant-${Date.now()}`,
        role: 'assistant',
        content: result.message.content,
        timestamp: new Date(),
        toolCalls: result.message.tool_calls,
      }

      const finalMessages = [...updatedMessages, assistantMessage]
      this.setState({ 
        messages: finalMessages, 
        isGenerating: false,
        lastResponse: assistantMessage.content,
      })

      this.emit('messageReceived', assistantMessage)
      return assistantMessage
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Chat failed'
      this.setState({ 
        isGenerating: false, 
        error: errorMessage 
      })
      this.emit('error', error)
      throw error
    }
  }

  async *streamChatMessage(content: string, documentContent?: string, documentId?: string, schema?: SerializedSchema | null): AsyncGenerator<string, AIMessage, unknown> {
    const userMessage: AIMessage = {
      id: `user-${Date.now()}`,
      role: 'user',
      content,
      timestamp: new Date(),
    }

    const updatedMessages = [...this.state.messages, userMessage]
    this.setState({
      messages: updatedMessages,
      isGenerating: true,
      error: null
    })

    let accumulatedContent = ''
    let finalAIResponse: any = null
    let isEditResponse = false
    let editDescription = ''

    try {
      const response = await this.makeRequest('/chat', {
        messages: updatedMessages.map(msg => ({
          role: msg.role,
          content: msg.content,
          ...(msg.toolCalls && { tool_calls: msg.toolCalls }),
        })),
        documentContent,
        documentId,
        schema,
        stream: true,
      })

      if (!response.body) {
        throw new Error('No response body')
      }

      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let buffer = ''

      try {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          buffer += decoder.decode(value, { stream: true })
          const lines = buffer.split('\n')
          buffer = lines.pop() || ''

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6).trim()

              if (data === '[DONE]') {
                // Stream is complete
                break
              }

              try {
                const parsed = JSON.parse(data)

                if (parsed.type === 'stream') {

                  // Handle streaming chunks - accumulate content
                  accumulatedContent += parsed.delta

                  // Try to detect if this is an edit response by parsing accumulated content
                  try {
                    const trimmedContent = accumulatedContent.trim()
                    if (trimmedContent.startsWith('{') && trimmedContent.includes('"type"')) {
                      // Try to parse as JSON to detect edit responses
                      const testParse = JSON.parse(trimmedContent)
                      if (testParse.type === 'edit') {
                        isEditResponse = true
                        editDescription = testParse.description || 'Making document edits...'
                        // Yield the description instead of raw JSON
                        yield `✏️ ${editDescription}`
                      } else {
                        // Regular chat response
                        yield accumulatedContent
                      }
                    } else {
                      // Not JSON yet, yield as normal
                      yield accumulatedContent
                    }
                  } catch {
                    // Not valid JSON yet, continue accumulating
                    if (!isEditResponse) {
                      yield accumulatedContent
                    } else {
                      // Already detected as edit, keep showing description
                      yield `✏️ ${editDescription}`
                    }

                  }

                  // Emit just the delta for UI components that want incremental updates
                  this.emit('chatChunk', parsed.delta)
                } else if (parsed.type === 'final') {
                  // Handle final response
                  finalAIResponse = parsed.aiResponse
                } else if (parsed.type === 'error') {
                  throw new Error(parsed.error)
                }
              } catch (parseError) {
                console.warn('Failed to parse SSE data:', data, parseError)
              }
            }
          }
        }

        // Use final response or fallback to accumulated content
        const parsedResponse = finalAIResponse || {
          type: 'chat',
          message: {
            role: 'assistant',
            content: accumulatedContent,
            timestamp: new Date().toISOString()
          }
        }

        // Get the final content to display
        let finalContent = parsedResponse.message?.content || accumulatedContent


        // For edit responses, show the description instead of raw JSON
        if (parsedResponse.type === 'edit') {
          finalContent = `✏️ ${parsedResponse.description || 'Document edited successfully'}`
        }

        // Create assistant message
        const assistantMessage: AIMessage = {
          id: `assistant-${Date.now()}`,
          role: 'assistant',
          content: finalContent,
          timestamp: new Date(),
        }

        const finalMessages = [...updatedMessages, assistantMessage]
        this.setState({
          messages: finalMessages,
          isGenerating: false,
          lastResponse: finalContent,
        })

        // If it's an edit response, emit the edit event
        if (parsedResponse.type === 'edit' && parsedResponse.patch) {
          this.emit('documentEdited', {
            patch: parsedResponse.patch,
            description: parsedResponse.description
          })
        }

        this.emit('messageReceived', assistantMessage)
        return assistantMessage
      } finally {
        reader.releaseLock()
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Chat streaming failed'
      this.setState({
        isGenerating: false,
        error: errorMessage
      })
      this.emit('error', error)
      throw error
    }
  }

  clearMessages() {
    this.setState({ messages: [], error: null })
    this.emit('messagesCleared')
  }

  clearError() {
    this.setState({ error: null })
  }
}
