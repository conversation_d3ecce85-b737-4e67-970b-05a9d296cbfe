import { ReportComponent, GroupInfo } from '../../types'
import { logger, debounce, TIMEOUT_DELAYS } from '../index'
import type { ComponentStatus, GroupStatus } from '../index'

interface GroupManagerOptions {
  debounceDelay?: number
  onGroupStatusChange?: (groupId: string, status: GroupStatus) => void
}

export class GroupStatusManager {
  private groupCache = new Map<string, GroupInfo>()
  private updateQueue = new Set<string>()
  private isProcessing = false
  private options: Required<GroupManagerOptions>
  
  // Debounced update function
  private debouncedProcessQueue: () => void

  constructor(options: GroupManagerOptions = {}) {
    this.options = {
      debounceDelay: options.debounceDelay ?? TIMEOUT_DELAYS.DEBOUNCE,
      onGroupStatusChange: options.onGroupStatusChange ?? (() => {})
    }
    
    this.debouncedProcessQueue = debounce(
      () => this.processUpdateQueue(),
      this.options.debounceDelay
    )
  }

  /**
   * Queue a group for status update
   */
  queueGroupUpdate(groupId: string): void {
    this.updateQueue.add(groupId)
    this.debouncedProcessQueue()
  }

  /**
   * Queue multiple groups for status update
   */
  queueGroupUpdates(groupIds: string[]): void {
    groupIds.forEach(id => this.updateQueue.add(id))
    this.debouncedProcessQueue()
  }

  /**
   * Process all queued group updates
   */
  private async processUpdateQueue(): Promise<void> {
    if (this.isProcessing || this.updateQueue.size === 0) {
      return
    }

    this.isProcessing = true
    const groupsToUpdate = Array.from(this.updateQueue)
    this.updateQueue.clear()

    logger.debug('GroupStatusManager', 'processUpdateQueue', 
      `Processing ${groupsToUpdate.length} group updates`)

    // Process updates in batches to avoid blocking
    const BATCH_SIZE = 10
    for (let i = 0; i < groupsToUpdate.length; i += BATCH_SIZE) {
      const batch = groupsToUpdate.slice(i, i + BATCH_SIZE)
      
      // Use Promise.all for parallel processing within batch
      await Promise.all(
        batch.map(groupId => this.updateGroupStatusInternal(groupId))
      )
      
      // Small delay between batches to avoid blocking UI
      if (i + BATCH_SIZE < groupsToUpdate.length) {
        await new Promise(resolve => setTimeout(resolve, 0))
      }
    }

    this.isProcessing = false

    // Process any new updates that came in during processing
    if (this.updateQueue.size > 0) {
      this.debouncedProcessQueue()
    }
  }

  /**
   * Calculate group status based on children
   */
  calculateGroupStatus(
    groupId: string,
    components: Map<string, ReportComponent>,
    componentHierarchy: Map<string, string[]>
  ): GroupInfo {
    // Check cache first
    const cached = this.groupCache.get(groupId)
    if (cached && this.isCacheValid(cached, components)) {
      return cached
    }

    const children = componentHierarchy.get(groupId) || []
    const descendants = this.getAllDescendants(groupId, componentHierarchy)
    
    let loadedCount = 0
    let errorCount = 0
    let totalCount = 0

    // Calculate status from all descendants
    descendants.forEach(descendantId => {
      const component = components.get(descendantId)
      if (component && component.type !== 'report-group') {
        totalCount++
        
        if (['loaded', 'preserved', 'locked'].includes(component.status)) {
          loadedCount++
        } else if (component.status === 'error') {
          errorCount++
        }
      }
    })

    // Determine group status
    let status: GroupStatus = 'not_loaded'
    if (totalCount === 0) {
      status = 'loaded' // Empty groups are considered loaded
    } else if (loadedCount < totalCount && errorCount === 0) {
      // If any components are not yet loaded (loading/idle), group should be loading
      status = 'loading'
    } else if (errorCount > 0 && loadedCount === totalCount - errorCount) {
      // Only show error if all non-errored components are loaded
      status = 'error'
    } else if (loadedCount === totalCount) {
      status = 'loaded'
    } else {
      // Default to loading for any other state combination
      status = 'loading'
    }

    const groupInfo: GroupInfo = {
      id: groupId,
      status,
      totalComponents: totalCount,
      loadedComponents: loadedCount,
      children,
      descendants
    }

    // Update cache
    this.groupCache.set(groupId, groupInfo)
    
    return groupInfo
  }

  /**
   * Get all descendants of a group (recursive)
   */
  private getAllDescendants(
    groupId: string,
    componentHierarchy: Map<string, string[]>,
    visited = new Set<string>()
  ): string[] {
    if (visited.has(groupId)) {
      return [] // Prevent infinite recursion
    }
    
    visited.add(groupId)
    const descendants: string[] = []
    const children = componentHierarchy.get(groupId) || []
    
    for (const childId of children) {
      descendants.push(childId)
      descendants.push(...this.getAllDescendants(childId, componentHierarchy, visited))
    }
    
    return descendants
  }

  /**
   * Check if cached group info is still valid
   */
  private isCacheValid(
    cached: GroupInfo,
    components: Map<string, ReportComponent>
  ): boolean {
    // Simple validation - check if any descendant status has changed
    // In a real implementation, you might want to track component versions
    return cached.descendants.every(id => {
      const component = components.get(id)
      return component !== undefined
    })
  }

  /**
   * Internal method to update a single group's status
   */
  private async updateGroupStatusInternal(groupId: string): Promise<void> {
    // This would need access to components and hierarchy
    // In practice, this would be called with proper context
    logger.debug('GroupStatusManager', 'updateGroupStatusInternal', 
      `Updating group status for ${groupId}`)
  }

  /**
   * Clear cache for a specific group
   */
  invalidateGroup(groupId: string): void {
    this.groupCache.delete(groupId)
    this.queueGroupUpdate(groupId)
  }

  /**
   * Clear entire cache
   */
  clearCache(): void {
    this.groupCache.clear()
    this.updateQueue.clear()
  }

  /**
   * Get cached group info
   */
  getCachedGroupInfo(groupId: string): GroupInfo | undefined {
    return this.groupCache.get(groupId)
  }

  /**
   * Get all groups that need their status updated when a component changes
   */
  getAffectedGroups(
    componentId: string,
    components: Map<string, ReportComponent>,
    componentHierarchy: Map<string, string[]>
  ): string[] {
    const affectedGroups: string[] = []
    const component = components.get(componentId)
    
    if (!component) return affectedGroups
    
    // Add direct parent
    if (component.parentId) {
      affectedGroups.push(component.parentId)
      
      // Traverse up the hierarchy
      let currentId = component.parentId
      while (currentId) {
        const parent = components.get(currentId)
        if (parent?.parentId && parent.type === 'report-group') {
          affectedGroups.push(parent.parentId)
          currentId = parent.parentId
        } else {
          break
        }
      }
    }
    
    return [...new Set(affectedGroups)] // Remove duplicates
  }

  /**
   * Batch update multiple groups efficiently
   */
  async batchUpdateGroups(
    groupIds: string[],
    components: Map<string, ReportComponent>,
    componentHierarchy: Map<string, string[]>
  ): Promise<Map<string, GroupInfo>> {
    const results = new Map<string, GroupInfo>()
    
    // Sort groups by depth (deepest first) to maximize cache hits
    const sortedGroups = this.sortGroupsByDepth(groupIds, components)
    
    for (const groupId of sortedGroups) {
      const groupInfo = this.calculateGroupStatus(groupId, components, componentHierarchy)
      results.set(groupId, groupInfo)
      
      // Notify of status change
      const previousStatus = this.groupCache.get(groupId)?.status
      if (previousStatus !== groupInfo.status) {
        this.options.onGroupStatusChange(groupId, groupInfo.status)
      }
    }
    
    return results
  }

  /**
   * Sort groups by their depth in the hierarchy (deepest first)
   */
  private sortGroupsByDepth(
    groupIds: string[],
    components: Map<string, ReportComponent>
  ): string[] {
    const depths = new Map<string, number>()
    
    const getDepth = (id: string): number => {
      if (depths.has(id)) return depths.get(id)!
      
      const component = components.get(id)
      if (!component || !component.parentId) {
        depths.set(id, 0)
        return 0
      }
      
      const parentDepth = getDepth(component.parentId)
      const depth = parentDepth + 1
      depths.set(id, depth)
      return depth
    }
    
    groupIds.forEach(id => getDepth(id))
    
    return groupIds.sort((a, b) => {
      const depthA = depths.get(a) || 0
      const depthB = depths.get(b) || 0
      return depthB - depthA // Deepest first
    })
  }
}

export const groupStatusManager = new GroupStatusManager()