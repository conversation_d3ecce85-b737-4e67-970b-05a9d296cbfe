type LogLevel = 'debug' | 'info' | 'warn' | 'error'

interface LogConfig {
  level: LogLevel
  prefix: string
  enabled: boolean
}

class EditorLogger {
  private config: LogConfig = {
    level: 'info',
    prefix: 'Editor',
    enabled: process.env.NODE_ENV === 'development'
  }

  private shouldLog(level: LogLevel): boolean {
    if (!this.config.enabled) return false
    
    const levels: Record<LogLevel, number> = {
      debug: 0,
      info: 1,
      warn: 2,
      error: 3
    }
    
    return levels[level] >= levels[this.config.level]
  }

  private formatMessage(component: string, method: string, message: string): string {
    return `${this.config.prefix}.${component}.${method}: ${message}`
  }

  debug(component: string, method: string, message: string): void {
    if (this.shouldLog('debug')) {
      console.log(this.formatMessage(component, method, message))
    }
  }

  info(component: string, method: string, message: string): void {
    if (this.shouldLog('info')) {
      console.log(this.formatMessage(component, method, message))
    }
  }

  warn(component: string, method: string, message: string): void {
    if (this.shouldLog('warn')) {
      console.warn(this.formatMessage(component, method, message))
    }
  }

  error(component: string, method: string, message: string, error?: Error): void {
    if (this.shouldLog('error')) {
      console.error(this.formatMessage(component, method, message), error)
    }
  }

  configure(config: Partial<LogConfig>): void {
    this.config = { ...this.config, ...config }
  }
}

export const logger = new EditorLogger()