import React from 'react'
import { logger } from './logger'

type CleanupFunction = () => void | Promise<void>

interface ResourceTracker {
  id: string
  type: 'timeout' | 'interval' | 'listener' | 'subscription' | 'ref'
  cleanup: CleanupFunction
  created: Date
}

/**
 * Memory management utility for tracking and cleaning up resources
 */
export class MemoryManager {
  private resources = new Map<string, ResourceTracker>()
  private cleanupCallbacks = new Set<CleanupFunction>()
  private isCleaningUp = false

  /**
   * Register a resource for tracking and cleanup
   */
  register(
    id: string,
    type: ResourceTracker['type'],
    cleanup: CleanupFunction
  ): void {
    if (this.resources.has(id)) {
      logger.warn('MemoryManager', 'register', 
        `Resource ${id} already registered, cleaning up old resource`)
      this.cleanup(id)
    }

    this.resources.set(id, {
      id,
      type,
      cleanup,
      created: new Date()
    })

    logger.debug('MemoryManager', 'register', 
      `Registered ${type} resource: ${id}`)
  }

  /**
   * Register a timeout for automatic cleanup
   */
  registerTimeout(
    id: string,
    timeoutId: NodeJS.Timeout
  ): void {
    this.register(id, 'timeout', () => clearTimeout(timeoutId))
  }

  /**
   * Register an interval for automatic cleanup
   */
  registerInterval(
    id: string,
    intervalId: NodeJS.Timeout
  ): void {
    this.register(id, 'interval', () => clearInterval(intervalId))
  }

  /**
   * Register an event listener for automatic cleanup
   */
  registerListener(
    id: string,
    target: EventTarget,
    event: string,
    handler: EventListener,
    options?: boolean | AddEventListenerOptions
  ): void {
    this.register(id, 'listener', () => {
      target.removeEventListener(event, handler, options)
    })
  }

  /**
   * Register a subscription for automatic cleanup
   */
  registerSubscription(
    id: string,
    unsubscribe: () => void
  ): void {
    this.register(id, 'subscription', unsubscribe)
  }

  /**
   * Register a ref that needs cleanup
   */
  registerRef(
    id: string,
    ref: { current: any },
    cleanup?: () => void
  ): void {
    this.register(id, 'ref', () => {
      if (cleanup) {
        cleanup()
      }
      ref.current = null
    })
  }

  /**
   * Clean up a specific resource
   */
  async cleanup(id: string): Promise<void> {
    const resource = this.resources.get(id)
    if (!resource) {
      return
    }

    try {
      await resource.cleanup()
      this.resources.delete(id)
      logger.debug('MemoryManager', 'cleanup', 
        `Cleaned up ${resource.type} resource: ${id}`)
    } catch (error) {
      logger.error('MemoryManager', 'cleanup', 
        `Error cleaning up ${resource.type} resource: ${id}`, error as Error)
    }
  }

  /**
   * Clean up all resources of a specific type
   */
  async cleanupByType(type: ResourceTracker['type']): Promise<void> {
    const resourcesOfType = Array.from(this.resources.entries())
      .filter(([_, resource]) => resource.type === type)

    logger.info('MemoryManager', 'cleanupByType', 
      `Cleaning up ${resourcesOfType.length} ${type} resources`)

    await Promise.all(
      resourcesOfType.map(([id]) => this.cleanup(id))
    )
  }

  /**
   * Clean up all resources
   */
  async cleanupAll(): Promise<void> {
    if (this.isCleaningUp) {
      logger.warn('MemoryManager', 'cleanupAll', 
        'Cleanup already in progress')
      return
    }

    this.isCleaningUp = true
    logger.info('MemoryManager', 'cleanupAll', 
      `Cleaning up ${this.resources.size} resources`)

    // Clean up in reverse order of registration (LIFO)
    const resourceIds = Array.from(this.resources.keys()).reverse()
    
    for (const id of resourceIds) {
      await this.cleanup(id)
    }

    // Execute additional cleanup callbacks
    for (const callback of this.cleanupCallbacks) {
      try {
        await callback()
      } catch (error) {
        logger.error('MemoryManager', 'cleanupAll', 
          'Error executing cleanup callback', error as Error)
      }
    }

    this.cleanupCallbacks.clear()
    this.isCleaningUp = false
  }

  /**
   * Add a cleanup callback to be executed during cleanupAll
   */
  addCleanupCallback(callback: CleanupFunction): void {
    this.cleanupCallbacks.add(callback)
  }

  /**
   * Remove a cleanup callback
   */
  removeCleanupCallback(callback: CleanupFunction): void {
    this.cleanupCallbacks.delete(callback)
  }

  /**
   * Get resource statistics
   */
  getStats(): {
    total: number
    byType: Record<ResourceTracker['type'], number>
    oldestResource: ResourceTracker | null
  } {
    const byType: Record<ResourceTracker['type'], number> = {
      timeout: 0,
      interval: 0,
      listener: 0,
      subscription: 0,
      ref: 0
    }

    let oldestResource: ResourceTracker | null = null
    let oldestDate = new Date()

    this.resources.forEach(resource => {
      byType[resource.type]++
      
      if (resource.created < oldestDate) {
        oldestDate = resource.created
        oldestResource = resource
      }
    })

    return {
      total: this.resources.size,
      byType,
      oldestResource
    }
  }

  /**
   * Check for potential memory leaks
   */
  checkForLeaks(maxAge: number = 60000): ResourceTracker[] {
    const now = new Date()
    const leaks: ResourceTracker[] = []

    this.resources.forEach(resource => {
      const age = now.getTime() - resource.created.getTime()
      if (age > maxAge) {
        leaks.push(resource)
      }
    })

    if (leaks.length > 0) {
      logger.warn('MemoryManager', 'checkForLeaks', 
        `Found ${leaks.length} potential memory leaks`)
    }

    return leaks
  }

  /**
   * Create a scoped memory manager for a specific component
   */
  createScope(scopeId: string): ScopedMemoryManager {
    return new ScopedMemoryManager(this, scopeId)
  }
}

/**
 * Scoped memory manager for component-specific cleanup
 */
export class ScopedMemoryManager {
  private scopedIds = new Set<string>()

  constructor(
    private parent: MemoryManager,
    private scopeId: string
  ) {}

  private makeScopedId(id: string): string {
    return `${this.scopeId}:${id}`
  }

  register(
    id: string,
    type: ResourceTracker['type'],
    cleanup: CleanupFunction
  ): void {
    const scopedId = this.makeScopedId(id)
    this.scopedIds.add(scopedId)
    this.parent.register(scopedId, type, cleanup)
  }

  registerTimeout(id: string, timeoutId: NodeJS.Timeout): void {
    const scopedId = this.makeScopedId(id)
    this.scopedIds.add(scopedId)
    this.parent.registerTimeout(scopedId, timeoutId)
  }

  registerInterval(id: string, intervalId: NodeJS.Timeout): void {
    const scopedId = this.makeScopedId(id)
    this.scopedIds.add(scopedId)
    this.parent.registerInterval(scopedId, intervalId)
  }

  async cleanup(id: string): Promise<void> {
    const scopedId = this.makeScopedId(id)
    await this.parent.cleanup(scopedId)
    this.scopedIds.delete(scopedId)
  }

  async cleanupAll(): Promise<void> {
    const ids = Array.from(this.scopedIds)
    await Promise.all(ids.map(id => this.parent.cleanup(id)))
    this.scopedIds.clear()
  }
}

// Global memory manager instance
export const memoryManager = new MemoryManager()

// React hook for component memory management
export function useMemoryManager(componentId: string): ScopedMemoryManager {
  const scopedManager = React.useRef<ScopedMemoryManager | null>(null)
  
  if (!scopedManager.current) {
    scopedManager.current = memoryManager.createScope(componentId)
  }

  React.useEffect(() => {
    return () => {
      scopedManager.current?.cleanupAll()
    }
  }, [componentId])

  return scopedManager.current
}

// Singleton pattern for app-wide memory management
let globalMemoryManager: MemoryManager | null = null

export function getGlobalMemoryManager(): MemoryManager {
  if (!globalMemoryManager) {
    globalMemoryManager = new MemoryManager()
  }
  return globalMemoryManager
}