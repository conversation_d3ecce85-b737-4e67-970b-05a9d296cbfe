import { ComponentStatus, COMPONENT_STATUS, isFinalState, isReadyState } from '../utils/constants'
import { logger } from '../utils/logger'

type StatusTransition = {
  from: ComponentStatus
  to: ComponentStatus
  condition?: (context: ComponentContext) => boolean
  sideEffect?: (context: ComponentContext) => void
}

interface ComponentContext {
  componentId: string
  componentType: string
  dependencies?: string[]
  hasError?: boolean
  errorMessage?: string
}

class ComponentStateMachine {
  private transitions: StatusTransition[] = [
    // Initial registration
    {
      from: COMPONENT_STATUS.UNREGISTERED,
      to: COMPONENT_STATUS.REGISTERING
    },
    
    // After registration, check for dependencies
    {
      from: COMPONENT_STATUS.REGISTERING,
      to: COMPONENT_STATUS.WAITING,
      condition: (ctx) => Boolean(ctx.dependencies?.length)
    },
    
    // No dependencies, go straight to loading
    {
      from: COMPONENT_STATUS.REGISTERING,
      to: COMPONENT_STATUS.LOADING,
      condition: (ctx) => !ctx.dependencies?.length
    },
    
    // Dependencies resolved, start loading
    {
      from: COMPONENT_STATUS.WAITING,
      to: COMPONENT_STATUS.LOADING
    },
    
    // Loading complete
    {
      from: COMPONENT_STATUS.LOADING,
      to: COMPONENT_STATUS.LOADED,
      condition: (ctx) => !ctx.hasError
    },
    
    // Loading failed
    {
      from: COMPONENT_STATUS.LOADING,
      to: COMPONENT_STATUS.ERROR,
      condition: (ctx) => Boolean(ctx.hasError)
    },
    
    // Error recovery - back to loading
    {
      from: COMPONENT_STATUS.ERROR,
      to: COMPONENT_STATUS.LOADING
    },
    
    // Preserve from loaded state
    {
      from: COMPONENT_STATUS.LOADED,
      to: COMPONENT_STATUS.PRESERVED
    },
    
    // Lock from loaded or preserved state
    {
      from: COMPONENT_STATUS.LOADED,
      to: COMPONENT_STATUS.LOCKED
    },
    {
      from: COMPONENT_STATUS.PRESERVED,
      to: COMPONENT_STATUS.LOCKED
    },
    
    // Unlock back to loaded
    {
      from: COMPONENT_STATUS.LOCKED,
      to: COMPONENT_STATUS.LOADED
    }
  ]

  canTransition(from: ComponentStatus, to: ComponentStatus, context: ComponentContext): boolean {
    const transition = this.transitions.find(t => t.from === from && t.to === to)
    
    if (!transition) {
      logger.warn('StateMachine', 'canTransition', 
        `No transition defined from ${from} to ${to} for component ${context.componentId}`)
      return false
    }
    
    if (transition.condition && !transition.condition(context)) {
      logger.debug('StateMachine', 'canTransition', 
        `Transition condition failed from ${from} to ${to} for component ${context.componentId}`)
      return false
    }
    
    return true
  }

  transition(from: ComponentStatus, to: ComponentStatus, context: ComponentContext): ComponentStatus {
    if (!this.canTransition(from, to, context)) {
      logger.error('StateMachine', 'transition', 
        `Invalid transition from ${from} to ${to} for component ${context.componentId}`)
      return from // Stay in current state
    }
    
    const transition = this.transitions.find(t => t.from === from && t.to === to)!
    
    // Execute side effect if present
    if (transition.sideEffect) {
      try {
        transition.sideEffect(context)
      } catch (error) {
        logger.error('StateMachine', 'transition', 
          `Side effect failed for transition ${from} -> ${to}`, error as Error)
      }
    }
    
    logger.debug('StateMachine', 'transition', 
      `Component ${context.componentId} transitioned from ${from} to ${to}`)
    
    return to
  }

  getValidTransitions(from: ComponentStatus): ComponentStatus[] {
    return this.transitions
      .filter(t => t.from === from)
      .map(t => t.to)
  }

  isValidStatus(status: string): status is ComponentStatus {
    return Object.values(COMPONENT_STATUS).includes(status as ComponentStatus)
  }

  isFinalState(status: ComponentStatus): boolean {
    return isFinalState(status)
  }

  isReadyState(status: ComponentStatus): boolean {
    return isReadyState(status)
  }

  // Automatic status determination based on context
  determineNextStatus(current: ComponentStatus, context: ComponentContext): ComponentStatus {
    const validTransitions = this.getValidTransitions(current)
    
    // Find the first valid transition that meets conditions
    for (const nextStatus of validTransitions) {
      if (this.canTransition(current, nextStatus, context)) {
        return nextStatus
      }
    }
    
    return current // No valid transitions
  }
}

export const componentStateMachine = new ComponentStateMachine()
export type { ComponentContext, StatusTransition }