// Utilities
export { logger } from './utils/logger'
export { debounce, throttle, TimeoutRegistry, TIMEOUT_DELAYS } from './utils/timeouts'
export type { TimeoutManager } from './utils/timeouts'
export { 
  COMPONENT_STATUS, 
  GROUP_STATUS, 
  DOCUMENT_STATUS, 
  ENTITY_CHANGE_STATUS,
  FINAL_STATES,
  READY_STATES,
  isValidComponentStatus,
  isFinalState,
  isReadyState
} from './utils/constants'
export type { ComponentStatus, GroupStatus, DocumentStatus, EntityChangeStatus } from './utils/constants'
export { GroupStatusManager, groupStatusManager } from './utils/groupManager'
export { MemoryManager, ScopedMemoryManager, memoryManager, useMemoryManager, getGlobalMemoryManager } from './utils/memoryManager'
export { performanceMonitor, usePerformanceMonitor, performanceDecorator, asyncPerformanceDecorator } from './utils/performanceMonitor'

// State Machine
export { componentStateMachine } from './state/componentStateMachine'
export type { ComponentContext, StatusTransition } from './state/componentStateMachine'

// Supabase Services
export { reportService, ReportService } from './supabase/reportService'
export { versionService, VersionService } from './supabase/versionService'
export type { DocumentVersion } from './supabase/versionService'