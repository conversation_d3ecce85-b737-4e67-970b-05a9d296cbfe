import { useCallback, useEffect, useRef, useState } from 'react'
import { Editor } from '@tiptap/react'
import { createClient } from '@/app/supabase/client'

interface UseAutoSaveOptions {
  documentId: string
  editor?: Editor
  onSave?: (content: string, data?: any) => Promise<void> | void
  saveInterval?: number // in milliseconds
  createVersionInterval?: number // in milliseconds
  enabled?: boolean
}

interface AutoSaveState {
  isSaving: boolean
  lastSaved: Date | null
  hasUnsavedChanges: boolean
  error: string | null
}

export function useAutoSave({
  documentId,
  editor,
  onSave,
  saveInterval = 30000, // 30 seconds
  createVersionInterval = 300000, // 5 minutes
  enabled = true
}: UseAutoSaveOptions) {
  const [state, setState] = useState<AutoSaveState>({
    isSaving: false,
    lastSaved: null,
    hasUnsavedChanges: false,
    error: null
  })

  const supabase = createClient()
  const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const versionTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const lastContentRef = useRef<string>('')
  const lastDataRef = useRef<any>(null)

  // Save document content
  const saveDocument = useCallback(async (createVersion = false) => {
    if (!editor || !documentId || state.isSaving) return

    setState(prev => ({ ...prev, isSaving: true, error: null }))

    try {
      const content = editor.getHTML()
      const data = editor.getJSON()

      // Call the provided save function
      if (onSave) {
        await onSave(content, data)
      }

      // Create a version if requested
      if (createVersion) {
        const { data: { user } } = await supabase.auth.getUser()

        if (user) {
          // Get the next version number
          const { data: latestVersion } = await supabase
            .from('doc_versions')
            .select('version_number')
            .eq('document_id', documentId)
            .order('version_number', { ascending: false })
            .limit(1)
            .single()

          const nextVersionNumber = (latestVersion?.version_number || 0) + 1

          await supabase
            .from('doc_versions')
            .insert({
              document_id: documentId,
              version_number: nextVersionNumber,
              content,
              data,
              created_by: user.id,
              change_summary: 'Auto-save version',
              is_auto_save: true
            })
        }
      }

      // Update refs to track changes
      lastContentRef.current = content
      lastDataRef.current = data

      setState(prev => ({
        ...prev,
        isSaving: false,
        lastSaved: new Date(),
        hasUnsavedChanges: false,
        error: null
      }))
    } catch (error) {
      console.error('Auto-save error:', error)
      setState(prev => ({
        ...prev,
        isSaving: false,
        error: error instanceof Error ? error.message : 'Failed to save'
      }))
    }
  }, [editor, documentId, onSave, state.isSaving])

  // Check if content has changed
  const hasContentChanged = useCallback(() => {
    if (!editor) return false

    const currentContent = editor.getHTML()
    const currentData = JSON.stringify(editor.getJSON())
    const lastData = JSON.stringify(lastDataRef.current)

    return currentContent !== lastContentRef.current || currentData !== lastData
  }, [editor])

  // Schedule auto-save
  const scheduleAutoSave = useCallback(() => {
    if (!enabled || !editor) return

    // Clear existing timeout
    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current)
    }

    // Schedule new save
    saveTimeoutRef.current = setTimeout(() => {
      if (hasContentChanged()) {
        saveDocument(false)
      }
    }, saveInterval)
  }, [enabled, editor, hasContentChanged, saveDocument, saveInterval])

  // Schedule version creation
  const scheduleVersionCreation = useCallback(() => {
    if (!enabled || !editor) return

    // Clear existing timeout
    if (versionTimeoutRef.current) {
      clearTimeout(versionTimeoutRef.current)
    }

    // Schedule new version creation
    versionTimeoutRef.current = setTimeout(() => {
      if (hasContentChanged()) {
        saveDocument(true)
      }
    }, createVersionInterval)
  }, [enabled, editor, hasContentChanged, saveDocument, createVersionInterval])

  // Handle editor updates
  useEffect(() => {
    if (!editor || !enabled) return

    const handleUpdate = () => {
      setState(prev => ({ ...prev, hasUnsavedChanges: hasContentChanged() }))
      scheduleAutoSave()
    }

    // Listen to editor updates
    editor.on('update', handleUpdate)

    // Initial setup
    if (editor.getHTML()) {
      lastContentRef.current = editor.getHTML()
      lastDataRef.current = editor.getJSON()
    }

    return () => {
      editor.off('update', handleUpdate)
    }
  }, [editor, enabled, hasContentChanged, scheduleAutoSave])

  // Set up version creation interval
  useEffect(() => {
    if (!enabled) return

    scheduleVersionCreation()

    return () => {
      if (versionTimeoutRef.current) {
        clearTimeout(versionTimeoutRef.current)
      }
    }
  }, [enabled, scheduleVersionCreation])

  // Handle page unload - save before leaving
  useEffect(() => {
    if (!enabled) return

    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      if (state.hasUnsavedChanges) {
        // Try to save synchronously (may not work in all browsers)
        saveDocument(false)

        // Show warning to user
        event.preventDefault()
        event.returnValue = 'You have unsaved changes. Are you sure you want to leave?'
        return event.returnValue
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, [enabled, state.hasUnsavedChanges, saveDocument])

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current)
      }
      if (versionTimeoutRef.current) {
        clearTimeout(versionTimeoutRef.current)
      }
    }
  }, [])

  // Manual save function
  const manualSave = useCallback(async (createVersion = false) => {
    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current)
    }
    await saveDocument(createVersion)
  }, [saveDocument])

  // Force save function (ignores change detection)
  const forceSave = useCallback(async (createVersion = false) => {
    if (!editor || !documentId) return

    setState(prev => ({ ...prev, isSaving: true, error: null }))

    try {
      const content = editor.getHTML()
      const data = editor.getJSON()

      if (onSave) {
        await onSave(content, data)
      }

      if (createVersion) {
        const { data: { user } } = await supabase.auth.getUser()

        if (user) {
          // Get the next version number
          const { data: latestVersion } = await supabase
            .from('doc_versions')
            .select('version_number')
            .eq('document_id', documentId)
            .order('version_number', { ascending: false })
            .limit(1)
            .single()

          const nextVersionNumber = (latestVersion?.version_number || 0) + 1

          await supabase
            .from('doc_versions')
            .insert({
              document_id: documentId,
              version_number: nextVersionNumber,
              content,
              data,
              created_by: user.id,
              change_summary: 'Manual save version',
              is_auto_save: false
            })
        }
      }

      lastContentRef.current = content
      lastDataRef.current = data

      setState(prev => ({
        ...prev,
        isSaving: false,
        lastSaved: new Date(),
        hasUnsavedChanges: false,
        error: null
      }))
    } catch (error) {
      console.error('Force save error:', error)
      setState(prev => ({
        ...prev,
        isSaving: false,
        error: error instanceof Error ? error.message : 'Failed to save'
      }))
    }
  }, [editor, documentId, onSave])

  return {
    ...state,
    manualSave,
    forceSave,
    hasContentChanged
  }
}

export default useAutoSave
