import { useState, useEffect, useCallback, useRef, useMemo } from 'react'
import { Editor } from '@tiptap/react'
import { createClient } from '@/app/supabase/client'
import type { RealtimeChannel } from '@supabase/supabase-js'

interface PresenceUser {
  id: string
  name: string
  email?: string
  avatar?: string
  color: string
  cursorPosition?: any
  selection?: any
  online_at: string
}

interface UsePresenceOptions {
  documentId: string
  user: {
    id: string
    name: string
    email?: string
    avatar?: string
    color?: string
  }
  editor?: Editor
}

export function usePresence({
  documentId,
  user,
  editor
}: UsePresenceOptions) {
  const [activeUsers, setActiveUsers] = useState<PresenceUser[]>([])
  const [isConnected, setIsConnected] = useState(false)

  // Memoize supabase client to prevent re-creation on every render
  const supabase = useMemo(() => createClient(), [])

  const channelRef = useRef<RealtimeChannel | null>(null)
  const lastUpdateRef = useRef<Date>(new Date())

  // Memoize user data to prevent unnecessary re-renders
  const memoizedUser = useMemo(() => ({
    id: user.id,
    name: user.name,
    email: user.email,
    avatar: user.avatar,
    color: user.color || '#3B82F6'
  }), [user.id, user.name, user.email, user.avatar, user.color])

  // Update user presence
  const updatePresence = useCallback(async () => {
    if (!memoizedUser.id) return

    try {
      let cursorPosition = null
      let selection = null

      // Get cursor position and selection from editor if available
      if (editor) {
        const { from, to } = editor.state.selection
        cursorPosition = { from, to }

        if (from !== to) {
          selection = {
            from,
            to,
            text: editor.state.doc.textBetween(from, to)
          }
        }
      }

      if (!channelRef.current) return

      const presenceState = {
        id: memoizedUser.id,
        name: memoizedUser.name,
        email: memoizedUser.email,
        avatar: memoizedUser.avatar,
        color: memoizedUser.color,
        cursorPosition,
        selection,
        online_at: new Date().toISOString()
      }

      await channelRef.current.track(presenceState)

      lastUpdateRef.current = new Date()
    } catch (error) {
      console.error('Error updating presence:', error)
    }
  }, [memoizedUser, editor])

  // Remove user presence when leaving
  const removePresence = useCallback(async () => {
    if (!channelRef.current) return

    try {
      await channelRef.current.untrack()
    } catch (error) {
      console.error('Error removing presence:', error)
    }
  }, [])

  // Set up presence tracking
  useEffect(() => {
    if (!documentId || !memoizedUser.id) return

    // Create channel for this document
    const channel = supabase.channel(`document:${documentId}`, {
      config: {
        presence: {
          key: memoizedUser.id
        }
      }
    })

    channelRef.current = channel

    // Subscribe to presence events
    channel
      .on('presence', { event: 'sync' }, () => {
        const presenceState = channel.presenceState()
        const users: PresenceUser[] = []

        Object.keys(presenceState).forEach((userId) => {
          const presence = presenceState[userId]
          if (presence && presence.length > 0) {
            const latestPresence = presence[0] as any
            if (latestPresence.id !== memoizedUser.id) { // Exclude current user
              users.push({
                id: latestPresence.id,
                name: latestPresence.name,
                email: latestPresence.email,
                avatar: latestPresence.avatar,
                color: latestPresence.color,
                cursorPosition: latestPresence.cursorPosition,
                selection: latestPresence.selection,
                online_at: latestPresence.online_at
              })
            }
          }
        })

        setActiveUsers(users)
      })
      .on('presence', { event: 'join' }, ({ key, newPresences }) => {
        // Reduced logging - only log in development
        if (process.env.NODE_ENV === 'development') {
          console.log('User joined:', key, newPresences.length, 'presences')
        }
      })
      .on('presence', { event: 'leave' }, ({ key, leftPresences }) => {
        // Reduced logging - only log in development
        if (process.env.NODE_ENV === 'development') {
          console.log('User left:', key, leftPresences.length, 'presences')
        }
      })
      .subscribe(async (status) => {
        if (status === 'SUBSCRIBED') {
          setIsConnected(true)
          // Track initial presence
          await updatePresence()
        }
      })

    // Handle page visibility changes
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        updatePresence()
      }
    }

    // Handle beforeunload to clean up presence
    const handleBeforeUnload = () => {
      removePresence()
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('beforeunload', handleBeforeUnload)

    return () => {
      // Cleanup
      supabase.removeChannel(channel)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('beforeunload', handleBeforeUnload)

      // Remove presence on unmount
      removePresence()
      setIsConnected(false)
      channelRef.current = null
    }
  }, [documentId, memoizedUser.id, updatePresence, removePresence, supabase])

  // Update presence when editor selection changes
  useEffect(() => {
    if (!editor) return

    let timeoutId: NodeJS.Timeout | null = null

    const handleSelectionUpdate = () => {
      // Clear any pending update
      if (timeoutId) {
        clearTimeout(timeoutId)
      }

      // Debounce updates to avoid too many presence updates
      timeoutId = setTimeout(() => {
        const now = new Date()
        const timeSinceLastUpdate = now.getTime() - lastUpdateRef.current.getTime()

        // Only update if more than 2 seconds have passed to reduce frequency
        if (timeSinceLastUpdate > 2000) {
          updatePresence()
        }
      }, 500) // Wait 500ms after last selection change
    }

    editor.on('selectionUpdate', handleSelectionUpdate)

    return () => {
      editor.off('selectionUpdate', handleSelectionUpdate)
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
    }
  }, [editor, updatePresence])

  return {
    activeUsers,
    isConnected,
    updatePresence,
    removePresence
  }
}

export default usePresence
