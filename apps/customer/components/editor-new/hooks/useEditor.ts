import { useCallback } from 'react'
import { useComponents, useDocument, useVersioning } from '../context/providers/EditorProvider'
import { ReportComponent, ComponentUpdate } from '../types'
import { logger, componentStateMachine } from '../services'
import type { ComponentStatus } from '../services'

/**
 * Main hook for editor functionality
 * Combines component, document, and versioning operations
 */
export function useEditor() {
  const componentContext = useComponents()
  const documentContext = useDocument()
  const versioningContext = useVersioning()

  // Component operations
  const registerComponent = useCallback((component: ReportComponent) => {
    logger.info('useEditor', 'registerComponent', `Registering component ${component.id}`)
    componentContext.registerComponent(component)
  }, [componentContext])

  const updateComponent = useCallback((update: ComponentUpdate) => {
    logger.debug('useEditor', 'updateComponent', `Updating component ${update.id}`)
    componentContext.updateComponent(update)
  }, [componentContext])

  const getComponent = useCallback((id: string) => {
    return componentContext.getComponent(id)
  }, [componentContext])

  const getComponentsByStatus = useCallback((status: ComponentStatus) => {
    return componentContext.getComponentsByStatus(status)
  }, [componentContext])

  // Document operations
  const loadDocument = useCallback(async (reportId: string) => {
    logger.info('useEditor', 'loadDocument', `Loading document ${reportId}`)
    // Document loading will be handled by DocumentProvider initialization
    documentContext.setEntityRun(null, 'latest')
  }, [documentContext])

  const saveDocument = useCallback(async () => {
    logger.info('useEditor', 'saveDocument', 'Saving document')
    await documentContext.triggerImmediateSave()
  }, [documentContext])

  const setContent = useCallback((content: string, data: any, source?: 'user' | 'system' | 'restore') => {
    documentContext.dispatch({ type: 'CONTENT_CHANGED', content, data, source: source || 'user' })
  }, [documentContext])

  // Version operations
  const createVersion = useCallback(async (changeSummary?: string) => {
    logger.info('useEditor', 'createVersion', 'Creating manual version')
    return await versioningContext.createVersion(changeSummary)
  }, [versioningContext])

  const restoreVersion = useCallback(async (versionNumber: number) => {
    logger.info('useEditor', 'restoreVersion', `Restoring version ${versionNumber}`)
    await versioningContext.restoreVersion(versionNumber)
  }, [versioningContext])

  // Combined operations
  const initializeDocument = useCallback(async (reportId: string) => {
    logger.info('useEditor', 'initializeDocument', `Initializing document ${reportId}`)
    
    try {
      // Load document content
      await loadDocument(reportId)
      
      // Load version history
      await versioningContext.listVersions()
      
      // Mark as initialized  
      documentContext.dispatch({ type: 'DOCUMENT_INITIALIZED' })
      
      logger.info('useEditor', 'initializeDocument', `Document ${reportId} initialized successfully`)
    } catch (error) {
      logger.error('useEditor', 'initializeDocument', `Failed to initialize document ${reportId}`, error as Error)
      throw error
    }
  }, [loadDocument, versioningContext, documentContext])

  const resetEditor = useCallback(() => {
    logger.info('useEditor', 'resetEditor', 'Resetting editor state')
    
    // Reset document - dispatch reset actions
    documentContext.dispatch({ type: 'RESET_DIRTY_STATE' })
    
    // Reset versioning state would need to be added to VersioningContext
    // For now, this will reset when documentId changes
  }, [documentContext])

  // State getters
  const getEditorState = useCallback(() => {
    return {
      // Document state
      editor: documentContext.state.editor,
      content: documentContext.state.content,
      data: documentContext.state.data,
      reportId: 'unknown', // Not available in new context
      status: documentContext.state.isSaving ? 'saving' : 'idle',
      hasUnsavedChanges: documentContext.state.isDirty,
      lastSaved: documentContext.state.lastSaved,
      error: documentContext.state.saveError,
      
      // Component state
      components: componentContext.components,
      totalComponents: componentContext.components.size,
      loadedComponents: componentContext.getComponentsByStatus('loaded').length,
      areAllComponentsLoaded: componentContext.areAllComponentsInFinalState(),
      
      // Version state
      versions: versioningContext.versions,
      currentVersion: versioningContext.currentVersion,
      autoSaveEnabled: versioningContext.autoSaveEnabled,
      lastAutoSave: versioningContext.lastAutoSave,
      
      // UI state
      showSidePanel: documentContext.state.showSidePanel,
      activePanelTab: documentContext.state.activePanelTab,
      isInitialized: documentContext.state.isInitialized
    }
  }, [componentContext, documentContext, versioningContext])

  const getComponentState = useCallback((componentId: string) => {
    const component = componentContext.getComponent(componentId)
    if (!component) return null

    return {
      component,
      validTransitions: componentStateMachine.getValidTransitions(component.status),
      isFinalState: componentStateMachine.isFinalState(component.status),
      isReadyState: componentStateMachine.isReadyState(component.status),
      children: componentContext.getComponentsByParent(componentId),
      hasChildren: componentContext.componentHierarchy.has(componentId)
    }
  }, [componentContext])

  return {
    // State
    ...getEditorState(),
    
    // Component operations
    registerComponent,
    updateComponent,
    removeComponent: componentContext.removeComponent,
    getComponent,
    getComponentsByStatus,
    getComponentsByType: componentContext.getComponentsByType,
    getComponentsByParent: componentContext.getComponentsByParent,
    getComponentTree: componentContext.getComponentTree,
    getComponentState,
    
    // Document operations
    loadDocument,
    saveDocument,
    resetEditor,
    setEditor: (editor: any) => documentContext.dispatch({ type: 'EDITOR_CREATED', editor }),
    setContent,
    setUnsavedChanges: (dirty: boolean) => {
      if (dirty) documentContext.dispatch({ type: 'CONTENT_CHANGED', content: '', data: null, source: 'user' })
      else documentContext.dispatch({ type: 'RESET_DIRTY_STATE' })
    },
    addCitation: documentContext.addCitation,
    addCitations: documentContext.registerCitations,
    setSidePanel: (show: boolean, tab?: any) => documentContext.togglePanel(show, tab),
    setEntity: (entity: string | null, run: string) => documentContext.setEntityRun(entity, run),
    
    // Version operations
    createVersion,
    restoreVersion,
    loadVersion: versioningContext.loadVersion,
    deleteVersion: versioningContext.deleteVersion,
    setAutoSaveEnabled: versioningContext.setAutoSaveEnabled,
    
    // Combined operations
    initializeDocument
  }
}

/**
 * Hook for component-specific operations
 */
export function useEditorComponent(componentId: string) {
  const editor = useEditor()
  const component = editor.getComponent(componentId)
  
  const update = useCallback((updates: Partial<ReportComponent>) => {
    editor.updateComponent({ id: componentId, ...updates })
  }, [editor, componentId])
  
  const remove = useCallback(() => {
    editor.removeComponent(componentId)
  }, [editor, componentId])
  
  const setState = useCallback((status: ComponentStatus) => {
    editor.updateComponent({ id: componentId, status })
  }, [editor, componentId])
  
  return {
    component,
    update,
    remove,
    setState,
    state: editor.getComponentState(componentId)
  }
}