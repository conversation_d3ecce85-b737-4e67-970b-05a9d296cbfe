'use client'

import React, { useEffect, useState } from 'react'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@ui/components/ui/select'
import { createClient } from '@/app/supabase/client'
import { useAuth } from '@/components/context/auth/auth-context'
import { Label } from '@ui/components/ui/label'
import { Switch } from '@ui/components/ui/switch'


interface EntityOption {
  id: string
  name: string
}

interface RunOption {
  id: string
  name: string
  run_date?: string
  start_year?: number
  end_year?: number
  run_type?: string
}

interface DocumentEntityRunSelectorProps {
  selectedEntity?: string | null
  selectedRun?: string
  includeDisclosures?: boolean
  onEntityChange: (entityId: string) => void
  onRunChange: (runId: string) => void
  onDisclosuresChange: (include: boolean) => void
  className?: string
}

export function DocumentEntityRunSelector({
  selectedEntity,
  selectedRun = 'latest',
  includeDisclosures = true,
  onEntityChange,
  onRunChange,
  onDisclosuresChange,
  className
}: DocumentEntityRunSelectorProps) {
  const [entities, setEntities] = useState<EntityOption[]>([])
  const [runs, setRuns] = useState<RunOption[]>([])
  const [loadingEntities, setLoadingEntities] = useState(true)
  const [loadingRuns, setLoadingRuns] = useState(false)
  const [entityError, setEntityError] = useState<string | null>(null)
  const [runError, setRunError] = useState<string | null>(null)

  const auth = useAuth()
  const supabase = createClient()

  // Simple wrapper functions to update parent state
  // The DocumentProvider will handle syncing with DocumentContext via props
  const handleEntityChange = (entityId: string) => {
    onEntityChange(entityId)
  }

  const handleRunChange = (runId: string) => {
    onRunChange(runId)
  }

  const handleDisclosuresChange = (include: boolean) => {
    onDisclosuresChange(include)
  }



  // Fetch entities on mount
  useEffect(() => {
    async function fetchEntities() {
      try {
        setEntityError(null)
        const userId = auth.user?.id
        console.log('DocumentEntityRunSelector: Auth state:', {
          hasUser: !!auth.user,
          userId: userId
        })

        if (!userId) {
          console.log('No user ID available for fetching entities')
          setEntityError('User not authenticated')
          setLoadingEntities(false)
          return
        }

        console.log('Fetching entities for user:', userId)

        // Use the same pattern as the working EntitySelector
        const { data, error } = await supabase
          .from('view_my_companies')
          .select('*')
          .eq('profile_id', userId)

        console.log('Entity fetch result:', { data, error, userId })

        if (error) {
          console.error('Error fetching entities from view_my_companies:', error)
          setEntityError('Failed to load entities')
          setLoadingEntities(false)
          return
        }

        if (!data || data.length === 0) {
          console.warn('No entities found for user:', userId)
          setEntityError('No entities available')
          setLoadingEntities(false)
          return
        }

        console.log('Fetched entities:', data)
        const entityOptions = data.map((item: any) => ({
          id: item.entity_xid as string,
          name: item.name as string
        }))

        setEntities(entityOptions)

        // Auto-select first entity if none is currently selected
        if (!selectedEntity && entityOptions.length > 0) {
          console.log('Auto-selecting first entity:', entityOptions[0])
          onEntityChange(entityOptions[0].id)
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error)
        console.error('Error fetching entities:', {
          error: errorMessage,
          userId: auth.user?.id,
          stack: error instanceof Error ? error.stack : undefined
        })
        setEntityError(`Failed to load entities: ${errorMessage}`)
      } finally {
        setLoadingEntities(false)
      }
    }

    if (auth.user?.id) {
      fetchEntities()
    } else if (!auth.user) {
      setLoadingEntities(false)
      setEntityError('Please log in to view entities')
    }
  }, [auth.user?.id]) // Only depend on user ID

  // Auto-selection is now handled directly in fetchEntities to avoid timing issues

  // Fetch runs when entity changes
  useEffect(() => {
    async function fetchRuns() {
      if (!selectedEntity) {
        setRuns([{ id: 'latest', name: 'Latest Run' }]) // Always include latest run option
        return
      }

      setLoadingRuns(true)
      setRunError(null)
      try {
        console.log('Fetching runs for entity:', selectedEntity)

        // Fetch entity-specific runs
        const { data: entityRuns, error: entityRunsError } = await supabase
          .from('xfer_runs')
          .select('run_id, run_date, start_year, end_year, run_type')
          .eq('scope', 'entity')
          .eq('target', selectedEntity)
          .order('run_date', { ascending: false })
          .limit(10)

        // Fetch general runs
        const { data: generalRuns, error: generalRunsError } = await supabase
          .from('xfer_runs')
          .select('run_id, run_date, start_year, end_year, run_type')
          .eq('scope', 'all')
          .order('run_date', { ascending: false })
          .limit(5)

        if (entityRunsError) {
          console.error('Error fetching entity runs:', {
            error: entityRunsError,
            entity: selectedEntity
          })
          setRunError('Failed to load entity runs')
        }
        if (generalRunsError) {
          console.error('Error fetching general runs:', {
            error: generalRunsError
          })
          setRunError('Failed to load general runs')
        }

        // Combine and format runs
        const allRuns = [
          ...(entityRuns || []),
          ...(generalRuns || [])
        ]

        console.log('Fetched runs:', allRuns)

        const runOptions: RunOption[] = [
          { id: 'latest', name: 'Latest Run' },
          ...allRuns.map((run: any) => ({
            id: run.run_id.toString(),
            name: `Run ${run.run_id} (${run.run_date ? new Date(run.run_date).toLocaleDateString() : 'No date'}) : ${run.start_year} - ${run.end_year || 'now'} (${run.run_type})`,
            run_date: run.run_date,
            start_year: run.start_year,
            end_year: run.end_year,
            run_type: run.run_type
          }))
        ]

        setRuns(runOptions)
      } catch (error) {
        console.error('Error fetching runs:', error)
        setRunError('Failed to load runs')
      } finally {
        setLoadingRuns(false)
      }
    }

    fetchRuns()
  }, [selectedEntity]) // supabase not included to prevent infinite loops

  return (
    <div className={`flex items-center gap-3 p-2 bg-muted/30 rounded-lg border ${className || ''}`}>
      {/* Entity Selector */}
      <div className="flex items-center gap-2 min-w-0 flex-1">
        <Label htmlFor="entity-select" className="text-sm font-medium whitespace-nowrap">
          Entity:
        </Label>
        <Select
          value={selectedEntity || ''}
          onValueChange={handleEntityChange}
          disabled={loadingEntities || !!entityError}
        >
          <SelectTrigger id="entity-select" data-testid="entity-select" className="h-8 text-sm">
            <SelectValue placeholder={
              entityError ? "Error loading entities" :
              loadingEntities ? "Loading entities..." :
              "Select entity"
            } />
          </SelectTrigger>
          <SelectContent>
            {entities.map(entity => (
              <SelectItem key={entity.id} value={entity.id}>
                {entity.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Run Selector */}
      <div className="flex items-center gap-2 min-w-0 flex-1">
        <Label htmlFor="run-select" className="text-sm font-medium whitespace-nowrap">
          Run:
        </Label>
        <Select
          value={selectedRun}
          onValueChange={handleRunChange}
          disabled={loadingRuns || !selectedEntity || !!runError}
        >
          <SelectTrigger id="run-select" data-testid="run-select" className="h-8 text-sm">
            <SelectValue placeholder={
              runError ? "Error loading runs" :
              loadingRuns ? "Loading runs..." :
              !selectedEntity ? "Select entity first" :
              "Select run"
            } />
          </SelectTrigger>
          <SelectContent>
            {runs.map(run => (
              <SelectItem key={run.id} value={run.id}>
                {run.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Disclosures Toggle */}
      <div className="flex items-center gap-2 whitespace-nowrap">
        <Switch
          id="include-disclosures"
          checked={includeDisclosures}
          onCheckedChange={handleDisclosuresChange}
          className="scale-75"
        />
        <Label htmlFor="include-disclosures" className="text-sm cursor-pointer">
          Disclosures
        </Label>
      </div>
    </div>
  )
}
