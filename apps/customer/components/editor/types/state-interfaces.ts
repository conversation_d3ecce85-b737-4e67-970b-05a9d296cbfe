/**
 * Consolidated State Interfaces for Editor Components
 * 
 * These interfaces replace ref-based state management with proper TypeScript
 * interfaces that can be used with state management solutions like Zustand.
 */

// Core component tracking interfaces
export interface ComponentUpdateState {
  updatingComponents: Set<string>;
  pendingSaves: Set<string>;
  processingUpdates: Set<string>;
  pendingRegistrations: Set<string>;
}

export interface GroupStatusState {
  groupStatusUpdateTimeouts: Map<string, string>;
  pendingGroupUpdates: Set<string>;
  updateChain: Set<string>;
  isProcessingBatch: boolean;
}

export interface ComponentRegistrationState {
  orphanedComponents: Map<string, ReportComponent[]>;
  registeredComponents: Set<string>;
  componentHierarchy: Map<string, string[]>;
}

// Auto-save and versioning interfaces
export interface AutoSaveState {
  saveTimeoutId: NodeJS.Timeout | null;
  versionTimeoutId: NodeJS.Timeout | null;
  lastContent: string;
  lastData: any;
  hasUnsavedChanges: boolean;
  isAutoSaving: boolean;
}

export interface VersioningState {
  currentVersion: string;
  versions: DocumentVersion[];
  isCreatingVersion: boolean;
  lastAutoSaveTime: number;
}

// Document and editor state interfaces
export interface DocumentState {
  id: string;
  title: string;
  content: string;
  isLoading: boolean;
  hasUnsavedChanges: boolean;
  lastModified: Date;
  entityChangeListeners: Set<EntityChangeListener>;
}

export interface EditorState {
  isInitialized: boolean;
  hasSetInitialContent: boolean;
  renderCount: number;
  lastRenderTime: number;
  isEditorFocused: boolean;
}

// Component status and sync interfaces
export interface ComponentStatusState {
  componentStatuses: Map<string, ComponentStatus>;
  statusSyncTimeouts: Map<string, NodeJS.Timeout>;
  lastSyncedStatuses: Map<string, string>;
  isSyncing: Set<string>;
  previousStatuses: Map<string, string>;
}

export interface ComponentSyncState {
  isRegistered: Map<string, boolean>;
  loadingStates: Map<string, boolean>;
  errorStates: Map<string, Error | null>;
  syncTimestamps: Map<string, number>;
}

// Performance and monitoring interfaces
export interface PerformanceState {
  renderCounts: Map<string, number>;
  renderTimes: Map<string, number>;
  memoryUsage: number;
  componentLoadTimes: Map<string, number>;
}

export interface MonitoringState {
  activeScopes: Set<string>;
  resourceUsage: Map<string, ResourceUsage>;
  errorCounts: Map<string, number>;
  warnings: Warning[];
}

// Combined editor store interface
export interface EditorStoreState {
  // Core states
  document: DocumentState;
  editor: EditorState;
  
  // Component management
  componentUpdate: ComponentUpdateState;
  componentRegistration: ComponentRegistrationState;
  componentStatus: ComponentStatusState;
  componentSync: ComponentSyncState;
  
  // Group and batch operations
  groupStatus: GroupStatusState;
  
  // Auto-save and versioning
  autoSave: AutoSaveState;
  versioning: VersioningState;
  
  // Performance and monitoring
  performance: PerformanceState;
  monitoring: MonitoringState;
}

// Action interfaces for state updates
export interface EditorStoreActions {
  // Document actions
  updateDocument: (updates: Partial<DocumentState>) => void;
  setDocumentContent: (content: string) => void;
  markDocumentUnsaved: () => void;
  markDocumentSaved: () => void;
  
  // Component update actions
  addUpdatingComponent: (componentId: string) => void;
  removeUpdatingComponent: (componentId: string) => void;
  addPendingSave: (componentId: string) => void;
  removePendingSave: (componentId: string) => void;
  
  // Group status actions
  setGroupUpdateTimeout: (groupId: string, timeoutId: string) => void;
  clearGroupUpdateTimeout: (groupId: string) => void;
  addPendingGroupUpdate: (groupId: string) => void;
  removePendingGroupUpdate: (groupId: string) => void;
  setProcessingBatch: (isProcessing: boolean) => void;
  
  // Component registration actions
  registerComponent: (componentId: string) => void;
  unregisterComponent: (componentId: string) => void;
  addOrphanedComponent: (parentId: string, component: ReportComponent) => void;
  removeOrphanedComponent: (parentId: string, componentId: string) => void;
  
  // Auto-save actions
  setSaveTimeout: (timeoutId: NodeJS.Timeout) => void;
  clearSaveTimeout: () => void;
  setVersionTimeout: (timeoutId: NodeJS.Timeout) => void;
  clearVersionTimeout: () => void;
  updateLastContent: (content: string) => void;
  setAutoSaving: (isAutoSaving: boolean) => void;
  
  // Component status actions
  updateComponentStatus: (componentId: string, status: ComponentStatus) => void;
  setStatusSyncTimeout: (componentId: string, timeoutId: NodeJS.Timeout) => void;
  clearStatusSyncTimeout: (componentId: string) => void;
  setSyncing: (componentId: string, isSyncing: boolean) => void;
  
  // Editor actions
  setEditorInitialized: (initialized: boolean) => void;
  setInitialContentSet: (hasSet: boolean) => void;
  incrementRenderCount: () => void;
  setEditorFocused: (focused: boolean) => void;
  
  // Performance actions
  recordRender: (componentId: string, renderTime: number) => void;
  updateMemoryUsage: (usage: number) => void;
  recordComponentLoadTime: (componentId: string, loadTime: number) => void;
  
  // Monitoring actions
  addActiveScope: (scopeId: string) => void;
  removeActiveScope: (scopeId: string) => void;
  updateResourceUsage: (resourceId: string, usage: ResourceUsage) => void;
  addWarning: (warning: Warning) => void;
  clearWarnings: () => void;
  
  // Utility actions
  reset: () => void;
  cleanup: () => void;
}

// Helper types
export interface ReportComponent {
  id: string;
  type: string;
  parentId?: string;
  status: ComponentStatus;
  data: any;
}

export interface DocumentVersion {
  id: string;
  title: string;
  content: string;
  createdAt: Date;
  author: string;
}

export interface EntityChangeListener {
  id: string;
  callback: (entity: any) => void;
  filter?: (entity: any) => boolean;
}

export enum ComponentStatus {
  IDLE = 'idle',
  LOADING = 'loading',
  SUCCESS = 'success',
  ERROR = 'error',
  UPDATING = 'updating'
}

export interface ResourceUsage {
  memory: number;
  cpu: number;
  timestamp: number;
}

export interface Warning {
  id: string;
  message: string;
  level: 'low' | 'medium' | 'high';
  timestamp: number;
  componentId?: string;
}

// Initial state factories
export const createInitialComponentUpdateState = (): ComponentUpdateState => ({
  updatingComponents: new Set(),
  pendingSaves: new Set(),
  processingUpdates: new Set(),
  pendingRegistrations: new Set()
});

export const createInitialGroupStatusState = (): GroupStatusState => ({
  groupStatusUpdateTimeouts: new Map(),
  pendingGroupUpdates: new Set(),
  updateChain: new Set(),
  isProcessingBatch: false
});

export const createInitialComponentRegistrationState = (): ComponentRegistrationState => ({
  orphanedComponents: new Map(),
  registeredComponents: new Set(),
  componentHierarchy: new Map()
});

export const createInitialAutoSaveState = (): AutoSaveState => ({
  saveTimeoutId: null,
  versionTimeoutId: null,
  lastContent: '',
  lastData: null,
  hasUnsavedChanges: false,
  isAutoSaving: false
});

export const createInitialVersioningState = (): VersioningState => ({
  currentVersion: '',
  versions: [],
  isCreatingVersion: false,
  lastAutoSaveTime: 0
});

export const createInitialDocumentState = (): DocumentState => ({
  id: '',
  title: '',
  content: '',
  isLoading: false,
  hasUnsavedChanges: false,
  lastModified: new Date(),
  entityChangeListeners: new Set()
});

export const createInitialEditorState = (): EditorState => ({
  isInitialized: false,
  hasSetInitialContent: false,
  renderCount: 0,
  lastRenderTime: 0,
  isEditorFocused: false
});

export const createInitialComponentStatusState = (): ComponentStatusState => ({
  componentStatuses: new Map(),
  statusSyncTimeouts: new Map(),
  lastSyncedStatuses: new Map(),
  isSyncing: new Set(),
  previousStatuses: new Map()
});

export const createInitialComponentSyncState = (): ComponentSyncState => ({
  isRegistered: new Map(),
  loadingStates: new Map(),
  errorStates: new Map(),
  syncTimestamps: new Map()
});

export const createInitialPerformanceState = (): PerformanceState => ({
  renderCounts: new Map(),
  renderTimes: new Map(),
  memoryUsage: 0,
  componentLoadTimes: new Map()
});

export const createInitialMonitoringState = (): MonitoringState => ({
  activeScopes: new Set(),
  resourceUsage: new Map(),
  errorCounts: new Map(),
  warnings: []
});

export const createInitialEditorStoreState = (): EditorStoreState => ({
  document: createInitialDocumentState(),
  editor: createInitialEditorState(),
  componentUpdate: createInitialComponentUpdateState(),
  componentRegistration: createInitialComponentRegistrationState(),
  componentStatus: createInitialComponentStatusState(),
  componentSync: createInitialComponentSyncState(),
  groupStatus: createInitialGroupStatusState(),
  autoSave: createInitialAutoSaveState(),
  versioning: createInitialVersioningState(),
  performance: createInitialPerformanceState(),
  monitoring: createInitialMonitoringState()
});