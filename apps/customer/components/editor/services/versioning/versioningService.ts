import { createClient } from '@/app/supabase/client'
import { logger } from '../utils/logger'

export interface DocumentVersion {
  id: string
  documentId: string
  versionNumber: number
  content: string
  data?: any
  changeSummary?: string
  isAutoSave: boolean
  createdAt: Date
  userId?: string
}

export interface VersioningConfig {
  autoSaveInterval: number // ms
  maxAutoSaveVersions: number
  enableManualVersions: boolean
}

/**
 * Service for managing document versioning and auto-save functionality.
 * Centralizes all version-related operations previously in useDocumentVersioning.
 */
export class VersioningService {
  private supabase = createClient()
  private autoSaveTimers = new Map<string, NodeJS.Timeout>()
  
  private defaultConfig: VersioningConfig = {
    autoSaveInterval: 5000, // 5 seconds
    maxAutoSaveVersions: 5,
    enableManualVersions: true,
  }

  /**
   * Creates a new document version
   */
  async createVersion(params: {
    documentId: string
    content: string
    data?: any
    changeSummary?: string
    isAutoSave: boolean
    userId?: string
  }): Promise<{ version?: DocumentVersion; error?: string }> {
    try {
      const { documentId, content, data, changeSummary, isAutoSave, userId } = params
      
      logger.info('VersioningService', 'createVersion', 
        `Creating ${isAutoSave ? 'auto-save' : 'manual'} version for document ${documentId}`)

      // Get next version number
      const versionNumber = await this.getNextVersionNumber(documentId)

      const versionData = {
        document_id: documentId,
        version_number: versionNumber,
        content,
        data: data ? JSON.stringify(data) : null,
        change_summary: changeSummary,
        is_auto_save: isAutoSave,
        user_id: userId,
        created_at: new Date().toISOString(),
      }

      const { data: insertedData, error } = await this.supabase
        .from('document_versions')
        .insert(versionData)
        .select()
        .single()

      if (error) throw error

      const version: DocumentVersion = {
        id: insertedData.id as string,
        documentId: insertedData.document_id as string,
        versionNumber: insertedData.version_number as number,
        content: insertedData.content as string,
        data: insertedData.data ? JSON.parse(insertedData.data as string) : undefined,
        changeSummary: insertedData.change_summary as string,
        isAutoSave: insertedData.is_auto_save as boolean,
        createdAt: new Date(insertedData.created_at as string),
        userId: insertedData.user_id as string,
      }

      // Clean up old auto-save versions if this is an auto-save
      if (isAutoSave) {
        await this.cleanupOldAutoSaveVersions(documentId)
      }

      logger.info('VersioningService', 'createVersion', 
        `Successfully created version ${versionNumber} for document ${documentId}`)

      return { version }
    } catch (error) {
      logger.error('VersioningService', 'createVersion', 'Failed to create version', error as Error)
      return { error: error instanceof Error ? error.message : 'Failed to create version' }
    }
  }

  /**
   * Gets all versions for a document
   */
  async getDocumentVersions(documentId: string): Promise<{
    versions: DocumentVersion[]
    error?: string
  }> {
    try {
      logger.debug('VersioningService', 'getDocumentVersions', 
        `Loading versions for document ${documentId}`)

      const { data, error } = await this.supabase
        .from('document_versions')
        .select('*')
        .eq('document_id', documentId)
        .order('version_number', { ascending: false })

      if (error) throw error

      const versions: DocumentVersion[] = (data || []).map(row => ({
        id: row.id as string,
        documentId: row.document_id as string,
        versionNumber: row.version_number as number,
        content: row.content as string,
        data: row.data ? JSON.parse(row.data as string) : undefined,
        changeSummary: row.change_summary as string,
        isAutoSave: row.is_auto_save as boolean,
        createdAt: new Date(row.created_at as string),
        userId: row.user_id as string,
      }))

      logger.debug('VersioningService', 'getDocumentVersions', 
        `Found ${versions.length} versions for document ${documentId}`)

      return { versions }
    } catch (error) {
      logger.error('VersioningService', 'getDocumentVersions', 
        `Failed to load versions for document ${documentId}`, error as Error)
      return { 
        versions: [], 
        error: error instanceof Error ? error.message : 'Failed to load versions' 
      }
    }
  }

  /**
   * Gets a specific version
   */
  async getVersion(versionId: string): Promise<{
    version?: DocumentVersion
    error?: string
  }> {
    try {
      const { data, error } = await this.supabase
        .from('document_versions')
        .select('*')
        .eq('id', versionId)
        .single()

      if (error) throw error

      const version: DocumentVersion = {
        id: data.id as string,
        documentId: data.document_id as string,
        versionNumber: data.version_number as number,
        content: data.content as string,
        data: data.data ? JSON.parse(data.data as string) : undefined,
        changeSummary: data.change_summary as string,
        isAutoSave: data.is_auto_save as boolean,
        createdAt: new Date(data.created_at as string),
        userId: data.user_id as string,
      }

      return { version }
    } catch (error) {
      logger.error('VersioningService', 'getVersion', `Failed to load version ${versionId}`, error as Error)
      return { error: error instanceof Error ? error.message : 'Failed to load version' }
    }
  }

  /**
   * Starts auto-save for a document
   */
  startAutoSave(
    documentId: string,
    getContent: () => { content: string; data?: any },
    config: Partial<VersioningConfig> = {}
  ): void {
    const finalConfig = { ...this.defaultConfig, ...config }
    
    // Clear existing timer
    this.stopAutoSave(documentId)

    logger.info('VersioningService', 'startAutoSave', 
      `Starting auto-save for document ${documentId} (interval: ${finalConfig.autoSaveInterval}ms)`)

    const timer = setInterval(async () => {
      try {
        const { content, data } = getContent()
        
        // Only save if there's actual content
        if (content.trim()) {
          await this.createVersion({
            documentId,
            content,
            data,
            isAutoSave: true,
          })
        }
      } catch (error) {
        logger.error('VersioningService', 'autoSave', 'Auto-save failed', error as Error)
      }
    }, finalConfig.autoSaveInterval)

    this.autoSaveTimers.set(documentId, timer)
  }

  /**
   * Stops auto-save for a document
   */
  stopAutoSave(documentId: string): void {
    const timer = this.autoSaveTimers.get(documentId)
    if (timer) {
      clearInterval(timer)
      this.autoSaveTimers.delete(documentId)
      logger.info('VersioningService', 'stopAutoSave', `Stopped auto-save for document ${documentId}`)
    }
  }

  /**
   * Creates an immediate save (manual version)
   */
  async saveImmediate(
    documentId: string,
    content: string,
    data?: any,
    changeSummary?: string,
    userId?: string
  ): Promise<{ version?: DocumentVersion; error?: string }> {
    return this.createVersion({
      documentId,
      content,
      data,
      changeSummary,
      isAutoSave: false,
      userId,
    })
  }

  /**
   * Deletes a version
   */
  async deleteVersion(versionId: string): Promise<{ success: boolean; error?: string }> {
    try {
      logger.info('VersioningService', 'deleteVersion', `Deleting version ${versionId}`)

      const { error } = await this.supabase
        .from('document_versions')
        .delete()
        .eq('id', versionId)

      if (error) throw error

      logger.info('VersioningService', 'deleteVersion', `Successfully deleted version ${versionId}`)
      return { success: true }
    } catch (error) {
      logger.error('VersioningService', 'deleteVersion', `Failed to delete version ${versionId}`, error as Error)
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to delete version' 
      }
    }
  }

  /**
   * Gets the next version number for a document
   */
  private async getNextVersionNumber(documentId: string): Promise<number> {
    const { data, error } = await this.supabase
      .from('document_versions')
      .select('version_number')
      .eq('document_id', documentId)
      .order('version_number', { ascending: false })
      .limit(1)

    if (error || !data || data.length === 0) {
      return 1
    }

    return (data[0].version_number as number) + 1
  }

  /**
   * Cleans up old auto-save versions, keeping only the most recent ones
   */
  private async cleanupOldAutoSaveVersions(
    documentId: string,
    maxVersions: number = this.defaultConfig.maxAutoSaveVersions
  ): Promise<void> {
    try {
      // Get all auto-save versions, oldest first
      const { data, error } = await this.supabase
        .from('document_versions')
        .select('id')
        .eq('document_id', documentId)
        .eq('is_auto_save', true)
        .order('version_number', { ascending: true })

      if (error || !data) return

      // Keep only the most recent versions
      if (data.length > maxVersions) {
        const versionsToDelete = data.slice(0, data.length - maxVersions)
        const idsToDelete = versionsToDelete.map(v => v.id)

        logger.debug('VersioningService', 'cleanupOldAutoSaveVersions', 
          `Deleting ${idsToDelete.length} old auto-save versions`)

        const { error: deleteError } = await this.supabase
          .from('document_versions')
          .delete()
          .in('id', idsToDelete)

        if (deleteError) {
          logger.error('VersioningService', 'cleanupOldAutoSaveVersions', 
            'Failed to delete old versions', deleteError)
        }
      }
    } catch (error) {
      logger.error('VersioningService', 'cleanupOldAutoSaveVersions', 
        'Cleanup failed', error as Error)
    }
  }

  /**
   * Compares two versions and returns the differences
   */
  compareVersions(version1: DocumentVersion, version2: DocumentVersion): {
    contentChanged: boolean
    dataChanged: boolean
    summary: string
  } {
    const contentChanged = version1.content !== version2.content
    const dataChanged = JSON.stringify(version1.data) !== JSON.stringify(version2.data)

    let summary = 'No changes'
    if (contentChanged && dataChanged) {
      summary = 'Content and structure changed'
    } else if (contentChanged) {
      summary = 'Content changed'
    } else if (dataChanged) {
      summary = 'Structure changed'
    }

    return { contentChanged, dataChanged, summary }
  }

  /**
   * Cleanup all auto-save timers (for unmounting)
   */
  cleanup(): void {
    logger.info('VersioningService', 'cleanup', 
      `Cleaning up ${this.autoSaveTimers.size} auto-save timers`)
    
    this.autoSaveTimers.forEach((timer, documentId) => {
      clearInterval(timer)
    })
    this.autoSaveTimers.clear()
  }

  /**
   * Gets statistics about auto-save timers
   */
  getAutoSaveStats(): {
    activeDocuments: string[]
    totalTimers: number
  } {
    return {
      activeDocuments: Array.from(this.autoSaveTimers.keys()),
      totalTimers: this.autoSaveTimers.size,
    }
  }
}

// Export singleton instance
export const versioningService = new VersioningService()