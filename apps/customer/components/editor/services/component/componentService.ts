import { createClient } from '@/app/supabase/client'
import { logger } from '../utils/logger'
import type { ReportComponent } from '../../types'

/**
 * Service for managing report component CRUD operations.
 * Centralizes all component-related database operations and business logic.
 */
export class ComponentService {
  private supabase = createClient()

  /**
   * Loads a component's content from the API
   */
  async loadComponentContent(
    componentId: string,
    endpoint: string,
    params: Record<string, any> = {}
  ): Promise<{ content: string; error?: string }> {
    try {
      logger.info('ComponentService', 'loadComponentContent', `Loading content for ${componentId}`)
      
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(params),
      })

      if (!response.ok) {
        throw new Error(`API request failed: ${response.statusText}`)
      }

      const data = await response.json()
      
      logger.info('ComponentService', 'loadComponentContent', `Successfully loaded content for ${componentId}`)
      return { content: data.content || '' }
    } catch (error) {
      logger.error('ComponentService', 'loadComponentContent', `Failed to load content for ${componentId}`, error as Error)
      return { 
        content: '', 
        error: error instanceof Error ? error.message : 'Failed to load content' 
      }
    }
  }

  /**
   * Saves a component's state to the database
   */
  async saveComponent(
    documentId: string,
    component: ReportComponent
  ): Promise<{ success: boolean; error?: string }> {
    try {
      logger.info('ComponentService', 'saveComponent', `Saving component ${component.id}`)
      
      const { error } = await this.supabase
        .from('document_components')
        .upsert({
          document_id: documentId,
          component_id: component.id,
          type: component.type,
          status: component.status,
          title: component.title,
          content: component.content,
          endpoint: component.endpoint,
          prompt: component.prompt,
          dependencies: component.dependencies,
          parent_id: component.parentId,
          children: component.children,
          error: component.error,
          last_refreshed: component.lastRefreshed,
          preserved: component.preserved,
          locked: component.locked,
          updated_at: new Date().toISOString(),
        })

      if (error) throw error

      logger.info('ComponentService', 'saveComponent', `Successfully saved component ${component.id}`)
      return { success: true }
    } catch (error) {
      logger.error('ComponentService', 'saveComponent', `Failed to save component ${component.id}`, error as Error)
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to save component' 
      }
    }
  }

  /**
   * Loads all components for a document
   */
  async loadDocumentComponents(documentId: string): Promise<{
    components: ReportComponent[]
    error?: string
  }> {
    try {
      logger.info('ComponentService', 'loadDocumentComponents', `Loading components for document ${documentId}`)
      
      const { data, error } = await this.supabase
        .from('document_components')
        .select('*')
        .eq('document_id', documentId)

      if (error) throw error

      const components: ReportComponent[] = (data || []).map((row: any) => ({
        id: row.component_id,
        type: row.type,
        status: row.status,
        title: row.title,
        content: row.content,
        endpoint: row.endpoint,
        prompt: row.prompt,
        dependencies: row.dependencies,
        parentId: row.parent_id,
        children: row.children,
        error: row.error,
        lastRefreshed: row.last_refreshed ? new Date(row.last_refreshed) : undefined,
        preserved: row.preserved,
        locked: row.locked,
      }))

      logger.info('ComponentService', 'loadDocumentComponents', 
        `Successfully loaded ${components.length} components for document ${documentId}`)
      return { components }
    } catch (error) {
      logger.error('ComponentService', 'loadDocumentComponents', 
        `Failed to load components for document ${documentId}`, error as Error)
      return { 
        components: [], 
        error: error instanceof Error ? error.message : 'Failed to load components' 
      }
    }
  }

  /**
   * Deletes a component from the database
   */
  async deleteComponent(
    documentId: string,
    componentId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      logger.info('ComponentService', 'deleteComponent', `Deleting component ${componentId}`)
      
      const { error } = await this.supabase
        .from('document_components')
        .delete()
        .eq('document_id', documentId)
        .eq('component_id', componentId)

      if (error) throw error

      logger.info('ComponentService', 'deleteComponent', `Successfully deleted component ${componentId}`)
      return { success: true }
    } catch (error) {
      logger.error('ComponentService', 'deleteComponent', `Failed to delete component ${componentId}`, error as Error)
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to delete component' 
      }
    }
  }

  /**
   * Batch updates multiple components
   */
  async batchUpdateComponents(
    documentId: string,
    updates: Array<{ id: string; updates: Partial<ReportComponent> }>
  ): Promise<{ success: boolean; error?: string }> {
    try {
      logger.info('ComponentService', 'batchUpdateComponents', 
        `Batch updating ${updates.length} components`)

      const promises = updates.map(async ({ id, updates }) => {
        const { data: fullComponent, error } = await this.supabase
          .from('document_components')
          .select('*')
          .eq('id', id)
          .single()

        if (error) {
          throw new Error(`Failed to fetch component with id ${id}: ${error.message}`)
        }

        const completePayload = { ...fullComponent, ...updates };
        return this.saveComponent(documentId, completePayload as ReportComponent);
      });

      const results = await Promise.all(promises)
      const hasErrors = results.some(r => !r.success)

      if (hasErrors) {
        throw new Error('Some components failed to update')
      }

      logger.info('ComponentService', 'batchUpdateComponents', 'Successfully updated all components')
      return { success: true }
    } catch (error) {
      logger.error('ComponentService', 'batchUpdateComponents', 'Batch update failed', error as Error)
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to update components' 
      }
    }
  }

  /**
   * Refreshes a component's content
   */
  async refreshComponent(
    component: ReportComponent,
    params: Record<string, any> = {}
  ): Promise<{ content: string; error?: string }> {
    if (!component.endpoint) {
      return { content: '', error: 'No endpoint configured for component' }
    }

    logger.info('ComponentService', 'refreshComponent', `Refreshing component ${component.id}`)
    
    // Load fresh content
    const result = await this.loadComponentContent(
      component.id,
      component.endpoint,
      {
        ...params,
        prompt: component.prompt,
        componentId: component.id,
        componentType: component.type,
      }
    )

    return result
  }

  /**
   * Validates component hierarchy
   */
  validateHierarchy(components: Map<string, ReportComponent>): {
    valid: boolean
    errors: string[]
  } {
    const errors: string[] = []

    // Check for orphaned components
    components.forEach((component, id) => {
      if (component.parentId && !components.has(component.parentId)) {
        errors.push(`Component ${id} references non-existent parent ${component.parentId}`)
      }

      // Validate children references
      if (component.children) {
        component.children.forEach(childId => {
          if (!components.has(childId)) {
            errors.push(`Component ${id} references non-existent child ${childId}`)
          }
        })
      }

      // Validate dependencies
      if (component.dependencies) {
        component.dependencies.forEach(depId => {
          if (!components.has(depId)) {
            errors.push(`Component ${id} references non-existent dependency ${depId}`)
          }
        })
      }
    })

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * Gets all descendants of a component
   */
  getAllDescendants(
    componentId: string,
    components: Map<string, ReportComponent>
  ): ReportComponent[] {
    const descendants: ReportComponent[] = []
    const component = components.get(componentId)

    if (!component?.children) return descendants

    component.children.forEach(childId => {
      const child = components.get(childId)
      if (child) {
        descendants.push(child)
        // Recursively get descendants
        descendants.push(...this.getAllDescendants(childId, components))
      }
    })

    return descendants
  }

  /**
   * Gets all ancestors of a component
   */
  getAllAncestors(
    componentId: string,
    components: Map<string, ReportComponent>
  ): ReportComponent[] {
    const ancestors: ReportComponent[] = []
    let current = components.get(componentId)

    while (current?.parentId) {
      const parent = components.get(current.parentId)
      if (parent) {
        ancestors.push(parent)
        current = parent
      } else {
        break
      }
    }

    return ancestors
  }
}

// Export singleton instance
export const componentService = new ComponentService()