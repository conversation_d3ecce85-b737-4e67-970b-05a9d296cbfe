'use client'

import React, { use<PERSON>allback, useEffect, useMemo, useRef, useState } from 'react'
import { EditorContent, useEditor } from '@tiptap/react'

// --- Tiptap Core Extensions ---
import { StarterKit } from '@tiptap/starter-kit'
import { Markdown } from 'tiptap-markdown'
import { Image } from '@tiptap/extension-image'
import { TaskItem } from '@tiptap/extension-task-item'
import { TaskList } from '@tiptap/extension-task-list'
import { TextAlign } from '@tiptap/extension-text-align'
import { Typography } from '@tiptap/extension-typography'
import { Highlight } from '@tiptap/extension-highlight'
import { Subscript } from '@tiptap/extension-subscript'
import { Superscript } from '@tiptap/extension-superscript'
import { Underline } from '@tiptap/extension-underline'
import { Table } from '@tiptap/extension-table'
import { TableRow } from '@tiptap/extension-table-row'
import { TableHeader } from '@tiptap/extension-table-header'
import { TableCell } from '@tiptap/extension-table-cell'
import { Link } from '@tiptap/extension-link'
import { Dropcursor } from '@tiptap/extension-dropcursor'

// --- Block Extensions ---
import GlobalDragHandle from 'tiptap-extension-global-drag-handle'
import AutoJoiner from 'tiptap-extension-auto-joiner'
// TipTap Cloud extensions removed - using Supabase-based collaboration instead
// --- Custom Extensions ---
import { ChartExtension } from './extensions/chart-extension'
import { CitationExtension } from './extensions/CitationExtension'
import { ReportSectionExtension } from './extensions/ReportSectionExtension'
import { ReportSummaryExtension } from './extensions/ReportSummaryExtension'

import { ReferencesExtension } from './extensions/ReferencesExtension'
import { TableOfContentsExtension } from './extensions/TableOfContentsExtension'
import { SlashCommands, slashCommandsSuggestion } from './extensions/SlashCommands'
import { UniqueIdExtension } from './extensions/UniqueIdExtension'
import { DetailsExtension } from './extensions/DetailsExtension'
import { EmojiExtension, emojiSuggestion } from './extensions/EmojiExtension'
import { FileHandlerExtension, uploadFileToSupabase } from './extensions/FileHandlerExtension'
import { MathematicsExtension } from './extensions/MathematicsExtension'
import { AICommandExtension } from './extensions/AICommandExtension'
import { AISlashCommandExtension } from './extensions/AISlashCommandExtension'
import { ChangeTrackingExtension } from './extensions/ChangeTrackingExtension'
import { AccessibilityExtension } from './extensions/AccessibilityExtension'
import { ColumnsExtension } from './extensions/ColumnsExtension'

// --- Markdown Processing ---
import { processMarkdownForTipTap } from './utils/markdown-processor'

// --- Export Utilities ---
import { exportDocument, type ExportFormat } from './utils/export-utils'

// --- Hooks ---
import { useSupabaseAutoSave } from './hooks/useSupabaseAutoSave'
import { useNetworkStatus } from '@/hooks/use-network-status'

// --- UI Components ---
import { cn } from '@utils/lib/utils'
import { AriaLiveRegion, EditorAnnouncements, useAriaLiveAnnouncer } from './AriaLiveRegion'
import { OfflineIndicator } from '@/components/ui/offline-indicator'

// --- Toolbar and Panel Components ---
import { EditorToolbar } from './toolbar/EditorToolbar'
import { SupabaseCollaborationToolbar } from './toolbar/SupabaseCollaborationToolbar'
import { TabbedSidePanel } from './panels/TabbedSidePanel'
import { EditorContextBubbleMenu } from './components/ContextBubbleMenu'
import { RightClickContextMenu } from './components/RightClickContextMenu'
import { AIToolbar } from './toolbar/AIToolbar'
import { CollaborationSettingsDialog } from './dialogs/CollaborationSettingsDialog'

// --- AI Provider ---
import { CustomAIProvider } from './providers/CustomAIProvider'

// --- Types ---
import { CitationType } from '@/components/citation'
import { ReportGroupExtension } from '@/components/editor/extensions/ReportGroupComponent'
import { DocumentEntityRunDisplay } from './DocumentEntityRunDisplay'

// Safe wrapper for EditorContent to catch any rendering errors
const SafeEditorContent = ({ editor }: { editor: any }) => {
  // Use React error boundary pattern with state
  const [hasError, setHasError] = React.useState(false)
  const [errorMessage, setErrorMessage] = React.useState('')

  // Reset error state when editor changes
  React.useEffect(() => {
    setHasError(false)
    setErrorMessage('')
  }, [editor])

  try {
    if (!editor) {
      return (
        <div className="flex items-center justify-center h-32">
          <p className="text-sm text-gray-500">Editor not available</p>
        </div>
      )
    }

    if (editor.isDestroyed) {
      return (
        <div className="flex items-center justify-center h-32">
          <p className="text-sm text-red-500">Editor has been destroyed</p>
        </div>
      )
    }

    if (hasError) {
      return (
        <div className="flex items-center justify-center h-32">
          <div className="text-center">
            <p className="text-sm text-red-500">Error rendering editor content</p>
            {errorMessage && <p className="text-xs text-gray-500 mt-1">{errorMessage}</p>}
            <button
              onClick={() => {
                setHasError(false)
                setErrorMessage('')
              }}
              className="mt-2 px-3 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Retry
            </button>
          </div>
        </div>
      )
    }

    return <EditorContent editor={editor} />
  } catch (error) {
    console.error('EditorContent render error:', error)
    setHasError(true)
    setErrorMessage(error instanceof Error ? error.message : 'Unknown error')
    return (
      <div className="flex items-center justify-center h-32">
        <p className="text-sm text-red-500">Error rendering editor content</p>
      </div>
    )
  }
}

export interface EkoDocumentEditorProps {
  documentId: string
  citations?: CitationType[]
  initialContent?: string
  initialData?: any
  onSave?: (content: string, data?: any) => Promise<void> | void
  onEditorCreate?: (editor: any) => void // Callback when editor is created
  className?: string
  editable?: boolean
  showToolbar?: boolean
  showCollaboration?: boolean
  showAI?: boolean // New prop for AI features
  viewMode?: boolean // New prop for view-only mode
  printMode?: boolean // New prop for print mode styling
  entityId?: string | null // Entity ID for DocumentEntityRunDisplay
  runId?: string // Run ID for DocumentEntityRunDisplay
  user?: {
    id: string
    name: string
    email?: string
    avatar?: string
    color?: string
  }
  // Feature flag props for fine-grained control
  featureFlags?: {
    aiTools?: boolean
    aiChat?: boolean
    aiEdit?: boolean
    comments?: boolean
    share?: boolean
    exportWord?: boolean
    exportMarkdown?: boolean
    exportHtml?: boolean
  }
}

// Internal component that uses the DocumentContext
function EkoDocumentEditorInternal({
  documentId,
  citations = [],
  initialContent = '',
  initialData,
  onSave,
  onEditorCreate,
  className,
  editable = true,
  showToolbar = true,
  showCollaboration = true,
  showAI = true,
  viewMode = false,
  printMode = false,
  entityId,
  runId,
  user = {
    id: 'anonymous',
    name: 'Anonymous User',
    color: '#3B82F6',
  },
  featureFlags = {},
}: EkoDocumentEditorProps) {
  // Remove the old reportState - now handled by DocumentProvider
  const { announce } = useAriaLiveAnnouncer()
  const { isOffline } = useNetworkStatus()
  const [showSidePanel, setShowSidePanel] = useState(false)
  const [activeSidePanelTab, setActiveSidePanelTab] = useState<'comments' | 'history' | 'share' | 'ai'>('comments')
  const [showSettingsDialog, setShowSettingsDialog] = useState(false)
  const [internalPrintMode, setInternalPrintMode] = useState(printMode)
  const [processedContent, setProcessedContent] = useState<string>('')
  const [processedFootnotes, setProcessedFootnotes] = useState<any[]>([])
  const [isProcessingMarkdown, setIsProcessingMarkdown] = useState(false)
  const [hasProcessedInitialContent, setHasProcessedInitialContent] = useState(false)
  const [editorLoadTimeout, setEditorLoadTimeout] = useState(false)

  // Sync internal print mode with prop
  useEffect(() => {
    setInternalPrintMode(printMode)
  }, [printMode])

  // Add timeout to prevent infinite loading state
  useEffect(() => {
    const timeout = setTimeout(() => {
      setEditorLoadTimeout(true)
    }, 10000) // 10 second timeout

    return () => clearTimeout(timeout)
  }, [])

  // Initialize AI provider
  const aiProvider = useMemo(() => {
    if (!showAI) return null
    return new CustomAIProvider({
      apiKey: 'e884e5966f75d858f9d20eb7f828f9b173f7b4f67fe49b0e567b868d07751211', // Use the TipTap secret as API key
      baseUrl: '/api/ai'
    })
  }, [showAI])

  // Supabase auto-save will be initialized after editor is created

  // Process markdown content only ONCE on initial load when we have initialContent but no initialData
  useEffect(() => {
    const processMarkdown = async () => {
      // Only process markdown if we have initialContent but no JSON data AND haven't processed it yet
      if (!initialContent || initialData || hasProcessedInitialContent || isProcessingMarkdown) {
        return
      }

      setIsProcessingMarkdown(true)
      try {
        const result = await processMarkdownForTipTap(initialContent, citations, {
          admin: editable,
          inlineCitations: true,
          badgeStyle: true,
          skipCitations: false
        })
        setProcessedContent(result.html)
        setProcessedFootnotes(result.footnotes)
        setHasProcessedInitialContent(true)
      } catch (error) {
        console.error('Error processing markdown:', error)
        // Fallback to original content
        setProcessedContent(initialContent)
        setHasProcessedInitialContent(true)
      } finally {
        setIsProcessingMarkdown(false)
      }
    }

    processMarkdown()
  }, [initialContent, initialData, hasProcessedInitialContent, citations, editable, isProcessingMarkdown])

  // User object no longer needed for TipTap Cloud

  // Memoize extensions to prevent re-creation on every render
  // Only recreate extensions when absolutely necessary
  const extensions = useMemo(() => {
    // Create stable references for configuration objects
    const citationConfig = {
      citations: citations || [],
      admin: Boolean(editable),
    }

    const referencesConfig = {
      citations: citations || [],
      footnotes: processedFootnotes || [],
      admin: Boolean(editable),
    }

    return [
      StarterKit.configure({
        // Keep history enabled since we're not using TipTap Cloud collaboration
        history: {},
        // Disable dropcursor from StarterKit to avoid conflicts
        dropcursor: false,
      }),
      Markdown.configure({
        html: true,
        tightLists: true,
        linkify: false,
        breaks: false,
        transformPastedText: false,
        transformCopiedText: false,
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      Underline,
      TaskList,
      TaskItem.configure({
        nested: true,
      }),
      Highlight.configure({
        multicolor: true,
      }),
      Image,
      Typography,
      Superscript,
      Subscript,
      Table.configure({
        resizable: false,
        HTMLAttributes: {
          class: 'w-full',
        },
      }),
      TableRow,
      TableHeader,
      TableCell.configure({
        HTMLAttributes: {
          class: 'eko-table-cell',
        },
      }),
      Link.configure({
        openOnClick: false,
      }),
      Dropcursor,

      // Block Extensions
      GlobalDragHandle.configure({
        dragHandleWidth: 24, // Slightly larger for better accessibility
        scrollThreshold: 100,
        // Exclude certain tags from having drag handles
        excludedTags: [],
        // Include our custom report nodes
        customNodes: ['reportSection', 'reportGroup', 'reportSummary'],
        // Use custom drag handle selector to apply Tailwind classes
        dragHandleSelector: '.drag-handle',
      }),
      AutoJoiner.configure({
        elementsToJoin: ['bulletList', 'orderedList'],
      }),

      // Custom Extensions
      ChartExtension,
      CitationExtension.configure(citationConfig),
      ReportGroupExtension,
      ReportSectionExtension,
      ReportSummaryExtension,
      ReferencesExtension.configure(referencesConfig),
      TableOfContentsExtension,
      // Re-enabling extensions one by one to find the culprit
      SlashCommands.configure({
        suggestion: slashCommandsSuggestion,
      }),

      // Custom extensions replacing TipTap Pro
      UniqueIdExtension.configure({
        attributeName: 'id',
        types: ['heading', 'paragraph'],
      }),
      DetailsExtension,
      EmojiExtension.configure({
        suggestion: emojiSuggestion,
      }),
      FileHandlerExtension.configure({
        onUpload: uploadFileToSupabase,
        allowedMimeTypes: [
          'image/jpeg',
          'image/png',
          'image/gif',
          'image/webp',
          'image/svg+xml',
          'application/pdf',
          'text/plain',
          'text/markdown',
        ],
        maxFileSize: 10 * 1024 * 1024, // 10MB
      }),
      MathematicsExtension,
      ColumnsExtension,

      // Accessibility Extension
      AccessibilityExtension.configure({
        addAriaRoles: true,
        enhanceTableAccessibility: true,
        enhanceListAccessibility: true,
        enhanceHeadingAccessibility: true,
      }),

      // AI Extensions
      ...(showAI && aiProvider ? [
        ChangeTrackingExtension.configure({
          enabled: true,
          dataOpUserId: user.id,
          dataOpUserNickname: user.name,
        }),
        AICommandExtension.configure({
          onAICommand: async (command: string, selectedText: string, editor: any) => {
            if (!aiProvider) return

            try {
              const documentContent = editor.getHTML()
              const response = await fetch('/api/ai/generate', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  prompt: command,
                  selectedText,
                  documentContent,
                }),
              })

              if (!response.ok) {
                throw new Error('AI generation failed')
              }

              const result = await response.json()
              if (result.success && result.text) {
                // Apply the result as tracked changes
                const { from, to } = editor.state.selection
                editor.chain()
                  .focus()
                  .deleteRange({ from, to })
                  .insertContent(result.text)
                  .run()
              }
            } catch (error) {
              console.error('AI command failed:', error)
            }
          },
        }),
        AISlashCommandExtension.configure({
          onAICommand: async (command: string, editor: any) => {
            if (!aiProvider) return

            try {
              // Map slash command to AI prompts
              const commandMap: Record<string, string> = {
                improve: 'Improve the writing quality, clarity, and flow of this text while maintaining its original meaning and tone.',
                grammar: 'Fix any grammar, spelling, and punctuation errors in this text.',
                shorter: 'Make this text more concise while preserving all key information and meaning.',
                expand: 'Expand this text with more detail, examples, and explanation while maintaining the same tone and style.',
                tone: 'Rewrite this text in a more professional and formal tone.',
                summarize: 'Create a concise summary of this text, highlighting the main points.',
                continue: 'Continue writing from where this text ends, maintaining the same style and tone.',
                custom: 'Please provide a custom instruction for this text.',
              }

              const prompt = commandMap[command] || command
              const documentContent = JSON.stringify(editor.getJSON())

              // Use the unified chat endpoint for AI slash commands
              const stream = aiProvider.streamChatMessage(prompt, documentContent, 'current-document')

              for await (const _chunk of stream) {
                // The AI provider will handle the response and emit documentEdited events
                // which will be caught by the AIChatPanel event handlers
              }
            } catch (error) {
              console.error('AI slash command failed:', error)
            }
          },
        }),
      ] : []),

      // Note: TipTap Cloud collaboration extensions removed
      // Using Supabase-based collaboration instead
    ]
  }, [
    // Only recreate when these values actually change
    JSON.stringify(citations || []),
    Boolean(editable),
    JSON.stringify(processedFootnotes || []),
    showAI,
    aiProvider,
    user.id,
    user.name
  ])

  // TipTap Cloud provider setup removed - using Supabase-based collaboration

  // Remove the debouncedSave function - we'll use only the Supabase auto-save hook
  // to prevent duplicate save mechanisms

  // Determine initial content once and memoize it
  const initialEditorContent = useMemo(() => {
    if (initialData) {
      return initialData
    }
    if (processedContent) {
      return processedContent
    }
    if (initialContent) {
      return initialContent
    }
    return ''
  }, [initialData, processedContent, initialContent])

  const editor = useEditor({
    immediatelyRender: true,
    editorProps: {
      attributes: {
        autocomplete: 'off',
        autocorrect: 'off',
        autocapitalize: 'off',
        'aria-label': 'Document editor',
        'role': 'textbox',
        'aria-multiline': 'true',
        'contenteditable': 'true',
        class: cn(
          'prose prose-slate dark:prose-invert max-w-none',
          'focus:outline-none min-h-[500px] p-6',
          viewMode && 'print:p-0 print:m-0', // Remove padding in view mode for print
          internalPrintMode && 'eko-print-mode', // Apply print mode styling
          className
        ),
      },
    },
    extensions,
    content: initialEditorContent,
    editable: editable && !viewMode, // Disable editing in view mode
    parseOptions: {
      preserveWhitespace: 'full',
    },
    onUpdate: ({ editor, transaction }) => {
      // Only handle accessibility announcements here
      // Auto-save is handled by the useSupabaseAutoSave hook to prevent duplicate saves
      if (transaction.docChanged) {
        const { from, to } = transaction.selection
        const selectedText = editor.state.doc.textBetween(from, to)

        // Check for formatting changes and announce them
        if (editor.isActive('bold') && selectedText) {
          announce(EditorAnnouncements.formatApplied('Bold'), 'polite')
        }
        if (editor.isActive('italic') && selectedText) {
          announce(EditorAnnouncements.formatApplied('Italic'), 'polite')
        }
        if (editor.isActive('underline') && selectedText) {
          announce(EditorAnnouncements.formatApplied('Underline'), 'polite')
        }
      }
    },
    onFocus: () => {
      announce(EditorAnnouncements.focusEntered(), 'polite')
    },
    onBlur: () => {
      announce(EditorAnnouncements.focusExited(), 'polite')
    },
    onCreate: ({ editor }) => {
      // Call the onEditorCreate callback if provided
      if (onEditorCreate) {
        onEditorCreate(editor)
      }
    },
  }) // Removed dependency array to fix BubbleMenu DOM insertion error

  // Register initial citations and set up editor
  useEffect(() => {
    if (!editor) return

    // Reset timeout flag when editor loads successfully
    setEditorLoadTimeout(false)

    // Update editable state
    editor.setEditable(editable && !viewMode)

    // Editor setup is now handled by DocumentProvider
  }, [editor, editable, viewMode, citations])

  // Handle extensions updates
  useEffect(() => {
    if (!editor) return

    // Only update extensions if they've actually changed
    const currentExtensions = editor.extensionManager.extensions.map(ext => ext.name).sort()
    const newExtensions = extensions.map(ext => ext.name).sort()

    if (JSON.stringify(currentExtensions) !== JSON.stringify(newExtensions)) {
      // Extensions have changed, we need to recreate the editor
      // For now, we'll log this case - in practice, extensions shouldn't change often
      console.warn('Extensions changed after editor creation. Consider moving extension creation outside of component.')
    }
  }, [editor, extensions])

  // Cleanup effect to properly destroy editor
  useEffect(() => {
    return () => {
      if (editor && !editor.isDestroyed) {
        try {
          editor.destroy()
        } catch (error) {
          console.error('Error destroying editor:', error)
        }
      }
    }
  }, [editor])

  // Expose editor instance to window for testing in development/test environments
  useEffect(() => {
    if (editor && (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test')) {
      (window as any).testEditor = editor
    }
    
    return () => {
      if ((window as any).testEditor === editor) {
        delete (window as any).testEditor
      }
    }
  }, [editor])

  // Initialize Supabase auto-save after editor is created
  const autoSave = useSupabaseAutoSave({
    documentId,
    editor: editor || undefined,
    onSave,
    enabled: !viewMode && !!editor,
  })

  // Update editor content when new content becomes available
  const lastContentRef = useRef<string>('')
  const hasSetInitialContent = useRef(false)

  useEffect(() => {
    if (!editor || isProcessingMarkdown || hasSetInitialContent.current) {
      return
    }

    try {
      let contentToSet = null
      let contentKey = ''

      // Priority order: initialData > processedContent > initialContent
      if (initialData) {
        contentToSet = initialData
        contentKey = JSON.stringify(initialData)
      } else if (processedContent && hasProcessedInitialContent) {
        contentToSet = processedContent
        contentKey = processedContent
      } else if (initialContent && !hasProcessedInitialContent && !processedContent) {
        contentToSet = initialContent
        contentKey = initialContent
      }

      // Only update if we have content and it's different from what we last set
      if (contentToSet && lastContentRef.current !== contentKey) {
        // Validate content before setting
        if (typeof contentToSet === 'string' && contentToSet.trim() === '') {
          // For empty content, set a minimal valid document structure
          editor.commands.setContent('<p></p>', false)
        } else {
          editor.commands.setContent(contentToSet, false, {
            preserveWhitespace: 'full',
          })
        }
        lastContentRef.current = contentKey
        hasSetInitialContent.current = true

        // Announce content loaded for accessibility
        announce(EditorAnnouncements.contentLoaded(), 'polite')
      }
    } catch (error) {
      console.error('Error setting editor content:', error)
      hasSetInitialContent.current = true // Prevent infinite retries
    }
  }, [editor, initialData, processedContent, initialContent, isProcessingMarkdown, hasProcessedInitialContent])

  const handleSave = useCallback(async () => {
    if (autoSave) {
      // Manual saves should create a version in the history
      await autoSave.manualSave(true)
    }
  }, [autoSave])

  // Handle document export
  const handleExport = useCallback(async (format: ExportFormat) => {
    if (!editor) {
      console.error('Editor not available for export')
      return
    }

    try {
      // Create a save function that uses the proper auto-save mechanism
      const saveFunction = async (content: string, data?: any) => {
        if (autoSave) {
          // Use manual save to ensure document is saved before export
          await autoSave.manualSave(true)
        } else if (onSave) {
          // Fallback to the provided onSave function
          await onSave(content, data)
        }
      }

      await exportDocument(editor, format, undefined, documentId, saveFunction)
    } catch (error) {
      console.error('Export failed:', error)
      // You could show a toast notification here
    }
  }, [editor, documentId, autoSave, onSave])

  // Handle adding comments from context menu
  const handleAddComment = useCallback((position: number) => {
    try {
      console.log('Adding comment at position:', position)

      // Use requestAnimationFrame to ensure DOM operations are complete
      requestAnimationFrame(() => {
        try {
          // Open comments panel and focus on the position
          setShowSidePanel(true)
          setActiveSidePanelTab('comments')
          // TODO: Implement comment creation at specific position
        } catch (error) {
          console.error('Error in comment panel state update:', error)
        }
      })
    } catch (error) {
      console.error('Error handling comment addition:', error)
    }
  }, [])

  // Show loading state while editor is initializing or processing content
  if ((!editor && !editorLoadTimeout) || isProcessingMarkdown) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <p className="text-sm text-gray-600">
            {isProcessingMarkdown ? 'Processing content...' : 'Loading editor...'}
          </p>
        </div>
      </div>
    )
  }

  // Show error state if editor failed to load after timeout
  if (!editor && editorLoadTimeout) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-sm text-red-600 mb-4">Editor failed to load. Please refresh the page.</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Refresh Page
          </button>
        </div>
      </div>
    )
  }

  // Validate editor state before rendering
  if (editor && editor.isDestroyed) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-sm text-red-600">Editor error. Please refresh the page.</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-2 px-4 py-2 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Refresh Page
          </button>
        </div>
      </div>
    )
  }

  // In view mode, only show the content
  if (viewMode) {
    return (
      <div className={cn('w-full h-full bg-background', className)}>
        <div className="w-full h-full">
          <div className="max-w-4xl mx-auto">
            <SafeEditorContent editor={editor} />
            {editable && <EditorContextBubbleMenu editor={editor} />}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div data-testid="eko-document-editor" className={cn('flex flex-col h-full bg-background', className)}>
        {/* ARIA Live Region for screen reader announcements */}
        <AriaLiveRegion />

        {/* Offline Indicator */}
        <OfflineIndicator isOffline={isOffline} className="mx-4 mt-2" />

        {/* Document Title Area with Entity and Run Display */}
        {(entityId || runId) && (
          <div className="border-b bg-muted/20 px-6 py-3">
            <DocumentEntityRunDisplay 
              entityId={entityId} 
              runId={runId}
              className="justify-start"
            />
          </div>
        )}

        {showToolbar && (
          <>
            <EditorToolbar
              editor={editor}
              isSaving={autoSave.isSaving}
              lastSaved={autoSave.lastSaved}
              onSave={handleSave}
              onExport={handleExport}
              printMode={internalPrintMode}
              onTogglePrintMode={() => setInternalPrintMode(!internalPrintMode)}
              documentId={documentId}
              onOpenHistory={() => {
                setShowSidePanel(true)
                setActiveSidePanelTab('history')
              }}
            />
            {showCollaboration && (
              <SupabaseCollaborationToolbar
                editor={editor}
                documentId={documentId}
                currentUser={user}
                showComments={showSidePanel && activeSidePanelTab === 'comments'}
                showHistory={showSidePanel && activeSidePanelTab === 'history'}
                onToggleComments={() => {
                  if (showSidePanel && activeSidePanelTab === 'comments') {
                    setShowSidePanel(false)
                  } else {
                    setShowSidePanel(true)
                    setActiveSidePanelTab('comments')
                  }
                }}
                onToggleHistory={() => {
                  if (showSidePanel && activeSidePanelTab === 'history') {
                    setShowSidePanel(false)
                  } else {
                    setShowSidePanel(true)
                    setActiveSidePanelTab('history')
                  }
                }}
                onShare={() => {
                  if (showSidePanel && activeSidePanelTab === 'share') {
                    setShowSidePanel(false)
                  } else {
                    setShowSidePanel(true)
                    setActiveSidePanelTab('share')
                  }
                }}
                onSettings={() => {
                  setShowSettingsDialog(true)
                }}
                featureFlags={{
                  comments: featureFlags.comments,
                  share: featureFlags.share,
                }}
              />
            )}
            {showAI && aiProvider && featureFlags.aiTools !== false && (
              <AIToolbar
                editor={editor}
                aiProvider={aiProvider}
                onOpenChat={() => {
                  if (showSidePanel && activeSidePanelTab === 'ai') {
                    setShowSidePanel(false)
                  } else {
                    setShowSidePanel(true)
                    setActiveSidePanelTab('ai')
                  }
                }}
                className="border-t"
              />
            )}
          </>
        )}

        <div className="flex-1 overflow-hidden">
          <div className="h-full overflow-y-auto">
            <div className="max-w-4xl mx-auto">
              <SafeEditorContent editor={editor} />
              <EditorContextBubbleMenu 
                editor={editor} 
                onAddComment={featureFlags.comments !== false ? handleAddComment : undefined} 
              />
              <RightClickContextMenu 
                editor={editor} 
                onAddComment={featureFlags.comments !== false ? handleAddComment : undefined}
                onAISuggestion={showAI && featureFlags.aiTools !== false ? () => {
                  // Open AI sidebar if AI is enabled
                  setShowSidePanel(true)
                  setActiveSidePanelTab('ai')
                } : undefined}
              />
            </div>
          </div>
        </div>

        {/* Tabbed Side Panel */}
        {showSidePanel && (
          <div className="absolute right-0 top-0 h-full z-40">
            <TabbedSidePanel
              editor={editor}
              documentId={documentId}
              currentUser={user}
              isOwner={true} // TODO: Determine actual ownership
              aiProvider={showAI && featureFlags.aiChat !== false ? aiProvider : undefined}
              defaultTab={activeSidePanelTab}
              onClose={() => setShowSidePanel(false)}
              onRestoreVersion={(version: any) => {
                // Handle version restore - set content and save
                console.log('Restored to version:', version.versionNumber)
                
                // Set the restored content in the editor
                if (version.data) {
                  editor.commands.setContent(version.data, true)
                } else if (version.content) {
                  editor.commands.setContent(version.content, true)
                }
                
                // Save the restored content
                if (onSave) {
                  const content = editor.getHTML()
                  const data = editor.getJSON()
                  onSave(content, data)
                }
              }}
              featureFlags={{
                comments: featureFlags.comments,
                share: featureFlags.share,
                aiChat: featureFlags.aiChat,
              }}
            />
          </div>
        )}

        {/* Collaboration Settings Dialog */}
        <CollaborationSettingsDialog
          open={showSettingsDialog}
          onOpenChange={setShowSettingsDialog}
          documentId={documentId}
          currentUser={user}
          isOwner={true} // TODO: Determine actual ownership
        />
    </div>
  )
}

// Main export component - now expects to be wrapped in DocumentProvider
export function EkoDocumentEditor(props: EkoDocumentEditorProps) {
  return <EkoDocumentEditorInternal {...props} />
}
