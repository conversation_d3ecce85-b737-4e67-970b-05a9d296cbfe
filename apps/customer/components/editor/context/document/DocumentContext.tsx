import React, { create<PERSON>ontext, useContext, useReducer, useCallback, useMemo, useRef } from 'react'
import { Editor } from '@tiptap/react'
import { CitationType } from '@/components/citation'
import { DocumentState, DocumentOperations, EntityChange } from '../../types'
import { reportService, logger, debounce, TIMEOUT_DELAYS } from '../../services'
import type { DocumentStatus, EntityChangeStatus } from '../../services'

interface DocumentContextState {
  // Editor state
  editor: Editor | null
  content: string
  data: any
  isDirty: boolean

  // Document state
  reportId: string | null
  status: DocumentStatus
  lastSaved: Date | null
  hasUnsavedChanges: boolean
  error: string | null

  // Citations
  citations: CitationType[]

  // UI state
  showSidePanel: boolean
  activePanelTab: 'comments' | 'history' | 'share' | 'ai'

  // Entity tracking
  currentEntity: string | null
  currentRun: string | null
  entityChanges: Map<string, EntityChange>

  // Document initialization
  isInitialized: boolean
  hasTriggeredInitialLoad: boolean
}

type DocumentContextAction =
  | { type: 'SET_EDITOR'; payload: Editor }
  | { type: 'SET_CONTENT'; payload: { content: string; data: any; source: 'user' | 'system' | 'restore' } }
  | { type: 'SET_REPORT_ID'; payload: string | null }
  | { type: 'SET_STATUS'; payload: DocumentStatus }
  | { type: 'SET_UNSAVED_CHANGES'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_LAST_SAVED'; payload: Date }
  | { type: 'ADD_CITATIONS'; payload: CitationType[] }
  | { type: 'ADD_CITATION'; payload: CitationType }
  | { type: 'SET_SIDE_PANEL'; payload: { show?: boolean; tab?: DocumentContextState['activePanelTab'] } }
  | { type: 'SET_ENTITY'; payload: { entity: string | null; run: string } }
  | { type: 'ADD_ENTITY_CHANGE'; payload: EntityChange }
  | { type: 'CLEAR_ENTITY_CHANGES' }
  | { type: 'SET_INITIALIZED'; payload: boolean }
  | { type: 'SET_INITIAL_LOAD_TRIGGERED'; payload: boolean }
  | { type: 'RESET_DOCUMENT' }

const initialState: DocumentContextState = {
  editor: null,
  content: '',
  data: null,
  isDirty: false,
  reportId: null,
  status: 'idle',
  lastSaved: null,
  hasUnsavedChanges: false,
  error: null,
  citations: [],
  showSidePanel: false,
  activePanelTab: 'comments',
  currentEntity: null,
  currentRun: null,
  entityChanges: new Map(),
  isInitialized: false,
  hasTriggeredInitialLoad: false
}

function documentReducer(state: DocumentContextState, action: DocumentContextAction): DocumentContextState {
  switch (action.type) {
    case 'SET_EDITOR':
      return { ...state, editor: action.payload }
    
    case 'SET_CONTENT':
      return {
        ...state,
        content: action.payload.content,
        data: action.payload.data,
        isDirty: action.payload.source === 'user',
        hasUnsavedChanges: action.payload.source === 'user' ? true : state.hasUnsavedChanges
      }
    
    case 'SET_REPORT_ID':
      return { ...state, reportId: action.payload }
    
    case 'SET_STATUS':
      return { ...state, status: action.payload }
    
    case 'SET_UNSAVED_CHANGES':
      return { ...state, hasUnsavedChanges: action.payload }
    
    case 'SET_ERROR':
      return { ...state, error: action.payload }
    
    case 'SET_LAST_SAVED':
      return { 
        ...state, 
        lastSaved: action.payload,
        isDirty: false,
        hasUnsavedChanges: false,
        error: null
      }
    
    case 'ADD_CITATIONS':
      return { ...state, citations: action.payload }
    
    case 'ADD_CITATION':
      return { 
        ...state, 
        citations: [...state.citations, action.payload]
      }
    
    case 'SET_SIDE_PANEL':
      return {
        ...state,
        showSidePanel: action.payload.show ?? state.showSidePanel,
        activePanelTab: action.payload.tab ?? state.activePanelTab
      }
    
    case 'SET_ENTITY':
      return {
        ...state,
        currentEntity: action.payload.entity,
        currentRun: action.payload.run
      }
    
    case 'ADD_ENTITY_CHANGE':
      const newEntityChanges = new Map(state.entityChanges)
      newEntityChanges.set(action.payload.id, action.payload)
      return { ...state, entityChanges: newEntityChanges }
    
    case 'CLEAR_ENTITY_CHANGES':
      return { ...state, entityChanges: new Map() }
    
    case 'SET_INITIALIZED':
      return { ...state, isInitialized: action.payload }
    
    case 'SET_INITIAL_LOAD_TRIGGERED':
      return { ...state, hasTriggeredInitialLoad: action.payload }
    
    case 'RESET_DOCUMENT':
      return { ...initialState, editor: state.editor }
    
    default:
      return state
  }
}

interface DocumentContextValue extends DocumentOperations {
  // State
  editor: Editor | null
  content: string
  data: any
  isDirty: boolean
  reportId: string | null
  status: DocumentStatus
  lastSaved: Date | null
  hasUnsavedChanges: boolean
  error: string | null
  citations: CitationType[]
  showSidePanel: boolean
  activePanelTab: 'comments' | 'history' | 'share' | 'ai'
  currentEntity: string | null
  currentRun: string | null
  entityChanges: Map<string, EntityChange>
  isInitialized: boolean
  hasTriggeredInitialLoad: boolean

  // Actions
  setEditor: (editor: Editor) => void
  setContent: (content: string, data: any, source?: 'user' | 'system' | 'restore') => void
  addCitations: (citations: CitationType[]) => void
  addCitation: (citation: CitationType) => void
  setSidePanel: (show?: boolean, tab?: DocumentContextState['activePanelTab']) => void
  setEntity: (entity: string | null, run: string) => void
  addEntityChange: (change: EntityChange) => void
  clearEntityChanges: () => void
  setInitialized: (initialized: boolean) => void
  setInitialLoadTriggered: (triggered: boolean) => void
}

const DocumentContext = createContext<DocumentContextValue | undefined>(undefined)

interface DocumentProviderProps {
  children: React.ReactNode
  reportId?: string
  onSave?: (content: string, data?: any) => Promise<void> | void
}

export function DocumentProvider({ children, reportId: initialReportId, onSave }: DocumentProviderProps) {
  const [state, dispatch] = useReducer(documentReducer, {
    ...initialState,
    reportId: initialReportId || null
  })

  // Debounced auto-save
  const debouncedSave = useRef(
    debounce(async () => {
      if (state.hasUnsavedChanges && state.reportId && state.editor) {
        await saveDocument()
      }
    }, TIMEOUT_DELAYS.SLOW)
  )

  // Auto-save when content changes
  React.useEffect(() => {
    if (state.hasUnsavedChanges) {
      debouncedSave.current()
    }
  }, [state.hasUnsavedChanges, state.content])

  const loadDocument = useCallback(async (reportId: string) => {
    if (!reportId) {
      logger.warn('DocumentProvider', 'loadDocument', 'No reportId provided')
      return
    }

    dispatch({ type: 'SET_STATUS', payload: 'loading' })
    dispatch({ type: 'SET_REPORT_ID', payload: reportId })

    try {
      logger.info('DocumentProvider', 'loadDocument', `Loading document ${reportId}`)
      
      const document = await reportService.loadReport(reportId)
      
      if (document) {
        dispatch({ type: 'SET_CONTENT', payload: { 
          content: document.content, 
          data: document.data, 
          source: 'system' 
        } })
        
        // Update editor content if editor is available
        if (state.editor) {
          state.editor.commands.setContent(document.content)
        }
        
        dispatch({ type: 'SET_STATUS', payload: 'loaded' })
        logger.info('DocumentProvider', 'loadDocument', `Successfully loaded document ${reportId}`)
      } else {
        dispatch({ type: 'SET_ERROR', payload: 'Document not found' })
        dispatch({ type: 'SET_STATUS', payload: 'error' })
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load document'
      logger.error('DocumentProvider', 'loadDocument', `Error loading document ${reportId}`, error as Error)
      dispatch({ type: 'SET_ERROR', payload: errorMessage })
      dispatch({ type: 'SET_STATUS', payload: 'error' })
    }
  }, [state.editor])

  const saveDocument = useCallback(async () => {
    if (!state.reportId || !state.editor) {
      logger.warn('DocumentProvider', 'saveDocument', 'Cannot save: missing reportId or editor')
      return
    }

    dispatch({ type: 'SET_STATUS', payload: 'saving' })

    try {
      logger.info('DocumentProvider', 'saveDocument', `Saving document ${state.reportId}`)
      
      const content = state.editor.getHTML()
      const data = state.editor.getJSON()

      // Use custom onSave if provided, otherwise use reportService
      if (onSave) {
        await onSave(content, data)
      } else {
        await reportService.saveReport(state.reportId, content, data)
      }

      dispatch({ type: 'SET_LAST_SAVED', payload: new Date() })
      dispatch({ type: 'SET_STATUS', payload: 'loaded' })
      
      logger.info('DocumentProvider', 'saveDocument', `Successfully saved document ${state.reportId}`)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to save document'
      logger.error('DocumentProvider', 'saveDocument', `Error saving document ${state.reportId}`, error as Error)
      dispatch({ type: 'SET_ERROR', payload: errorMessage })
      dispatch({ type: 'SET_STATUS', payload: 'error' })
      throw error
    }
  }, [state.reportId, state.editor, onSave])

  const resetDocument = useCallback(() => {
    logger.info('DocumentProvider', 'resetDocument', 'Resetting document state')
    dispatch({ type: 'RESET_DOCUMENT' })
  }, [])

  const setUnsavedChanges = useCallback((hasChanges: boolean) => {
    dispatch({ type: 'SET_UNSAVED_CHANGES', payload: hasChanges })
  }, [])

  const setEditor = useCallback((editor: Editor) => {
    dispatch({ type: 'SET_EDITOR', payload: editor })
  }, [])

  const setContent = useCallback((content: string, data: any, source: 'user' | 'system' | 'restore' = 'user') => {
    dispatch({ type: 'SET_CONTENT', payload: { content, data, source } })
  }, [])

  const addCitations = useCallback((citations: CitationType[]) => {
    dispatch({ type: 'ADD_CITATIONS', payload: citations })
  }, [])

  const addCitation = useCallback((citation: CitationType) => {
    dispatch({ type: 'ADD_CITATION', payload: citation })
  }, [])

  const setSidePanel = useCallback((show?: boolean, tab?: DocumentContextState['activePanelTab']) => {
    dispatch({ type: 'SET_SIDE_PANEL', payload: { show, tab } })
  }, [])

  const setEntity = useCallback((entity: string | null, run: string) => {
    dispatch({ type: 'SET_ENTITY', payload: { entity, run } })
  }, [])

  const addEntityChange = useCallback((change: EntityChange) => {
    dispatch({ type: 'ADD_ENTITY_CHANGE', payload: change })
  }, [])

  const clearEntityChanges = useCallback(() => {
    dispatch({ type: 'CLEAR_ENTITY_CHANGES' })
  }, [])

  const setInitialized = useCallback((initialized: boolean) => {
    dispatch({ type: 'SET_INITIALIZED', payload: initialized })
  }, [])

  const setInitialLoadTriggered = useCallback((triggered: boolean) => {
    dispatch({ type: 'SET_INITIAL_LOAD_TRIGGERED', payload: triggered })
  }, [])

  const contextValue = useMemo((): DocumentContextValue => ({
    // State
    editor: state.editor,
    content: state.content,
    data: state.data,
    isDirty: state.isDirty,
    reportId: state.reportId,
    status: state.status,
    lastSaved: state.lastSaved,
    hasUnsavedChanges: state.hasUnsavedChanges,
    error: state.error,
    citations: state.citations,
    showSidePanel: state.showSidePanel,
    activePanelTab: state.activePanelTab,
    currentEntity: state.currentEntity,
    currentRun: state.currentRun,
    entityChanges: state.entityChanges,
    isInitialized: state.isInitialized,
    hasTriggeredInitialLoad: state.hasTriggeredInitialLoad,

    // Operations
    loadDocument,
    saveDocument,
    resetDocument,
    setUnsavedChanges,
    setEditor,
    setContent,
    addCitations,
    addCitation,
    setSidePanel,
    setEntity,
    addEntityChange,
    clearEntityChanges,
    setInitialized,
    setInitialLoadTriggered
  }), [
    state.editor,
    state.content,
    state.data,
    state.isDirty,
    state.reportId,
    state.status,
    state.lastSaved,
    state.hasUnsavedChanges,
    state.error,
    state.citations,
    state.showSidePanel,
    state.activePanelTab,
    state.currentEntity,
    state.currentRun,
    state.entityChanges,
    state.isInitialized,
    state.hasTriggeredInitialLoad,
    loadDocument,
    saveDocument,
    resetDocument,
    setUnsavedChanges,
    setEditor,
    setContent,
    addCitations,
    addCitation,
    setSidePanel,
    setEntity,
    addEntityChange,
    clearEntityChanges,
    setInitialized,
    setInitialLoadTriggered
  ])

  return (
    <DocumentContext.Provider value={contextValue}>
      {children}
    </DocumentContext.Provider>
  )
}

export function useDocument(): DocumentContextValue {
  const context = useContext(DocumentContext)
  if (!context) {
    throw new Error('useDocument must be used within a DocumentProvider')
  }
  return context
}