import React, { createContext, useContext, useReducer, useRef } from 'react'
import { Editor } from '@tiptap/react'
import { CitationType } from '@/components/citation'
import { useGroupStatusManager } from './hooks/useGroupStatusManager'
import { useComponentRegistration } from './hooks/useComponentRegistration'
import { useDocumentVersioning } from './hooks/useDocumentVersioning'
// Legacy useDependencyManager removed - using dependencyService directly
import { useDocumentInitialization } from './hooks/useDocumentInitialization'
import { useComponentUpdater } from './hooks/useComponentUpdater'
import { renderLog } from './utils/renderLogger'
import type { ReportComponent } from '../types'

// Re-export for backward compatibility
export type { ReportComponent }

export interface DocumentState {
  // Editor state
  editor: Editor | null
  content: string
  data: any
  isDirty: boolean

  // Auto-save state
  isSaving: boolean
  lastSaved: Date | null
  saveError: string | null

  // Report state
  components: Map<string, ReportComponent>
  citations: CitationType[]

  // UI state
  showSidePanel: boolean
  activePanelTab: 'comments' | 'history' | 'share' | 'ai'

  // Entity/Run tracking for report sections
  currentEntity: string | null
  currentRun: string | null

  // Document initialization state (EKO-118)
  isInitialized: boolean
  hasTriggeredInitialLoad: boolean
}

export type DocumentAction =
  | { type: 'EDITOR_CREATED'; editor: Editor }
  | { type: 'CONTENT_CHANGED'; content: string; data: any; source: 'user' | 'system' | 'restore' }
  | { type: 'SAVE_STARTED' }
  | { type: 'SAVE_COMPLETED'; timestamp: Date }
  | { type: 'SAVE_FAILED'; error: string }
  | { type: 'COMPONENT_REGISTERED'; component: ReportComponent }
  | { type: 'COMPONENT_UPDATED'; id: string; updates: Partial<ReportComponent> }
  | { type: 'COMPONENT_REMOVED'; id: string }
  | { type: 'CITATIONS_REGISTERED'; citations: CitationType[] }
  | { type: 'CITATION_ADDED'; citation: CitationType }
  | { type: 'UI_PANEL_TOGGLE'; show?: boolean; tab?: DocumentState['activePanelTab'] }
  | { type: 'ENTITY_CHANGED'; entity: string | null; run: string }
  | { type: 'RESET_DIRTY_STATE' }
  | { type: 'DOCUMENT_INITIALIZED' }
  | { type: 'INITIAL_LOAD_TRIGGERED' }
  | { type: 'CHILD_STATE_CHANGED'; parentId: string; childState: ReportComponent; allChildrenStates: ReportComponent[] }
  | { type: 'DEPENDENCY_STATE_CHANGED'; dependentId: string; dependencyState: ReportComponent; allDependencyStates: ReportComponent[] }

const initialState: DocumentState = {
  editor: null,
  content: '',
  data: null,
  isDirty: false,
  isSaving: false,
  lastSaved: null,
  saveError: null,
  components: new Map(),
  citations: [],
  showSidePanel: false,
  activePanelTab: 'comments',
  currentEntity: null,
  currentRun: null,
  isInitialized: false,
  hasTriggeredInitialLoad: false,
}

function documentReducer(state: DocumentState, action: DocumentAction): DocumentState {
  console.log(`[REDUCER] Action: ${action.type}`, action)
  
  switch (action.type) {
    case 'EDITOR_CREATED':
      return {
        ...state,
        editor: action.editor,
      }

    case 'CONTENT_CHANGED': {
      // Mark as dirty if content actually changed, regardless of source
      const contentChanged =
        action.content !== state.content || JSON.stringify(action.data) !== JSON.stringify(state.data)
      const shouldMarkDirty = contentChanged && (action.source === 'user' || action.source === 'system')

      return {
        ...state,
        content: action.content,
        data: action.data,
        isDirty: shouldMarkDirty || state.isDirty,
        saveError: null, // Clear any previous save errors
      }
    }

    case 'SAVE_STARTED':
      return {
        ...state,
        isSaving: true,
        saveError: null,
      }

    case 'SAVE_COMPLETED':
      return {
        ...state,
        isSaving: false,
        lastSaved: action.timestamp,
        isDirty: false,
        saveError: null,
      }

    case 'SAVE_FAILED':
      return {
        ...state,
        isSaving: false,
        saveError: action.error,
      }

    case 'COMPONENT_REGISTERED': {
      const newComponents = new Map(state.components)
      const existing = newComponents.get(action.component.id)

      // Only update if the component has actually changed to prevent unnecessary re-renders
      if (!existing || JSON.stringify(existing) !== JSON.stringify(action.component)) {
        newComponents.set(action.component.id, action.component)

        return {
          ...state,
          components: newComponents,
        }
      }

      return state // No change needed
    }

    case 'COMPONENT_UPDATED': {
      const newComponents = new Map(state.components)
      const existing = newComponents.get(action.id)
      if (existing) {
        newComponents.set(action.id, { ...existing, ...action.updates })
      }

      return {
        ...state,
        components: newComponents,
      }
    }

    case 'COMPONENT_REMOVED': {
      const newComponents = new Map(state.components)
      newComponents.delete(action.id)

      return {
        ...state,
        components: newComponents,
      }
    }

    case 'CITATIONS_REGISTERED':
      return {
        ...state,
        citations: [
          ...state.citations,
          ...action.citations.filter(
            (newCit) => !state.citations.some((existing) => existing.doc_page_id === newCit.doc_page_id)
          ),
        ],
      }

    case 'CITATION_ADDED':
      if (state.citations.some((cit) => cit.doc_page_id === action.citation.doc_page_id)) {
        return state // Citation already exists
      }
      return {
        ...state,
        citations: [...state.citations, action.citation],
      }

    case 'UI_PANEL_TOGGLE':
      return {
        ...state,
        showSidePanel: action.show ?? !state.showSidePanel,
        activePanelTab: action.tab ?? state.activePanelTab,
      }

    case 'ENTITY_CHANGED':
      return {
        ...state,
        currentEntity: action.entity,
        currentRun: action.run,
      }

    case 'RESET_DIRTY_STATE':
      return {
        ...state,
        isDirty: false,
      }

    case 'DOCUMENT_INITIALIZED':
      return {
        ...state,
        isInitialized: true,
      }

    case 'INITIAL_LOAD_TRIGGERED':
      return {
        ...state,
        hasTriggeredInitialLoad: true,
      }

    case 'CHILD_STATE_CHANGED': {
      // Handle child state change notification for a parent group
      const parent = state.components.get(action.parentId)
      if (!parent || parent.type !== 'report-group') {
        return state
      }

      // Calculate new status based on all children states
      const hasError = action.allChildrenStates.some(child => child.status === 'error')
      const hasIdleOrLoading = action.allChildrenStates.some(
        child => child.status === 'idle' || child.status === 'loading'
      )
      const allLoaded = action.allChildrenStates.every(
        child => child.status === 'loaded' || child.status === 'preserved' || child.status === 'locked'
      )

      let newStatus: ReportComponent['status']
      if (hasIdleOrLoading) {
        newStatus = 'loading'
      } else if (hasError) {
        newStatus = 'error'
      } else if (allLoaded) {
        newStatus = 'loaded'
      } else {
        newStatus = 'loading'
      }

      // Only update if status changed
      if (parent.status === newStatus) {
        return state
      }

      const newComponents = new Map(state.components)
      newComponents.set(action.parentId, { ...parent, status: newStatus })

      return {
        ...state,
        components: newComponents,
      }
    }

    case 'DEPENDENCY_STATE_CHANGED': {
      // Handle dependency state change notification
      const dependent = state.components.get(action.dependentId)
      if (!dependent || dependent.type !== 'report-summary') {
        return state
      }

      // Re-evaluate dependent status based on all dependency states
      const hasError = action.allDependencyStates.some(dep => dep.status === 'error')
      const hasIdleOrLoading = action.allDependencyStates.some(
        dep => dep.status === 'idle' || dep.status === 'loading'
      )
      const allLoaded = action.allDependencyStates.every(
        dep => dep.status === 'loaded' || dep.status === 'preserved' || dep.status === 'locked'
      )

      // Don't change status if already loaded/error or if dependencies aren't ready
      if (dependent.status === 'loaded' || dependent.status === 'error' || dependent.status === 'locked' || dependent.status === 'preserved') {
        return state
      }

      let shouldTriggerLoad = false
      let newStatus: ReportComponent['status'] | undefined

      if (hasError) {
        newStatus = 'error'
      } else if (hasIdleOrLoading) {
        newStatus = 'loading'
      } else if (allLoaded && dependent.status !== 'loading') {
        // All dependencies are loaded and we're not already loading - trigger generation
        newStatus = 'loading'
        shouldTriggerLoad = true
      }

      if (!newStatus || dependent.status === newStatus) {
        return state
      }

      const newComponents = new Map(state.components)
      newComponents.set(action.dependentId, { 
        ...dependent, 
        status: newStatus,
        error: hasError ? 'One or more dependencies have errors' : undefined
      })

      return {
        ...state,
        components: newComponents,
      }
    }

    default:
      return state
  }
}

interface DocumentContextValue {
  state: DocumentState
  dispatch: React.Dispatch<DocumentAction>

  // Convenience methods
  registerComponent: (component: ReportComponent) => void
  updateComponent: (id: string, updates: Partial<ReportComponent>) => void
  removeComponent: (id: string) => void
  registerCitations: (citations: CitationType[]) => void
  addCitation: (citation: CitationType) => void
  togglePanel: (show?: boolean, tab?: DocumentState['activePanelTab']) => void
  setEntityRun: (entity: string | null, run: string) => void

  // Dependency management
  areDependenciesReady: (componentId: string) => boolean
  waitForDependencies: (componentId: string) => Promise<void>

  // Entity change listeners
  addEntityChangeListener: (listener: (entity: string | null, run: string) => void) => () => void
  notifyEntityChange: (entity: string | null, run: string) => void

  // Group status management
  updateGroupStatus: (groupId: string, immediate?: boolean) => void
  updateAllGroupStatuses: () => void

  // Document versioning and saving
  createDocumentVersion: (changeSummary: string) => Promise<void>
  triggerImmediateSave: () => Promise<void>
  triggerAutoSaveVersion: () => Promise<void>

  // Refresh functionality
  refreshAllDescendants: (groupId: string) => void

  // Document initialization (EKO-118)
  triggerInitialLoad: () => void

  // Check if all components are loaded
  areAllComponentsLoaded: () => boolean
}

const DocumentContext = createContext<DocumentContextValue | null>(null)

export const useDocumentContext = () => {
  const context = useContext(DocumentContext)
  if (!context) {
    throw new Error('useDocumentContext must be used within a DocumentProvider')
  }
  return context
}

interface DocumentProviderProps {
  children: React.ReactNode
  documentId: string
  onSave?: (content: string, data?: any) => Promise<void> | void
  initialEntity?: string | null
  initialRun?: string
}

export const DocumentProvider: React.FC<DocumentProviderProps> = ({
  children,
  documentId,
  onSave,
  initialEntity,
  initialRun = 'latest',
}) => {
  // Add render tracking
  const renderCountRef = useRef(0)
  renderCountRef.current += 1
  renderLog(`[RENDER] DocumentProvider render #${renderCountRef.current}`)
  renderLog(`[RENDER] Props: documentId=${documentId}, initialEntity=${initialEntity}, initialRun=${initialRun}`)
  
  const [state, dispatch] = useReducer(documentReducer, initialState)
  renderLog(`[RENDER] State: currentEntity=${state.currentEntity}, currentRun=${state.currentRun}, components=${state.components.size}`)
  
  // Check if state object reference is changing
  const lastStateRef = useRef(state)
  if (lastStateRef.current !== state) {
    renderLog(`[RENDER] State object reference changed! This triggers context recalc`)
    renderLog(`[RENDER] State change details:`, {
      editorChanged: lastStateRef.current.editor !== state.editor,
      contentChanged: lastStateRef.current.content !== state.content,
      componentsChanged: lastStateRef.current.components !== state.components,
      entityChanged: lastStateRef.current.currentEntity !== state.currentEntity,
      runChanged: lastStateRef.current.currentRun !== state.currentRun
    })
    lastStateRef.current = state
  }

  // Entity change listeners
  const entityChangeListenersRef = useRef<Set<(entity: string | null, run: string) => void>>(new Set())

  // Keep a ref to the current state to avoid stale closures in debounced functions
  const stateRef = useRef(state)
  stateRef.current = state

  // Initialize hooks for different functionality areas
  renderLog(`[RENDER] Initializing hooks`)
  const groupStatusManager = useGroupStatusManager({ dispatch, stateRef })
  renderLog(`[RENDER] GroupStatusManager initialized, updateGroupStatus ref:`, groupStatusManager.updateGroupStatus.toString().slice(0, 50))
  // Dependency management now handled by dependencyService in the new hooks
  const documentVersioning = useDocumentVersioning({ editor: state.editor, documentId, dispatch })
  renderLog(`[RENDER] DocumentVersioning initialized, triggerAutoSaveVersion ref:`, documentVersioning.triggerAutoSaveVersion.toString().slice(0, 50))
  const documentInitialization = useDocumentInitialization({
    editor: state.editor,
    hasTriggeredInitialLoad: state.hasTriggeredInitialLoad,
    isInitialized: state.isInitialized,
    dispatch,
    stateRef
  })
  renderLog(`[RENDER] DocumentInitialization initialized`)

  const componentUpdater = useComponentUpdater({
    documentId,
    components: state.components,
    onSave,
    onComponentChange: (componentId: string, component: ReportComponent) => {
      dispatch({ type: 'COMPONENT_UPDATED', id: componentId, updates: { 
        status: component.status,
        data: component.data,
        error: component.error
      } })
    }
  })
  renderLog(`[RENDER] ComponentUpdater initialized, updateComponent ref:`, componentUpdater.updateComponent.toString().slice(0, 50))

  const componentRegistration = useComponentRegistration({
    dispatch,
    stateRef,
    updateGroupStatus: groupStatusManager.updateGroupStatus
  })
  renderLog(`[RENDER] ComponentRegistration initialized, registerComponent ref:`, componentRegistration.registerComponent.toString().slice(0, 50))

  // Initialize entity/run from props
  React.useEffect(() => {
    renderLog(`[EFFECT] Entity initialization check: initialEntity=${initialEntity}, currentEntity=${state.currentEntity}`)
    if (initialEntity && initialEntity !== state.currentEntity) {
      renderLog(`[EFFECT] Setting initial entity/run: ${initialEntity}/${initialRun}`)
      dispatch({ type: 'ENTITY_CHANGED', entity: initialEntity, run: initialRun })
    }
  }, [initialEntity, initialRun, state.currentEntity])

  // Cleanup on unmount
  React.useEffect(() => {
    return () => {
      groupStatusManager.cleanup()
      componentRegistration.cleanup()
      documentInitialization.cleanup()
    }
  }, [groupStatusManager, componentRegistration, documentInitialization])

  // Helper function to get all descendants (recursive) - delegated to group status manager
  const getAllDescendants = React.useCallback(
    (parentId: string): ReportComponent[] => {
      return groupStatusManager.getAllDescendantsFromState(parentId, state.components)
    },
    [state.components, groupStatusManager]
  )

  // Convenience methods - delegate to appropriate hooks with stable references
  const registerComponent = React.useCallback(
    (component: ReportComponent) => componentRegistration.registerComponent(component),
    [componentRegistration.registerComponent]
  )

  // Document versioning methods are handled by the useDocumentVersioning hook
  const triggerAutoSaveVersion = React.useCallback(
    () => documentVersioning.triggerAutoSaveVersion(),
    [documentVersioning.triggerAutoSaveVersion]
  )

  // Component updating is handled by the useComponentUpdater hook
  const updateComponent = React.useCallback(
    (id: string, updates: Partial<ReportComponent>) => componentUpdater.updateComponent(id, updates),
    [componentUpdater.updateComponent]
  )

  const removeComponent = React.useCallback(
    (id: string) => {
      dispatch({ type: 'COMPONENT_REMOVED', id })
    },
    [dispatch]
  )

  const registerCitations = React.useCallback(
    (citations: CitationType[]) => {
      dispatch({ type: 'CITATIONS_REGISTERED', citations })
    },
    [dispatch]
  )

  const addCitation = React.useCallback(
    (citation: CitationType) => {
      dispatch({ type: 'CITATION_ADDED', citation })
    },
    [dispatch]
  )

  const togglePanel = React.useCallback(
    (show?: boolean, tab?: DocumentState['activePanelTab']) => {
      dispatch({ type: 'UI_PANEL_TOGGLE', show, tab })
    },
    [dispatch]
  )

  const setEntityRun = React.useCallback(
    (entity: string | null, run: string) => {
      dispatch({ type: 'ENTITY_CHANGED', entity, run })
      // Notify entity change listeners
      entityChangeListenersRef.current.forEach((listener) => {
        try {
          listener(entity, run)
        } catch (error) {
          console.error('Error in entity change listener:', error)
        }
      })
    },
    [dispatch]
  )

  // Dependency management now handled by the new dependencyService in focused hooks
  const areDependenciesReady = React.useCallback(
    (componentId: string) => {
      // This is now handled by the useComponentRelations hook
      return true // Simplified for now - dependency logic moved to service layer
    },
    []
  )
  const waitForDependencies = React.useCallback(
    async (componentId: string) => {
      // This is now handled by the useComponentRelations hook
      return Promise.resolve()
    },
    []
  )

  // Entity change listeners
  const addEntityChangeListener = React.useCallback(
    (listener: (entity: string | null, run: string) => void) => {
      entityChangeListenersRef.current.add(listener)
      return () => entityChangeListenersRef.current.delete(listener)
    },
    []
  )

  const notifyEntityChange = React.useCallback(
    (entity: string | null, run: string) => {
      setEntityRun(entity, run)
    },
    [setEntityRun]
  )

  // Group status management is handled by the useGroupStatusManager hook
  const updateGroupStatus = React.useCallback(
    (groupId: string, immediate?: boolean) => groupStatusManager.updateGroupStatus(groupId, immediate),
    [groupStatusManager.updateGroupStatus]
  )
  const updateAllGroupStatuses = React.useCallback(
    () => groupStatusManager.updateAllGroupStatuses(),
    [groupStatusManager.updateAllGroupStatuses]
  )

  // Document versioning methods are handled by the useDocumentVersioning hook
  const createDocumentVersion = React.useCallback(
    (changeSummary: string) => documentVersioning.createDocumentVersion(changeSummary),
    [documentVersioning.createDocumentVersion]
  )
  const triggerImmediateSave = React.useCallback(
    async () => {
      return documentVersioning.triggerImmediateSave(onSave)
    },
    [documentVersioning, onSave]
  )

  // Refresh and initialization methods are handled by their respective hooks
  const refreshAllDescendants = React.useCallback(
    (groupId: string) => groupStatusManager.refreshAllDescendants(groupId),
    [groupStatusManager.refreshAllDescendants]
  )
  const triggerInitialLoad = React.useCallback(
    () => documentInitialization.triggerInitialLoad(),
    [documentInitialization.triggerInitialLoad]
  )

  // Check if all components are loaded
  const areAllComponentsLoaded = React.useCallback((): boolean => {
    const components = Array.from(state.components.values())
    
    // If no components, consider it "loaded"
    if (components.length === 0) return true
    
    // Check if all components are in a final "loaded" state
    const finalStates = ['loaded', 'preserved', 'locked', 'error']
    return components.every(component => finalStates.includes(component.status))
  }, [state.components])

  // Add ref tracking for functions to detect changes
  const lastFunctionsRef = useRef<{[key: string]: string}>({})
  const currentFunctions = {
    registerComponent: registerComponent.toString().slice(0, 50),
    updateComponent: updateComponent.toString().slice(0, 50),
    updateGroupStatus: updateGroupStatus.toString().slice(0, 50),
    triggerAutoSaveVersion: triggerAutoSaveVersion.toString().slice(0, 50)
  }
  
  // Check which functions changed
  Object.keys(currentFunctions).forEach(key => {
    if (lastFunctionsRef.current[key] !== currentFunctions[key as keyof typeof currentFunctions]) {
      renderLog(`[MEMO] Function ${key} changed: 
        OLD: ${lastFunctionsRef.current[key] || 'undefined'}
        NEW: ${currentFunctions[key as keyof typeof currentFunctions]}`)
      lastFunctionsRef.current[key] = currentFunctions[key as keyof typeof currentFunctions]
    }
  })

  // Create a stable context value that doesn't change on every render
  const contextValue: DocumentContextValue = {
    state,
    dispatch,
    registerComponent,
    updateComponent,
    removeComponent,
    registerCitations,
    addCitation,
    togglePanel,
    setEntityRun,
    areDependenciesReady,
    waitForDependencies,
    addEntityChangeListener,
    notifyEntityChange,
    updateGroupStatus,
    updateAllGroupStatuses,
    createDocumentVersion,
    triggerImmediateSave,
    triggerAutoSaveVersion,
    refreshAllDescendants,
    triggerInitialLoad,
    areAllComponentsLoaded,
  }

  return <DocumentContext.Provider value={contextValue}>{children}</DocumentContext.Provider>
}
