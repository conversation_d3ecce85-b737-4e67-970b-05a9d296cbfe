import React, { useCallback, useRef } from 'react'
import { componentService } from '../../services/component/componentService'
import { versioningService } from '../../services/versioning/versioningService'
import { schedulerService } from '../../services/utils/schedulerService'
import { logger } from '../../services/utils/logger'
import type { ReportComponent } from '../../types'

interface ComponentSyncProps {
  documentId: string
  onSave?: (content: string, data?: any) => Promise<void> | void
}

/**
 * Hook for handling component synchronization with database and versioning.
 * Replaces database operations from useComponentUpdater.
 */
export const useComponentSync = ({ documentId, onSave }: ComponentSyncProps) => {
  const pendingSaves = useRef(new Set<string>())

  const saveComponent = useCallback(
    async (component: ReportComponent): Promise<{ success: boolean; error?: string }> => {
      if (pendingSaves.current.has(component.id)) {
        logger.debug('ComponentSync', 'saveComponent', 
          `Save already in progress for component ${component.id}`)
        return { success: false, error: 'Save already in progress' }
      }

      pendingSaves.current.add(component.id)

      try {
        logger.debug('ComponentSync', 'saveComponent', 
          `Saving component ${component.id} to database`)

        const result = await componentService.saveComponent(documentId, component)
        
        if (result.success) {
          logger.info('ComponentSync', 'saveComponent', 
            `Successfully saved component ${component.id}`)
        }

        return result
      } catch (error) {
        logger.error('ComponentSync', 'saveComponent', 
          `Failed to save component ${component.id}`, error as Error)
        return { 
          success: false, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        }
      } finally {
        pendingSaves.current.delete(component.id)
      }
    },
    [documentId]
  )

  const batchSaveComponents = useCallback(
    async (components: ReportComponent[]): Promise<{ success: boolean; error?: string }> => {
      logger.info('ComponentSync', 'batchSaveComponents', 
        `Batch saving ${components.length} components`)

      const updates = components.map(comp => ({ id: comp.id, updates: comp }))
      return await componentService.batchUpdateComponents(documentId, updates)
    },
    [documentId]
  )

  const createDocumentVersion = useCallback(
    async (content: string, data?: any, changeSummary?: string): Promise<void> => {
      if (!content.trim()) {
        logger.debug('ComponentSync', 'createDocumentVersion', 'Skipping version creation for empty content')
        return
      }

      try {
        logger.debug('ComponentSync', 'createDocumentVersion', 
          `Creating document version: ${changeSummary || 'Auto-save'}`)

        const result = await versioningService.createVersion({
          documentId,
          content,
          data,
          changeSummary,
          isAutoSave: !changeSummary, // Manual versions have change summaries
        })

        if (result.error) {
          throw new Error(result.error)
        }

        logger.info('ComponentSync', 'createDocumentVersion', 
          `Created version ${result.version?.versionNumber}`)
      } catch (error) {
        logger.error('ComponentSync', 'createDocumentVersion', 
          'Failed to create document version', error as Error)
        throw error
      }
    },
    [documentId]
  )

  const triggerSave = useCallback(
    async (content: string, data?: any): Promise<void> => {
      try {
        logger.debug('ComponentSync', 'triggerSave', 'Triggering document save')

        if (onSave) {
          await onSave(content, data)
        }

        // Also create a version for backup
        await createDocumentVersion(content, data, 'Manual save')

        logger.info('ComponentSync', 'triggerSave', 'Document save completed')
      } catch (error) {
        logger.error('ComponentSync', 'triggerSave', 'Document save failed', error as Error)
        throw error
      }
    },
    [onSave, createDocumentVersion]
  )

  const debouncedAutoSave = useCallback(
    schedulerService.debounced(
      async (content: string, data?: any) => {
        try {
          await createDocumentVersion(content, data)
          logger.debug('ComponentSync', 'autoSave', 'Auto-save completed')
        } catch (error) {
          logger.error('ComponentSync', 'autoSave', 'Auto-save failed', error as Error)
        }
      },
      2000, // 2 second debounce
      { context: `ComponentSync-${documentId}` }
    ),
    [documentId, createDocumentVersion]
  )

  const triggerAutoSave = useCallback(
    (content: string, data?: any) => {
      logger.debug('ComponentSync', 'triggerAutoSave', 'Triggering debounced auto-save')
      debouncedAutoSave(content, data)
    },
    [debouncedAutoSave]
  )

  // Cleanup on unmount
  React.useEffect(() => {
    return () => {
      // Clear the debounced function and any pending saves
      debouncedAutoSave.clear()
      schedulerService.clearContext(`ComponentSync-${documentId}`)
    }
  }, [documentId, debouncedAutoSave])

  return {
    saveComponent,
    batchSaveComponents,
    createDocumentVersion,
    triggerSave,
    triggerAutoSave,
  }
}