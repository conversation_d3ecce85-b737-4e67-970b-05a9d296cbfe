import { useCallback } from 'react'
import { useDocument, useVersioning } from '../context/providers/EditorProvider'
import { ReportComponent } from '../types'
import { logger } from '../services'
import type { ComponentStatus } from '../services'

/**
 * Main hook for editor functionality
 * Combines component, document, and versioning operations
 */
export function useEditor() {
  const documentContext = useDocument()
  const versioningContext = useVersioning()

  // Component operations - now handled by DocumentContext
  const registerComponent = useCallback((component: ReportComponent) => {
    logger.info('useEditor', 'registerComponent', `Registering component ${component.id}`)
    documentContext.registerComponent(component)
  }, [documentContext])

  const updateComponent = useCallback((componentId: string, updates: Partial<ReportComponent>) => {
    logger.debug('useEditor', 'updateComponent', `Updating component ${componentId}`)
    documentContext.updateComponent(componentId, updates)
  }, [documentContext])

  const getComponent = useCallback((id: string) => {
    return documentContext.state.components.get(id)
  }, [documentContext])

  const getComponentsByStatus = useCallback((status: ComponentStatus) => {
    return Array.from(documentContext.state.components.values()).filter(c => c.status === status)
  }, [documentContext])

  // Document operations
  const loadDocument = useCallback(async (reportId: string) => {
    logger.info('useEditor', 'loadDocument', `Loading document ${reportId}`)
    // Document loading will be handled by DocumentProvider initialization
    documentContext.setEntityRun(null, 'latest')
  }, [documentContext])

  const saveDocument = useCallback(async () => {
    logger.info('useEditor', 'saveDocument', 'Saving document')
    await documentContext.triggerImmediateSave()
  }, [documentContext])

  const setContent = useCallback((content: string, data: any, source?: 'user' | 'system' | 'restore') => {
    documentContext.dispatch({ type: 'CONTENT_CHANGED', content, data, source: source || 'user' })
  }, [documentContext])

  // Version operations
  const createVersion = useCallback(async (changeSummary?: string) => {
    logger.info('useEditor', 'createVersion', 'Creating manual version')
    return await versioningContext.createVersion(changeSummary)
  }, [versioningContext])

  const restoreVersion = useCallback(async (versionNumber: number) => {
    logger.info('useEditor', 'restoreVersion', `Restoring version ${versionNumber}`)
    await versioningContext.restoreVersion(versionNumber)
  }, [versioningContext])

  // Combined operations
  const initializeDocument = useCallback(async (reportId: string) => {
    logger.info('useEditor', 'initializeDocument', `Initializing document ${reportId}`)
    
    try {
      // Load document content
      await loadDocument(reportId)
      
      // Load version history
      await versioningContext.listVersions()
      
      // Mark as initialized  
      documentContext.dispatch({ type: 'DOCUMENT_INITIALIZED' })
      
      logger.info('useEditor', 'initializeDocument', `Document ${reportId} initialized successfully`)
    } catch (error) {
      logger.error('useEditor', 'initializeDocument', `Failed to initialize document ${reportId}`, error as Error)
      throw error
    }
  }, [loadDocument, versioningContext, documentContext])

  const resetEditor = useCallback(() => {
    logger.info('useEditor', 'resetEditor', 'Resetting editor state')
    
    // Reset document - dispatch reset actions
    documentContext.dispatch({ type: 'RESET_DIRTY_STATE' })
    
    // Reset versioning state would need to be added to VersioningContext
    // For now, this will reset when documentId changes
  }, [documentContext])

  // State getters
  const getEditorState = useCallback(() => {
    return {
      // Document state
      editor: documentContext.state.editor,
      content: documentContext.state.content,
      data: documentContext.state.data,
      reportId: 'unknown', // Not available in new context
      status: documentContext.state.isSaving ? 'saving' : 'idle',
      hasUnsavedChanges: documentContext.state.isDirty,
      lastSaved: documentContext.state.lastSaved,
      error: documentContext.state.saveError,
      
      // Component state
      components: documentContext.state.components,
      totalComponents: documentContext.state.components.size,
      loadedComponents: getComponentsByStatus('loaded').length,
      areAllComponentsLoaded: documentContext.areAllComponentsLoaded(),
      
      // Version state
      versions: versioningContext.versions,
      currentVersion: versioningContext.currentVersion,
      autoSaveEnabled: versioningContext.autoSaveEnabled,
      lastAutoSave: versioningContext.lastAutoSave,
      
      // UI state
      showSidePanel: documentContext.state.showSidePanel,
      activePanelTab: documentContext.state.activePanelTab,
      isInitialized: documentContext.state.isInitialized
    }
  }, [documentContext, versioningContext, getComponentsByStatus])

  const getComponentState = useCallback((componentId: string) => {
    const component = getComponent(componentId)
    if (!component) return null

    return {
      component,
      validTransitions: [], // Simplified for now
      isFinalState: ['loaded', 'error', 'preserved', 'locked'].includes(component.status),
      isReadyState: ['loaded', 'preserved', 'locked'].includes(component.status),
      children: component.children || [],
      hasChildren: (component.children?.length || 0) > 0
    }
  }, [getComponent])

  return {
    // State
    ...getEditorState(),
    
    // Component operations
    registerComponent,
    updateComponent,
    removeComponent: documentContext.removeComponent,
    getComponent,
    getComponentsByStatus,
    getComponentState,
    
    // Document operations
    loadDocument,
    saveDocument,
    resetEditor,
    setEditor: (editor: any) => documentContext.dispatch({ type: 'EDITOR_CREATED', editor }),
    setContent,
    addCitation: documentContext.addCitation,
    addCitations: documentContext.registerCitations,
    
    // Version operations
    createVersion,
    restoreVersion,
    loadVersion: versioningContext.loadVersion,
    deleteVersion: versioningContext.deleteVersion,
    setAutoSaveEnabled: versioningContext.setAutoSaveEnabled,
    
    // Combined operations
    initializeDocument
  }
}

/**
 * Hook for component-specific operations
 */
export function useEditorComponent(componentId: string) {
  const editor = useEditor()
  const component = editor.getComponent(componentId)
  
  const update = useCallback((updates: Partial<ReportComponent>) => {
    editor.updateComponent(componentId, updates)
  }, [editor, componentId])
  
  const remove = useCallback(() => {
    editor.removeComponent(componentId)
  }, [editor, componentId])
  
  const setState = useCallback((status: ComponentStatus) => {
    editor.updateComponent(componentId, { status })
  }, [editor, componentId])
  
  return {
    component,
    update,
    remove,
    setState,
    state: editor.getComponentState(componentId)
  }
}