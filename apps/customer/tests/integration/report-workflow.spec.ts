import { test, expect } from '@playwright/test';
import { TestUtils } from '../helpers/tests/test-utils';

test.describe('Report Workflow Integration', () => {
  let testUtils: TestUtils;

  test.beforeEach(async ({ page }) => {
    // Add console monitoring as requested
    page.on('console', msg => {
      console.log(`Browser console: ${msg.type()}: ${msg.text()}`);
    });
    
    testUtils = new TestUtils(page);
    await testUtils.login();
  });

  test('complete report creation and editing workflow', async ({ page }) => {
    // Set longer timeout for this complex test
    test.setTimeout(180000);
    console.log('Creating ESG Report document...');
    await testUtils.createDocumentFromTemplate('ESG Report');
    
    console.log('Waiting for report sections to load...');
    await expect(page.locator('[data-type="reportSection"]').first()).toBeVisible({ timeout: 45000 });
    
    // Verify template components were created
    console.log('Verifying template components...');
    expect(await testUtils.countComponents('report-summary')).toBeGreaterThan(0);
    expect(await testUtils.countComponents('report-group')).toBeGreaterThan(0);
    expect(await testUtils.countComponents('report-section')).toBeGreaterThan(0);
    
    // Wait for existing components to finish loading
    console.log('Waiting for components to finish loading...');
    await testUtils.waitForComponentLoading('[data-type="reportSection"]');
    
    // Add custom text at the beginning of the document
    console.log('Adding custom introduction text...');
    await page.click('.ProseMirror');
    await page.keyboard.press('Control+Home'); // More reliable than Home
    await page.waitForTimeout(500); // Let cursor position update
    await testUtils.typeInEditor('This is a custom introduction to the ESG report.');
    
    // Position cursor at the end to add new component
    console.log('Positioning cursor for new component...');
    await page.keyboard.press('Control+End'); // More reliable than End
    await page.keyboard.press('Enter');
    await page.waitForTimeout(500); // Let editor update
    
    console.log('Adding custom report section...');
    await testUtils.addReportComponent('section', {
      id: 'custom-section',
      title: 'Custom Analysis',
      prompt: 'Provide analysis on custom metrics'
    });
    
    console.log('Waiting for custom section to be visible...');
    await expect(page.locator('[data-id="custom-section"]')).toBeVisible({ timeout: 30000 });
    
    console.log('Opening component configuration...');
    await testUtils.openComponentConfig('[data-id="custom-section"]');
    
    console.log('Updating component configuration...');
    await testUtils.fillComponentConfig({
      prompt: 'Updated prompt for better analysis'
    });
    await testUtils.confirmComponentConfig();
    
    console.log('Testing component actions...');
    // Test refresh action
    await testUtils.performComponentAction('refresh', '[data-id="custom-section"]');
    await testUtils.waitForComponentLoading('[data-id="custom-section"]');
    
    // Test lock action
    await testUtils.performComponentAction('lock', '[data-id="custom-section"]');
    await testUtils.checkComponentState('locked', '[data-id="custom-section"]');
    
    // Verify component is still present and functional
    console.log('Verifying component state...');
    await expect(page.locator('[data-id="custom-section"]')).toBeVisible({ timeout: 10000 });
    await testUtils.checkComponentExists('custom-section');
    
    console.log('Waiting for auto-save to complete...');
    await testUtils.waitForAutoSave();
    
    console.log('Checking for any errors...');
    await testUtils.checkForErrors();
    
    console.log('Report workflow test completed successfully');
  });

  test('report summary dependency workflow', async ({ page }) => {
    // Set longer timeout for this complex test
    test.setTimeout(180000);
    console.log('Creating ESG Report document for summary dependency test...');
    await testUtils.createDocumentFromTemplate('ESG Report');
    
    console.log('Waiting for report sections to load...');
    await testUtils.waitForComponentLoading('[data-type="reportSection"]');
    
    // Wait for all ESG Report components to load completely
    console.log('Waiting for all template components to finish loading...');
    await page.waitForTimeout(5000); // Give components time to render
    
    // Verify that we have components that can be summarized
    const componentCount = await page.locator('[data-type="reportSection"], [data-type="reportGroup"]').count();
    console.log(`Found ${componentCount} components that can be summarized`);
    
    // Ensure we have at least one component before proceeding
    if (componentCount === 0) {
      throw new Error('No report components found to summarize. ESG Report template may not have loaded properly.');
    }
    
    // Wait for any loading indicators to disappear
    await page.waitForFunction(() => {
      const loadingElements = document.querySelectorAll('.loading, [data-testid="loading"]');
      return loadingElements.length === 0;
    }, { timeout: 30000 }).catch(() => {
      console.log('Some loading indicators may still be present, continuing...');
    });
    
    // Position cursor at the end of the document before adding summary
    console.log('Positioning cursor for new summary component...');
    await page.click('.ProseMirror');
    await page.keyboard.press('Control+End'); // More reliable cursor positioning
    await page.keyboard.press('Enter');
    await page.waitForTimeout(500); // Let editor update
    
    console.log('Adding custom summary component...');
    await testUtils.addReportComponent('summary', {
      id: 'custom-summary',
      title: 'Custom Summary',
      prompt: 'Summarize the key findings'
    });
    
    console.log('Waiting for summary component to be visible...');
    await expect(page.locator('[data-id="custom-summary"]')).toBeVisible({ timeout: 30000 });
    
    console.log('Opening summary component configuration...');
    await testUtils.openComponentConfig('[data-id="custom-summary"]');
    
    console.log('Waiting for configuration dialog...');
    await expect(page.locator('[role="dialog"]')).toBeVisible({ timeout: 15000 });
    
    // Wait for dialog to fully render
    await page.waitForTimeout(2000);
    
    console.log('Looking for component dependency section...');
    
    // First check if the dialog is for a summary component by looking for summary-specific fields
    const isSummaryDialog = await page.locator('text="Additional Prompt"').count() > 0 || await page.locator('text="Components to Summarize"').count() > 0;
    console.log('Is summary dialog:', isSummaryDialog);
    
    // Debug: log the dialog content to understand what's showing
    const dialogContent = await page.locator('[role="dialog"]').textContent();
    console.log('Dialog content preview:', dialogContent?.substring(0, 300));
    
    // Check if the dialog has fields for configuring the summary component
    // Look for Additional Prompt field which should exist for summary components
    const additionalPromptField = page.locator('text="Additional Prompt"');
    if (await additionalPromptField.count() > 0) {
      console.log('Found Additional Prompt field - configuring summary component');
      await expect(additionalPromptField).toBeVisible({ timeout: 5000 });
      
      // Look for the textbox associated with Additional Prompt
      const promptTextbox = page.locator('textbox[placeholder*="Additional Prompt"], textbox:below(text="Additional Prompt")').first();
      if (await promptTextbox.count() > 0) {
        console.log('Found Additional Prompt textbox');
        await promptTextbox.scrollIntoViewIfNeeded();
        await promptTextbox.fill('Provide a comprehensive summary of all report sections');
      }
    } else {
      console.log('Additional Prompt field not found, working with available configuration options');
    }
    
    // Check for other configuration options that might be available
    const titleField = page.locator('textbox[placeholder*="Title"], textbox:below(text="Title")').first();
    if (await titleField.count() > 0) {
      console.log('Found Title field');
      await titleField.scrollIntoViewIfNeeded();
      await titleField.clear();
      await titleField.fill('Custom Summary');
    }
    
    console.log('Confirming component configuration...');
    await testUtils.confirmComponentConfig();
    
    console.log('Waiting for summary component to finish loading...');
    await testUtils.waitForComponentLoading('[data-type="reportSummary"][data-id="custom-summary"]');
    
    console.log('Verifying summary component exists...');
    await testUtils.checkComponentExists('custom-summary');
    
    console.log('Summary dependency workflow test completed successfully');
  });

  test('error handling and recovery', async ({ page }) => {
    await testUtils.mockApiError('/api/report/entity');

    await testUtils.createDocumentFromTemplate('ESG Report');
    
    // Wait for report sections to be created and attempt to load
    await expect(page.locator('.report-section').first()).toBeVisible({ timeout: 10000 });
    
    // Look for loading states or empty content which indicates API error handling
    // The app may not show explicit error messages but handle errors gracefully
    await page.waitForTimeout(2000);
    
    await page.unroute('**/api/report/entity**');
    
    // Test recovery by refreshing a component after unrouting the error
    await testUtils.performComponentAction('refresh', '.report-section');
    
    await testUtils.waitForComponentLoading('[data-type="reportSection"]');
  });

  test('collaborative editing simulation', async ({ page, context }) => {
    const documentId = await testUtils.createDocumentFromTemplate('ESG Report');
    
    const page2 = await context.newPage();
    const testUtils2 = new TestUtils(page2);
    await testUtils2.login(); // Use default credentials
    await page2.goto(`/customer/documents/${documentId}`);
    await testUtils2.waitForEditor();
    
    // Click at the beginning of the editor to position cursor properly
    await page.click('.ProseMirror');
    await page.keyboard.press('Home');
    await testUtils.typeInEditor('User 1 content');
    await testUtils.waitForAutoSave();
    
    // Reload page2 to get the updated content (simulating real-time sync)
    await page2.reload();
    await testUtils2.waitForEditor();
    
    // Check if User 1 content is visible on page2
    await expect(page2.locator('.ProseMirror')).toContainText('User 1 content', { timeout: 10000 });
    
    // Position cursor at the end and add more content
    await page2.click('.ProseMirror');
    await page2.keyboard.press('End');
    await testUtils2.typeInEditor(' and User 2 content');
    await testUtils2.waitForAutoSave();
    
    // Reload page1 to get the updated content
    await page.reload();
    await testUtils.waitForEditor();
    
    // Check if both contents are visible on page1
    await expect(page.locator('.ProseMirror')).toContainText('User 1 content and User 2 content', { timeout: 10000 });
    
    await page2.close();
  });

  test('performance with large documents', async ({ page }) => {
    await testUtils.createDocumentFromTemplate('ESG Report');
    
    for (let i = 0; i < 5; i++) {
      // Move cursor to end of editor before adding each component
      await page.click('.ProseMirror');
      await page.keyboard.press('End');
      await page.keyboard.press('Enter');
      
      await testUtils.addReportComponent('section', {
        id: `perf-section-${i}`,
        title: `Performance Section ${i}`,
        prompt: `Analysis for section ${i}`
      });
      
      // Wait for each component to be rendered and move cursor after it
      await page.waitForTimeout(2000);
      
      // Verify this component was created successfully
      await expect(page.locator(`[data-id="perf-section-${i}"]`)).toBeVisible({ timeout: 10000 });
    }
    
    // Verify all components are visible
    for (let i = 0; i < 5; i++) {
      await expect(page.locator(`[data-id="perf-section-${i}"]`)).toBeVisible({ timeout: 10000 });
    }
    
    await testUtils.typeInEditor('Performance test content');
    await expect(page.locator('text=Performance test content')).toBeVisible();
    
    const startTime = Date.now();
    await testUtils.waitForComponentLoading('[data-type="reportSection"]');
    const loadTime = Date.now() - startTime;
    
    expect(loadTime).toBeLessThan(30000);
  });

  test('accessibility compliance', async ({ page }) => {
    await testUtils.createDocumentFromTemplate('ESG Report');
    
    const buttonCount = await page.locator('button').count();
    expect(buttonCount).toBeGreaterThan(0);
    await expect(page.locator('[role="dialog"]')).toHaveCount(0);
    
    await testUtils.openComponentConfig('.report-section');
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    await expect(page.locator('button:has-text("Close")')).toBeVisible();
    
    await page.keyboard.press('Tab');
    await page.keyboard.press('Escape');
    await expect(page.locator('[role="dialog"]')).not.toBeVisible();
  });

  test('mobile responsiveness', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });

    await testUtils.createDocumentFromTemplate('ESG Report');
    
    // Use flexible selectors for mobile test
    await expect(page.locator('[data-type="reportSection"], [data-type="reportGroup"]').first()).toBeVisible();
    
    const reportSection = page.locator('[data-type="reportSection"], [data-type="reportGroup"]').first();
    const menuTrigger = reportSection.locator('button[data-testid*="menu-trigger"], button:has(svg)').first();
    await menuTrigger.click();
    await expect(page.locator('text=Configure')).toBeVisible();
  });

  test('data persistence across sessions', async ({ page, context }) => {
    const documentId = await testUtils.createDocumentFromTemplate('ESG Report');
    
    // Click at the beginning of the editor to position cursor properly
    await page.click('.ProseMirror');
    await page.keyboard.press('Home');
    await testUtils.typeInEditor('Persistence test content');
    await testUtils.waitForAutoSave();
    
    await page.close();
    const newPage = await context.newPage();
    const newTestUtils = new TestUtils(newPage);
    await newTestUtils.login();
    
    await newPage.goto(`/customer/documents/${documentId}`);
    await newTestUtils.waitForEditor();
    
    await expect(newPage.locator('.ProseMirror')).toContainText('Persistence test content', { timeout: 10000 });
  });
});