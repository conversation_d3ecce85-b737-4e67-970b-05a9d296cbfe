import { test, expect } from '@playwright/test';
import { login } from './helpers/auth';
import { TestUtils } from '@/tests/helpers/tests/test-utils'

// Simple test to verify component creation and configuration works
test.describe('minimal component test', async () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
    page.on('console', msg => {
      console.log(`[Console] ${msg.type()}: ${msg.text()}`);
    });
    
    // Capture page errors
    page.on('pageerror', error => {
      console.log(`[PageError] ${error.message}`);
      console.log(`[PageError] Stack: ${error.stack}`);
    });
  })

  test('Choose a Document and wait for it to load successfully', async ({ page }) => {
    console.log('🚀 Starting document creation test...')
    
    // Create a new document from template using TestUtils
    console.log('📄 Creating document from Task List template...')
    const documentId = await testUtils.createDocumentFromTemplate('Blank Document')
    console.log('✅ Document created successfully with ID:', documentId)

    // Wait for editor to be fully loaded
    console.log('⏳ Waiting for editor to load...')
    await testUtils.waitForEditor()
    console.log('✅ Editor loaded successfully')

    // Wait for initial loading to complete
    console.log('⏳ Waiting for initial loading to complete...')
    await page.waitForTimeout(5000)
    console.log('✅ Initial loading wait completed')

    // Verify the document loaded successfully
    console.log('🔍 Checking if ProseMirror editor is visible...')
    await expect(page.locator('.ProseMirror')).toBeVisible({ timeout: 100 })
    console.log('✅ ProseMirror editor is visible - test passed!')

  })

});
