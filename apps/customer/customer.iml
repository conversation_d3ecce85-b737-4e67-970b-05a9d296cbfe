<?xml version="1.0" encoding="UTF-8"?>
<module type="WEB_MODULE" version="4">
  <component name="NewModuleRootManager" inherit-compiler-output="true">
    <exclude-output />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/tests" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/unit-tests" isTestSource="true" />
    </content>
    <content url="file://$MODULE_DIR$/app" />
    <content url="file://$MODULE_DIR$/components" />
    <content url="file://$MODULE_DIR$/public" />
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
  <component name="PackageRequirementsSettings">
    <option name="requirementsPath" value="" />
  </component>
  <component name="TestRunnerService">
    <option name="PROJECT_TEST_RUNNER" value="py.test" />
  </component>
</module>