import { Database } from '@/database.types'
import { V2EntityType } from './types/entity'
import { V2RunType } from '@/types/run'
import { PromiseTypeV2 as ImportedPromiseTypeV2 } from './types/promise'
import { CherryTypeV2 as ImportedCherryTypeV2 } from './types/cherry'
import { VagueTypeV2 as ImportedVagueTypeV2 } from './types/vague'
import { ScoreTypeV2 as ImportedScoreTypeV2 } from './types/score'
import { ModelSectionTypeV2 as ImportedModelSectionTypeV2 } from './types/model-section'
import { CitationType } from '@/components/citation'

// VagueType is now using the V2 version
export type VagueType = VagueTypeV2;
export type VagueTypeV2 = ImportedVagueTypeV2;
export type ScoreTypeV2 = ImportedScoreTypeV2;
export type EntityTypeV2 = V2EntityType;
export type FlagTypeV2Base = Database['public']['Tables']['xfer_flags']['Row'];
// IssueType is deprecated as model sections are now directly attached to each flag
export type PromiseTypeV2 = ImportedPromiseTypeV2;
export type CherryTypeV2 = ImportedCherryTypeV2;
// Using imported type instead of direct database reference
export type ModelSectionType = ModelSectionTypeV2;
export type ModelSectionTypeV2 = ImportedModelSectionTypeV2;
// Extended RunType to include all properties used in entity-analysis-report.tsx
export interface RunType {
  id: number;
  completed_at: string | null;
  model?: any;
  run_type: string;
  scope: string;
  target?: string | null;
  end_year?: number | null;
  models?: string[] | null;
  start_year?: number | null;
}
export type RunTypeV2 = V2RunType;
export type ProfileType = Database["public"]["Tables"]["profiles"]["Row"];
export type SingleDocAnalysesType = Database["public"]["Views"]["view_single_doc_runs"]["Row"];
export type EntityAnalysesRunType = Database["public"]["Views"]["view_entity_analysis_runs"]["Row"];
export type MyCompaniesType = Database["public"]["Views"]["view_my_companies"]["Row"];
export type QuotaUsedType = Database["public"]["Views"]["view_quota_used"]["Row"];
export type APIQueueType = Database["public"]["Tables"]["api_queue"]["Row"];
// Note: view_api_queue_expanded doesn't exist in current database schema
// export type APIQueueExpandedType = Database["public"]["Views"]["view_api_queue_expanded"]["Row"];
export type AccountMessageType = Database["public"]["Tables"]["acc_messages"]["Row"];
export type AccountMessageEnumType= Database["public"]["Enums"]["acc_message_type"]


// DEMISE model structure from Python's DEMISEModel
export type DEMISEModel = {
  demise_embedding?: number[];
  domain_embedding?: number[];
  effect_embedding?: number[];
};

// Statement metadata structure
export type StatementAndMetadata = {
  statement_text: string;
  source_text?: string;  // Added source_text field
  metadata: {
    statement_category: string;
    // Other metadata fields
  };
};

// Impact measurement types from Python backend
export type TemporalBreakdown = {
  immediate: string;
  medium_term: string;
  long_term: string;
};

export type ScaleFactors = {
  proximity_to_tipping_point: string; // Proximity to tipping points, an assessment

  // Reversibility questions (7 questions covering 0-100 scale)
  is_irreversible: boolean;
  takes_centuries_to_reverse: boolean;
  requires_significant_effort_to_reverse: boolean;
  reversible_within_years: boolean;
  reversible_within_months: boolean;
  reversible_within_weeks: boolean;
  fully_reversible_immediately: boolean;

  // Scale of impact questions (7 questions covering individual to global scale)
  affects_single_individual: boolean;
  affects_multiple_individuals: boolean;
  affects_many_beings: boolean;
  affects_large_population: boolean;
  affects_country_ecosystem: boolean;
  affects_species_biome: boolean;
  affects_all_global: boolean;

  // Directness questions (7 questions covering third party to direct action)
  third_party_action: boolean;
  entity_had_minor_influence: boolean;
  entity_influenced_outcome: boolean;
  entity_action_led_to_impact: boolean;
  entity_decision_caused_impact: boolean;
  direct_action_by_entity: boolean;
  entity_sole_direct_cause: boolean;

  // Authenticity questions (7 questions covering greenwashing to genuine commitment)
  pure_marketing_greenwashing: boolean;
  mostly_regulatory_compliance: boolean;
  primarily_business_driven: boolean;
  balanced_genuine_business: boolean;
  mostly_genuine_some_business: boolean;
  genuine_commitment: boolean;
  purely_altruistic: boolean;

  // Deliberateness questions (7 questions covering accidental to fully intentional)
  completely_accidental: boolean;
  foreseeable_not_intended: boolean;
  knew_consequences_acted_anyway: boolean;
  intended_action_predictable_outcome: boolean;
  planned_with_awareness: boolean;
  fully_intentional: boolean;
  deliberately_planned_for_impact: boolean;

  // Contribution questions (7 questions covering no contribution to sole contributor)
  not_contributing: boolean;
  very_minor_contributor: boolean;
  minor_contributor: boolean;
  significant_contributor: boolean;
  major_contributor: boolean;
  dominant_contributor: boolean;
  sole_contributor: boolean;

  // Duration (keeping as is)
  duration: 'short' | 'medium' | 'long';

  // Scope questions (7 questions covering individual to global impact)
  individual_impact: boolean;
  local_city_impact: boolean;
  regional_impact: boolean;
  national_impact: boolean;
  continental_impact: boolean;
  global_impact: boolean;
  universal_impact: boolean;

  // Degree questions (7 questions covering no harm to total destruction)
  no_harm: boolean;
  minor_harm: boolean;
  moderate_harm: boolean;
  severe_harm: boolean;
  severe_permanent_damage: boolean;
  near_total_destruction: boolean;
  total_destruction_death: boolean;

  // Probability questions (7 questions covering will not happen to already happened)
  will_not_happen: boolean;
  very_unlikely: boolean;
  unlikely: boolean;
  even_chance: boolean;
  highly_likely: boolean;
  virtually_certain: boolean;
  has_already_happened: boolean;
};

export type DimensionAssessment = {
  score: number; // 0.0 to 1.0
  reasoning: string;
  confidence: 'high' | 'medium' | 'low';
  temporal_breakdown: TemporalBreakdown;
  scale_factors: ScaleFactors;
};

// Individual impact assessment types with boolean indicators
export type AnimalHarmImpact = {
  assessment: DimensionAssessment;
  // Boolean impact indicators (to be populated from backend structure)
  animal_suffering_increased?: boolean;
  habitat_loss_or_degradation?: boolean;
  wildlife_population_decreased?: boolean;
  // ... additional indicators from backend
};

export type AnimalBenefitImpact = {
  assessment: DimensionAssessment;
  // Boolean benefit indicators
  animal_welfare_improved?: boolean;
  habitat_restored_or_protected?: boolean;
  wildlife_population_increased?: boolean;
  // ... additional indicators from backend
};

export type HumanHarmImpact = {
  assessment: DimensionAssessment;
  // Boolean harm indicators
  health_deteriorated?: boolean;
  economic_harm_caused?: boolean;
  human_rights_violated?: boolean;
  // ... additional indicators from backend
};

export type HumanBenefitImpact = {
  assessment: DimensionAssessment;
  // Boolean benefit indicators
  health_improved?: boolean;
  economic_benefits_provided?: boolean;
  human_rights_protected?: boolean;
  // ... additional indicators from backend
};

export type EnvironmentHarmImpact = {
  assessment: DimensionAssessment;
  // Boolean harm indicators
  pollution_increased?: boolean;
  ecosystem_damaged?: boolean;
  climate_risk_intensified?: boolean;
  // ... additional indicators from backend
};

export type EnvironmentBenefitImpact = {
  assessment: DimensionAssessment;
  // Boolean benefit indicators
  pollution_reduced?: boolean;
  ecosystem_restored?: boolean;
  renewable_energy_generated?: boolean;
  // ... additional indicators from backend
};

// New assessment structure with separate harm and benefit assessments
export type HarmImpactAssessment = {
  animals: AnimalHarmImpact;
  humans: HumanHarmImpact;
  environment: EnvironmentHarmImpact;
};

export type BenefitImpactAssessment = {
  animals: AnimalBenefitImpact;
  humans: HumanBenefitImpact;
  environment: EnvironmentBenefitImpact;
};

// Legacy type for backward compatibility - will be removed after migration
export type ImpactAssessment = {
  animals: DimensionAssessment;
  humans: DimensionAssessment;
  environment: DimensionAssessment;
};

// Scale factor adjustment step
export type ScaleFactorAdjustment = {
  factor_name: string;
  description: string;
  multiplier: number;
  score_before: number;
  score_after: number;
  triggered_criteria?: string[]; // Which boolean criteria triggered this adjustment
};

// Backend calculation details structure (from calculator.py)
export type BackendCalculationDetails = {
  triggered_indicators: string[]; // Array of indicator names
  base_score: number; // Initial score before any adjustments
  base_score_rationale: string; // Which indicator triggered this base score
  scale_factor_adjustments: ScaleFactorAdjustment[]; // Step-by-step adjustments
  pre_validation_score: number;
  final_score: number; // Score after all adjustments
  confidence_factors: string[];
  scale_factors: ScaleFactors;
  probability?: number;
};

// Validation details from backend (from validation.py)
export type ValidationDetails = {
  original_score: number;
  max_allowed_score: number;
  capped_score: number;
  was_capped: boolean;
  applied_limits: Array<[string, number]>; // [indicator_name, limit_value]
  confidence_adjusted: boolean;
};

// Complete calculation details structure matching backend
export type CalculationDetails = {
  harm: {
    animals?: BackendCalculationDetails;
    humans?: BackendCalculationDetails;
    environment?: BackendCalculationDetails;
  };
  benefit: {
    animals?: BackendCalculationDetails;
    humans?: BackendCalculationDetails;
    environment?: BackendCalculationDetails;
  };
  validation: {
    harm: {
      animals?: ValidationDetails;
      humans?: ValidationDetails;
      environment?: ValidationDetails;
    };
    benefit: {
      animals?: ValidationDetails;
      humans?: ValidationDetails;
      environment?: ValidationDetails;
    };
  };
};

export type AssessmentFeedback = {
  dimension: 'animals' | 'humans' | 'environment';
  assessment_type: 'harm' | 'benefit';
  component: 'reasoning' | 'temporal_breakdown' | 'scale_factors' | 'general';
  issue: string;
  suggestion: string;
};

export type ReviewDecision = {
  decision: 'accept' | 'amend' | 'redo';
  reasoning: string;
  bias_detected: boolean;
  bias_types: string[];
  accuracy_issues: string[];
  assessment_feedback: AssessmentFeedback[];
  general_comments?: string;
  confidence_in_review: 'high' | 'medium' | 'low';
};

export type EventImpactMeasurement = {
  id?: number;
  run_id?: number;
  event_id?: string;
  event_summary: string;
  event_description: string;
  harm_assessment: HarmImpactAssessment;
  benefit_assessment: BenefitImpactAssessment;
  key_uncertainties: string[];
  ethical_considerations: string[];
  assessed_at: string;
  model_used: string;
  prompt_version: string;
  harm_score: number; // 0.0 to 1.0
  benefit_score: number; // 0.0 to 1.0
  net_impact_score: number; // -1.0 to 1.0
  calculation_details?: CalculationDetails; // Now matches backend structure
  reviewer_feedback?: AssessmentFeedback[];
  review_decision?: ReviewDecision;
};

export type ConsistencyChecks = {
  score_alignment: boolean;
  temporal_consistency: boolean;
  dimensional_balance: boolean;
  scale_factors_alignment: boolean;
  scale_factors_internal_consistency: boolean;
  issues: string[];
};

export type ConfidenceAnalysis = {
  confidence_distribution: Record<string, number>;
  avg_confidence: number;
  low_confidence_high_impact: string[];
};

export type BiasDetection = {
  anthropocentric_bias: boolean;
  optimism_bias: boolean;
  recency_bias: boolean;
  contribution_bias: boolean;
  authenticity_bias: boolean;
  directness_bias: boolean;
  detected_issues: string[];
};

export type ImpactValueAnalysis = {
  impact_measurement?: EventImpactMeasurement;
};

// Structure that matches the XferEffectFlagModel Python class
export interface FlagModelData {
  id: number;
  entity_xid: string;
  flag_type: string;
  flag_title: string;
  flag_short_title: string;
  year: number;
  start_year?: number;
  end_year?: number;
  score: number;
  impact: number;
  confidence: number;
  credibility: number;
  flag_summary?: string;
  flag_analysis?: string;
  domains: string[];
  citations: CitationType[];
  flag_statements: StatementAndMetadata[];
  impact_value_analysis: ImpactValueAnalysis;
  impact_measurement?: EventImpactMeasurement; // Full impact measurement object
  model_sections: Record<string, string>; // Map of model name to section ID
  full_demise_centroid: DEMISEModel;
  is_disclosure_only: boolean; // Flag indicating if all source documents are disclosures
}

// Complete FlagTypeV2 with model data
// @ts-ignore
export interface FlagTypeV2 extends FlagTypeV2Base {
  model: FlagModelData;
  // Add issue field which is in the table but not in the model
  issue?: string;
}
